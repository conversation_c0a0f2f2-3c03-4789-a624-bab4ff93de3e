import { forward<PERSON><PERSON>, <PERSON>dule } from "@nestjs/common";
import { OrganizationController } from "./controllers/organization.controller";
import { OrganizationService } from "./services/organization.service";
import { AuthModule } from "src/auth/auth.module";
import { UtilsModule } from "src/utils/utils.module";
import { MailModule } from "src/mail/mail.module";
import { MongooseModule } from "@nestjs/mongoose";
import { User, UserSchema } from "src/users/schemas/user.schema";
import { Organizations, OrganizationSchema } from "./schemas/organization.schema";
import { OrganizationPipe } from "./pipes/organization.pipe";
import { Otp, OtpSchema } from "src/auth/schemas/otp.schema";
import { Services, ServiceSchema } from "./schemas/services.schema";
import { StaffProfileDetails, StaffSchema } from "src/staff/schemas/staff.schema";
import { PricingController } from "./controllers/pricing.controller";
import { PricingService } from "./services/pricing.service";
import { Pricing, PricingSchema } from "./schemas/pricing.schema";
import { ServiceCategoryPricing, ServiceCategoryPricingSchema } from "./schemas/service-category-pricing.schema";
import { Purchase, PurchaseSchema } from "src/users/schemas/purchased-packages.schema";
import { Attributes, AttributeSchema } from "src/attributes/schema/attribute.schema";
import { Clients, ClientSchema } from "src/users/schemas/clients.schema";
import { PayRateSchema, PayRate } from "src/staff/schemas/pay-rate.schema";
import { DATABASE_PRIMARY_CONNECTION_NAME } from "src/common/database/constants/database.constant";
import { Invoice, InvoiceSchema } from "src/users/schemas/invoice.schema";
import { RoleModule } from "src/role/role.module";
import { PromotionsModule } from "src/promotions/promotions.module";
import { Scheduling, SchedulingSchema } from "src/scheduling/schemas/scheduling.schema";
import { VoucherController } from "./controllers/voucher.controller";
import { VoucherService } from "./services/voucher.service";
import { VoucherPublicController } from "./controllers/voucher.public.controller";
import { VoucherRedemption, VoucherRedemptionSchema } from "src/users/schemas/voucher-redemption.schema";

@Module({
    imports: [
        forwardRef(() => AuthModule),
        UtilsModule,
        MailModule,
        RoleModule,
        PromotionsModule,
        MongooseModule.forFeature([
            { name: User.name, schema: UserSchema },
            { name: Organizations.name, schema: OrganizationSchema },
            { name: Attributes.name, schema: AttributeSchema },
            { name: Otp.name, schema: OtpSchema },
            { name: Services.name, schema: ServiceSchema },
            { name: Pricing.name, schema: PricingSchema },
            { name: ServiceCategoryPricing.name, schema: ServiceCategoryPricingSchema },
            { name: StaffProfileDetails.name, schema: StaffSchema },
            { name: Purchase.name, schema: PurchaseSchema },
            { name: Clients.name, schema: ClientSchema },
            { name: PayRate.name, schema: PayRateSchema },
            { name: Invoice.name, schema: InvoiceSchema },
            { name: Scheduling.name, schema: SchedulingSchema },
            { name: VoucherRedemption.name, schema: VoucherRedemptionSchema }
        ], DATABASE_PRIMARY_CONNECTION_NAME),
    ],
    controllers: [OrganizationController, PricingController, VoucherController, VoucherPublicController],
    providers: [OrganizationService, OrganizationPipe, PricingService, VoucherService],
    exports: [OrganizationService, PricingService, VoucherService],
})
export class OrganizationModule { }
