import { Injectable, BadRequestException, NotFoundException } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import { Attributes } from "src/utils/enums/attribute.enum";
import { ProductVariant } from "src/merchandise/schema/product-variant.schema";
import { Product, ProductSchema, ProductType } from "src/merchandise/schema/product.schema";
import { Types } from "mongoose";
import { AttributeList, VariantAttributes } from "src/merchandise/constant/attributeList.constant";
import { ProductAttributeType } from "src/merchandise/schema/product-attribute.schema";
import { AttributeType, AttributeValue, AttributeValueDocument } from "src/merchandise/schema/attribute.schema";
import { Category, CategoryDocument, CategoryLevel } from "src/merchandise/schema/category.schema";
import { TransactionService } from "src/utils/services/transaction.service";
import { ConfigService } from "@nestjs/config";
import { CreateProductDto } from "../dto/createProduct.dto";
import { FilterProductDto } from "../dto/filterProduct.dto";
import { UpdateProductStatusDto } from "../dto/productupdateStatus.dto";
import { Inventory } from "../schema/inventory.schema";
import { ProductVariantAttributeDocument, ProductVariantAttribute } from "../schema/product-attribute-list.schema";
import { create } from "domain";
import { StaffProfileDetails } from "src/staff/schemas/staff.schema";
import { IUserDocument } from "src/users/interfaces/user.interface";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { BrandProductListingDto } from "../dto/brand-product-listing.dto";
import { UploadBatch, UploadBatchDocument } from 'src/merchandise/schema/upload-batch.schema';
import { Organizations } from 'src/organization/schemas/organization.schema';
import { parse as parseCsv } from 'csv-parse/sync';
@Injectable()
export class ProductService {
    constructor(
        @InjectModel(Product.name) private productModel: Model<Product>,
        @InjectModel(AttributeValue.name) private AttributeValueModel: Model<AttributeValue>,
        @InjectModel(Category.name) private categoryModel: Model<Category>,
        @InjectModel(ProductVariant.name) private productVariantModel: Model<ProductVariant>,
        @InjectModel(Inventory.name) private inventoryModel: Model<Inventory>,
        @InjectModel(ProductVariantAttribute.name) private productVariantAttributeModel: Model<ProductVariantAttribute>,
        @InjectModel(StaffProfileDetails.name) private StaffProfileModel: Model<StaffProfileDetails>,
        @InjectModel(UploadBatch.name) private readonly uploadBatchModel: Model<UploadBatchDocument>,
        @InjectModel(Organizations.name) private readonly organizationModel: Model<Organizations>,


        private transactionService: TransactionService,
        private readonly configService: ConfigService,
    ) { }

    async getOrganizationId(user: IUserDocument) {
        const roleType = user.role.type;
        if (!user._id) {
            throw new BadRequestException("User not found");
        }
        if (!user.role) {
            throw new BadRequestException("User not found");
        }
        if (user.role.type === ENUM_ROLE_TYPE.WEB_MASTER || user.role.type === ENUM_ROLE_TYPE.FRONT_DESK_ADMIN || user.role.type === ENUM_ROLE_TYPE.TRAINER) {
            const staffDetails = await this.StaffProfileModel.findOne({ userId: user._id }, { organizationId: 1 });
            if (!staffDetails) throw new BadRequestException("Staff not found");
            return staffDetails.organizationId;
        }
        if (user.role.type === ENUM_ROLE_TYPE.ORGANIZATION) {
            return user._id;
        }
        return user._id;
    }

    async createProduct(createProductDto: CreateProductDto, user: any) {
        const session = await this.transactionService.startTransaction();
        const organizationId = await this.getOrganizationId(user);
        let modifiedProductDetails: {
            attributes: ProductAttributeType[];
            variantIds: Types.ObjectId[];
            variantAttributesList: (typeof VariantAttributes)[];
        } = {
            attributes: [],
            variantIds: [],
            variantAttributesList: [],

        };
        // const isSlugAlreadyExist
        try {
            if (createProductDto.attributes) {
                const attributes = Object.keys(createProductDto.attributes);
                for (const attribute of attributes) {
                    const value = createProductDto.attributes[attribute];
                    let objectId;
                    // Convert value to ObjectId
                    if (Array.isArray(value)) {
                        objectId = value.map(v => Types.ObjectId.createFromHexString(v));
                    } else {
                        if (Types.ObjectId.isValid(value.replace(/\s/g, ""))) {
                            objectId = Types.ObjectId.createFromHexString(value);
                        } else {
                            let addAttribute = new this.AttributeValueModel({
                                attribute: attribute,
                                value: value,
                                slug: this.generateSlug(value),
                            });
                            await addAttribute.save();
                            objectId = addAttribute._id;
                        }
                    }
                    modifiedProductDetails.attributes.push({
                        key: attribute as Attributes,
                        value: objectId,
                    });
                }
            }


            const product = await new this.productModel({
                ...createProductDto,
                ...modifiedProductDetails,
                organizationId: organizationId

            }).save({ session });
            if (createProductDto.type === "variable") {

                for (const variant of createProductDto.variants) {
                    let modifiedVariantDetails: {
                        attributes: ProductAttributeType[];
                    } = {
                        attributes: [],
                    };

                    if (variant.attributes) {
                        const variantAttributes = Object.keys(variant.attributes);
                        for (const attribute of variantAttributes) {
                            let objectId;
                            const value = variant.attributes[attribute];
                            if (Array.isArray(value)) {
                                objectId = value.map(v => Types.ObjectId.createFromHexString(v));
                            } else {
                                if (Types.ObjectId.isValid(value.replace(/\s/g, ""))) {
                                    objectId = Types.ObjectId.createFromHexString(value.trim());
                                } else {
                                    let addAttribute = new this.AttributeValueModel({
                                        attribute: attribute,
                                        value: value,
                                        slug: this.generateSlug(value),
                                    });
                                    await addAttribute.save();
                                    objectId = addAttribute._id;
                                }
                            }
                            modifiedVariantDetails.attributes.push({
                                key: attribute as Attributes,
                                value: objectId,
                            });
                        }
                    }

                    const productVariant = new this.productVariantModel({
                        productId: product._id,
                        ...variant,
                        ...modifiedVariantDetails,
                        organizationId: organizationId
                    });

                    const savedVariant = await productVariant.save({ session });
                    modifiedProductDetails.variantIds.push(savedVariant._id);
                }
            }

            await this.transactionService.commitTransaction(session);
            const success = await this.productModel.findByIdAndUpdate(
                product._id,
                {
                    variantIds: modifiedProductDetails.variantIds,
                    variantAttributesList: createProductDto.variantAttributesList
                },
                { new: true },
            );
            return success;

        }


        catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }

        // return {};
    }
    async getProductsList(filterProductDto: FilterProductDto, user: any) {
        let organizationId = await this.getOrganizationId(user)
        const pageSize = filterProductDto?.pageSize ?? 10;
        const page = filterProductDto?.page ?? 1;
        const skip = pageSize * (page - 1);
        let query = {};

        if (filterProductDto?.search) {
            const titleQueryString = filterProductDto.search.trim().split(" ").join("|");
            query["$or"] = [
                { name: { $regex: `.*${titleQueryString}.*`, $options: "i" } },
                { sku: { $regex: `.*${titleQueryString}.*`, $options: "i" } },
                { "variants.sku": { $regex: `.*${titleQueryString}.*`, $options: "i" } },
            ];
        }

        query["organizationId"] = organizationId


        let data = await this.productModel.aggregate([
            {
                $lookup: {
                    from: "productvariants",
                    localField: "variantIds",
                    foreignField: "_id",
                    as: "variants",
                },
            },
            {
                $lookup: {
                    from: "categories",
                    localField: "firstCategoryId",
                    foreignField: "_id",
                    as: "parentCategory",
                },
            },
            {
                $addFields: {
                    parentCategory: { $arrayElemAt: ["$parentCategory", 0] },
                },
            },
            {
                $addFields: {
                    brandAttrObjId: {
                        $let: {
                            vars: {
                                brandEntry: {
                                    $first: {
                                        $filter: {
                                            input: { $ifNull: ["$attributes", []] },
                                            as: "attr",
                                            cond: { $eq: ["$$attr.key", "brand"] },
                                        },
                                    },
                                },
                            },
                            in: {
                                $cond: [
                                    { $eq: [{ $type: "$$brandEntry.value" }, "objectId"] },
                                    "$$brandEntry.value",
                                    {
                                        $cond: [
                                            { $eq: [{ $type: "$$brandEntry.value" }, "string"] },
                                            { $toObjectId: "$$brandEntry.value" },
                                            null,
                                        ],
                                    },
                                ],
                            },
                        },
                    },
                },
            },

            {
                $lookup: {
                    from: "attributevalues",
                    let: { bId: "$brandAttrObjId" },
                    pipeline: [
                        { $match: { $expr: { $eq: ["$_id", "$$bId"] } } },
                        { $match: { attribute: "brand" } },
                        { $project: { _id: 1, value: 1, slug: 1 } },
                    ],
                    as: "brandDoc",
                },
            },
            { $unwind: { path: "$brandDoc", preserveNullAndEmptyArrays: true } },

            { $addFields: { brandName: "$brandDoc.value" } },
            {
                $match: query,
            },
            {
                $sort: { createdAt: -1 }
            },
            {
                $facet: {
                    products: [
                        {
                            $skip: skip,
                        },
                        {
                            $limit: pageSize,
                        },
                    ],
                    total: [
                        {
                            $count: "total",
                        },
                    ],
                },
            },
        ]);


        return {
            list: data[0]?.products,
            count: data[0]?.total[0]?.total || 0,
        };
    }


    async getProductDetails(id: string) {

        let data: any = await this.productModel.aggregate([
            {
                $match: {
                    _id: Types.ObjectId.createFromHexString(id),
                },
            },
            {
                $lookup: {
                    from: "categories",
                    localField: "firstCategoryId",
                    foreignField: "_id",
                    as: "firstCategoryDetails",
                    pipeline: [
                        {
                            $project: {
                                name: 1,
                                level: 1,
                                slug: 1,
                            },
                        },
                    ],
                },
            },
            {
                $lookup: {
                    from: "categories",
                    localField: "secondCategoryId",
                    foreignField: "_id",
                    as: "secondCategoryDetails",
                    pipeline: [
                        {
                            $project: {
                                name: 1,
                                level: 1,
                                slug: 1,
                            },
                        },
                    ],
                },
            },
            {
                $lookup: {
                    from: "attributevalues",
                    localField: "attributes.value",
                    foreignField: "_id",
                    as: "parentAttributes",
                },
            },
            {
                $lookup: {
                    from: "productvariants",
                    localField: "variantIds",
                    foreignField: "_id",
                    as: "productVariants",
                    pipeline: [
                        {
                            $lookup: {
                                from: "attributevalues",
                                localField: "attributes.value",
                                foreignField: "_id",
                                as: "variantAttributes",
                            },
                        },
                    ],
                },
            },
            {
                $addFields: {
                    variantAttributes: { $setUnion: "$productVariants.variantAttributes" },
                },
            },
            {
                $project: {
                    variantIds: 0,
                    attributes: 0,
                    // variantAttributesList: 0,
                },
            },
        ])
        const attributeArray = [];

        data[0]?.parentAttributes?.forEach(item => {
            let existingItem = attributeArray.find(element => element.key === item.attribute);
            if (existingItem) {
                existingItem.value.push(item);
            } else {
                attributeArray.push({
                    key: item.attribute,
                    inputType: AttributeList[item.attribute].inputType,
                    multiple: AttributeList[item.attribute].multiple,
                    value: [item],
                });
            }
        });
        data[0].parentAttributes = attributeArray;

        return data;
    }

    async updateProduct(id: string, createProductDto: CreateProductDto, user: any) {
        const product = await this.productModel.findById(id);
        if (!product) {
            throw new NotFoundException("Product not found");
        }

        const modifiedProductDetails = {
            attributes: [] as ProductAttributeType[],
            variants: [] as Types.ObjectId[],
            type: product.type,
        };
        const skuSet = new Set();
        for (const variant of createProductDto.variants || []) {
            if (skuSet.has(variant.sku)) {
                throw new BadRequestException(`Duplicate SKU '${variant.sku}' found in request.`);
            }
            skuSet.add(variant.sku);
        }
        // Function to handle attribute conversion
        const convertAttributeValueToObjectId = async (attribute: Attributes, value: any) => {
            const inputType = AttributeList[attribute]?.inputType;
            if (inputType === "textBox") {
                let attributeEntry = await this.AttributeValueModel.findOne({ value });
                if (!attributeEntry) {
                    attributeEntry = new this.AttributeValueModel({
                        attribute,
                        value,
                        slug: this.generateSlug(value),
                    });
                    await attributeEntry.save();
                }
                return attributeEntry._id;
            } else {
                return Array.isArray(value)
                    ? value.map(v => new Types.ObjectId(v))
                    : new Types.ObjectId(value);
            }
        };

        // Convert attributes if present
        if (createProductDto.attributes) {
            for (const attribute of Object.keys(createProductDto.attributes) as Attributes[]) {
                try {
                    const value = createProductDto.attributes[attribute];
                    const objectIdValue = await convertAttributeValueToObjectId(attribute, value);
                    modifiedProductDetails.attributes.push({ key: attribute, value: objectIdValue });
                } catch (error) {
                    console.error(`Error processing attribute ${attribute}:`, error);
                }
            }
        }

        // Handle variable product variants
        if (product.type === "variable" && createProductDto.variants?.length) {
            const variantPromises = createProductDto.variants.map(async variant => {
                const existingVariant = await this.productVariantModel.findOne({ sku: variant.sku });

                const modifiedVariantDetails = {
                    attributes: await Promise.all(
                        Object.keys(variant.attributes || {}).map(async (attribute: Attributes) => {
                            try {
                                const value = variant.attributes[attribute];
                                const objectIdValue = await convertAttributeValueToObjectId(attribute, value);
                                return { key: attribute, value: objectIdValue };
                            } catch (error) {
                                console.error(`Error processing variant attribute ${attribute}:`, error);
                                return null;
                            }
                        })
                    ).then(res => res.filter(Boolean)), // Remove null entries
                };

                if (existingVariant) {
                    return this.productVariantModel.findByIdAndUpdate(
                        existingVariant._id,
                        { ...variant, ...modifiedVariantDetails },
                        { new: true }
                    );
                } else {
                    const newVariant = new this.productVariantModel({
                        productId: id,
                        ...variant,
                        ...modifiedVariantDetails,
                    });
                    return newVariant.save();
                }
            });

            const savedVariants = await Promise.all(variantPromises);
            modifiedProductDetails.variants = savedVariants.map(variant => variant._id);
        }

        // Prepare final product update payload
        const productData = { ...createProductDto, ...modifiedProductDetails, variantIds: modifiedProductDetails.variants };
        delete productData.variants; // No need to store variants separately

        return this.productModel.findByIdAndUpdate(id, productData, { new: true });
    }

    async updateProductStatus(id: string, UpdateProductStatusDto: UpdateProductStatusDto): Promise<any> {
        const existProduct = await this.productModel.findById(id);
        const isProductExistInInventory = await this.inventoryModel.findOne({ productId: id })
        if (!existProduct) {
            throw new BadRequestException("Product not exist");
        }
        if (isProductExistInInventory && isProductExistInInventory.quantity !== undefined && isProductExistInInventory.quantity !== 0 && !UpdateProductStatusDto?.status) {
            throw new BadRequestException("The product is already in inventory and cannot be deactivated.");
        }

        const product = await this.productModel.findByIdAndUpdate(id, { status: UpdateProductStatusDto.status }, { new: true });
        if (existProduct.variantIds.length) {
            await this.productVariantModel.findOneAndUpdate({ productId: id }, { status: UpdateProductStatusDto.status }, { new: true });

        }
        return product;
    }

    async deleteProduct(id: string): Promise<any> {
        const existProduct = await this.productModel.findById(id);
        const isProductExistInInventory = await this.inventoryModel.findOne({ productId: id })
        if (!existProduct) {
            throw new BadRequestException("Product not exist");
        }
        if (isProductExistInInventory) {
            throw new BadRequestException("The product is  in inventory and cannot be Delete.");

        }
        const product = await this.productModel.findByIdAndDelete(id)
        if (existProduct.variantIds.length) {
            await this.productVariantModel.deleteMany({ productId: id });

        }
        return product;

    }
    private generateSlug(name: string): string {
        const shortId = Math.random().toString(36).substring(2, 6);
        return `${name.toLowerCase().split(" ").join("-")}-${shortId}`;
    }


    // ✅ Unified resolveAttributeValue helper
    private async resolveAttributeValue(
        attribute: string,
        value: string,
        organizationId: string
    ): Promise<Types.ObjectId> {
        const raw = String(value ?? '').trim();
        const attr = attribute.toLowerCase();

        if (!raw) {
            throw new BadRequestException(`Empty value for attribute '${attr}'.`);
        }


        if (/^[a-fA-F0-9]{24}$/.test(raw)) {
            const maybeId = new Types.ObjectId(raw);
            const found = await this.AttributeValueModel.findOne({
                _id: maybeId,
                attribute: attr,
                organizationId,
            }).lean();

            if (found) return found._id;
        }

        const existing = await this.AttributeValueModel.findOne({
            attribute: attr,
            value: raw,
            organizationId,
        }).lean();
        console.log(existing)
        if (existing) return existing._id;

        const created = await this.createOrGetSubAttributeValue(attr, raw, organizationId);
        return created._id;
    }

    private async resolveRevenueCategoryId(
        categoryName: string,
        organizationId: string
    ): Promise<Types.ObjectId | null> {
        if (!categoryName || !categoryName.trim()) {
            return null;
        }

        const trimmedName = categoryName.trim();

        try {
            const organization = await this.organizationModel.findOne({ userId: organizationId });
            if (!organization) {
                return null;
            }

            // Initialize revenueCategory array if it doesn't exist
            if (!organization.revenueCategory) {
                organization.revenueCategory = [];
            }

            // Check if revenue category already exists
            const existingCategory = organization.revenueCategory.find(
                category => category.name && category.name.toLowerCase() === trimmedName.toLowerCase()
            );

            if (existingCategory) {
                return existingCategory._id;
            }

            // Create new revenue category
            const newCategoryId = new Types.ObjectId();
            const newRevenueCategory = {
                _id: newCategoryId,
                name: trimmedName,
                description: '',
                isActive: true
            };

            organization.revenueCategory.push(newRevenueCategory as any);
            await organization.save();

            return newCategoryId;
        } catch (error) {
            console.error('Error resolving revenue category:', error);
            return null;
        }
    }

    private extractDynamicKeys(row: any): string[] {
        const exclude = [
            "sku", "master_sku", "name", "type", "Category", "Sub-Category",
            "Brand", "hsn", "gst", "variantSku", "variantTitle", "productId", "revenueCategory"
        ];
        return Object.keys(row).filter(k => !exclude.includes(k));
    }


    private groupBySku(data: any[]): Record<string, { row: any; lineNumber: number }[]> {
        return data.reduce((acc, row, index) => {
            const code = row.sku;
            if (!acc[code]) acc[code] = [];
            acc[code].push({ row, lineNumber: index + 2 });
            return acc;
        }, {});
    }
    private normalizeKeys(data: any[]): any[] {
        return data.map(row => {
            // existing mapping
            if (row.master_sku && !row.sku) {
                row.sku = row.master_sku;
            }

            const candidates = ['productId'];
            for (const c of candidates) {
                if (row[c] != null && row[c] !== '') {
                    row.migrationId = String(row[c]).trim();
                    break;
                }
            }

            return row;
        });
    }

    private async processProductGroup(
        groupedRows: { row: any; lineNumber: number }[],
        dynamicKeys: string[],
        errorLogs: any[],
        organizationId: string,
        isUpdate: boolean
    ): Promise<{ success: number }> {
        console.log(isUpdate, "dsljfl")
        const { row, lineNumber } = groupedRows[0];
        let success = 0;

        const isValid = this.validateProductRowFields(row, lineNumber, errorLogs);
        if (!isValid) return { success };

        const session = await this.transactionService.startTransaction();

        try {
            let product = await this.productModel.findOne({
                sku: row.sku,
                organizationId,
            });
            if (product && !isUpdate) {
                errorLogs.push({
                    lineNumber,
                    message: 'Duplicate product SKU.',
                    value: row.sku,
                    sku: row.sku,
                    rowData: row,
                });
                return { success: 0 };
            }
            const firstCategoryId = await this.createOrGetCategory(
                row.Category,
                CategoryLevel.FIRST,
                organizationId
            );

            const secondCategoryId = row["Sub-Category"]
                ? await this.createOrGetCategory(
                    row["Sub-Category"],
                    CategoryLevel.SECOND,
                    organizationId,
                    firstCategoryId
                )
                : null;
            console.log(row.Brand)
            const brandObjId = await this.resolveAttributeValue("brand", row.Brand, organizationId);
            console.log(brandObjId, "brand")
            const attributes = [{ key: "brand" as Attributes, value: brandObjId }];

            // Resolve revenue category if provided
            const revenueCategoryId = await this.resolveRevenueCategoryId(row.revenueCategory, organizationId);

            // ✅ If update requested but SKU not found, treat it like create
            const isNewProduct = !product;

            if (product) {

                // 🔁 Update flow
                product.name = row.name || product.name;
                product.sku = row.sku || product.sku;
                product.slug = this.generateSlug(row.name || product.name);
                product.type = row.type?.toLowerCase() || product.type?.toLowerCase();
                product.hsn = row.hsn || product.hsn;
                product.gst = row.gst || product.gst;
                product.firstCategoryId = new Types.ObjectId(firstCategoryId);
                product.secondCategoryId = secondCategoryId ? new Types.ObjectId(secondCategoryId) : null;
                product.attributes = attributes;
                if (row.productId && String(row.productId).trim() !== '') {
                    product.migrationId = String(row.productId).trim();
                }
                product.revenueCategory = revenueCategoryId;
                product.variantAttributesList = [];
                await product.save({ session });
            } else {
                product = await new this.productModel({
                    name: row.name,
                    itemCode: row.sku,
                    sku: row.sku,
                    slug: this.generateSlug(row.name),
                    type: row.type.toLowerCase(),
                    hsn: row.hsn,
                    gst: row.gst,
                    firstCategoryId,
                    secondCategoryId,
                    createdBy: organizationId,
                    organizationId,
                    attributes,
                    revenueCategory: revenueCategoryId,
                    variantIds: [],
                    variantAttributesList: [],
                    status: true,
                    migrationId: String(row.productId).trim()
                }).save({ session });
            }

            const variantIds: Types.ObjectId[] = [];
            const usedDynamicKeys = new Set<string>();
            if (row.type?.toLowerCase() === "variable") {
                for (const { row: vRow, lineNumber: vLine } of groupedRows) {
                    for (const key of dynamicKeys) {
                        const value = vRow[key];
                        if (value && value.trim() !== "") {
                            usedDynamicKeys.add(key);
                        }
                    }
                }
                for (const { row: vRow, lineNumber: vLine } of groupedRows) {
                    const result = await this.processVariantRow(
                        vRow,
                        product._id.toString(),
                        dynamicKeys,
                        errorLogs,
                        vLine,
                        organizationId,
                        session,
                        isUpdate
                    );

                    if (result?.variantId) {
                        variantIds.push(result.variantId);
                        success++;
                    }
                }

                await this.productModel.findByIdAndUpdate(
                    product._id,
                    { variantIds, variantAttributesList: Array.from(usedDynamicKeys) as Attributes[], },
                    { session }
                );
            } else {
                success++;
            }

            await this.transactionService.commitTransaction(session);
            return { success };
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            errorLogs.push({
                lineNumber,
                message: "Error in product group transaction",
                value: error.message,
                rowData: row,
            });
            return { success };
        } finally {
            session.endSession();
        }
    }




    private async processVariantRow(
        variantRow: any,
        productId: string,
        dynamicKeys: string[],
        errorLogs: any[],
        lineNumber: number,
        organizationId: any,
        session: any,
        isUpdate: boolean
    ): Promise<{ variantId?: Types.ObjectId }> {
        const isValid = this.validateVariantRowFields(variantRow, lineNumber, errorLogs);
        if (!isValid) return {};

        const attributes = [];

        for (const key of dynamicKeys) {
            const value = variantRow[key];
            if (!value || value.trim() === "") continue;

            let objId;
            if (Types.ObjectId.isValid(value.trim())) {
                objId = Types.ObjectId.createFromHexString(value.trim());
            } else {
                const attr: any = await this.createOrGetAttributeField(key, AttributeType.Variant, "dropdown", false, organizationId);
                const subAttr = await this.createOrGetSubAttributeValue(key, value, organizationId);
                objId = subAttr._id;
            }
            if (objId) attributes.push({ key, value: objId });
        }
        const existing = await this.productVariantModel.findOne({
            sku: variantRow.variantSku,
            organizationId,
            productId,
        });

        if (existing && isUpdate) {
            // ✅ UPDATE existing variant
            existing.title = variantRow.variantTitle;
            existing.itemCode = variantRow.variantSku;
            existing.hsnCode = variantRow.hsn || existing.hsnCode;
            existing.attributes = attributes;
            await existing.save({ session });
            return { variantId: existing._id };
        }



        if (existing && !isUpdate) {
            errorLogs.push({
                lineNumber,
                message: "Duplicate variant SKU",
                field: "variantSku",
                sku: variantRow.variantSku,
                value: variantRow.variantSku,
                rowData: variantRow,
            });
            return {};
        }

        // ✅ CREATE new variant
        const variant = new this.productVariantModel({
            title: variantRow.variantTitle,
            sku: variantRow.variantSku,
            itemCode: variantRow.variantSku,
            hsnCode: variantRow.hsn || '',
            productId,
            attributes,
            organizationId,
            createdBy: organizationId,
            status: true,
        });

        const saved = await variant.save({ session });
        return { variantId: saved._id };
    }



    private async createOrGetAttributeField(
        name: string,
        type: AttributeType,
        inputType: "dropdown",
        multiple: false,
        organizationId: string,
    ): Promise<ProductVariantAttributeDocument> {
        const slug = this.generateKeyFromName(name);

        let attribute = await this.productVariantAttributeModel.findOne({
            slug,
            organizationId,
        });

        if (!attribute) {
            const session = await this.transactionService.startTransaction();
            try {
                attribute = new this.productVariantAttributeModel({
                    name,
                    slug,
                    type,
                    inputType,
                    multiple,
                    organizationId,
                    createdBy: organizationId,
                });

                await attribute.save({ session });
                await this.transactionService.commitTransaction(session);
            } catch (error) {
                await this.transactionService.abortTransaction(session);
                throw new Error(`Failed to create attribute field: ${error.message}`);
            } finally {
                session.endSession();
            }
        }

        return attribute;
    }

    private async createOrGetSubAttributeValue(
        attribute: string,
        value: string,
        organizationId: string,
        hexCode?: string
    ): Promise<AttributeValueDocument> {
        let existing = await this.AttributeValueModel.findOne({
            attribute: attribute.toLowerCase(),
            value,
            organizationId,
        });
        if (existing) return existing;

        const session = await this.transactionService.startTransaction();
        try {
            const newAttrVal = new this.AttributeValueModel({
                attribute: attribute.toLowerCase(),
                value,
                slug: this.generateSlug(value),
                organizationId,
                createdBy: organizationId,
                hexCode,
            });

            const saved = await newAttrVal.save({ session });
            await this.transactionService.commitTransaction(session);
            return saved;
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw new Error(`Failed to create subattribute value for '${value}': ${error.message}`);
        } finally {
            session.endSession();
        }
    }


    private generateKeyFromName(name: string): string {
        return name
            .replace(/[^\w\s]/gi, "")
            .split(" ")
            .map((word, index) =>
                index === 0
                    ? word.toLowerCase()
                    : word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
            )
            .join("");
    }


    private async createOrGetCategory(
        name: string,
        level: CategoryLevel,
        organizationId: string,
        parentId?: string
    ): Promise<string> {
        name = name.trim();
        const existingCategory = await this.categoryModel.findOne({
            name,
            level,
            organizationId,
            parentId: parentId || null,
        });

        if (existingCategory) {
            return existingCategory._id;
        }


        let parentCategory: CategoryDocument | null = null;

        if (parentId) {
            parentCategory = await this.categoryModel.findById(parentId);
            if (!parentCategory) {
                throw new BadRequestException("Parent category not found for sub-category");
            }


            if (level === CategoryLevel.SECOND && parentCategory.level !== CategoryLevel.FIRST) {
                throw new BadRequestException("Sub-category must have a first-level category as parent");
            }
        }


        const session = await this.transactionService.startTransaction();
        try {
            const newCategory = new this.categoryModel({
                name,
                level,
                parentId: parentId || null,
                createdBy: organizationId,
                organizationId,
                status: "active",
                children: [],
            });

            if (parentCategory) {
                parentCategory.children.push(newCategory._id);
                await parentCategory.save({ session });
            }

            const created = await newCategory.save({ session });
            await this.transactionService.commitTransaction(session);
            return created._id;
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw new Error("Error creating category: " + error.message);
        } finally {
            session.endSession();
        }
    }



    async bulkUploadProducts(jsonData: any[], user: any, isUpdate = false): Promise<any> {
        const organizationId = await this.getOrganizationId(user) as string;
        const errorLogs = [];

        // Validate headers if data exists
        if (jsonData.length > 0) {
            const headers = Object.keys(jsonData[0]);
            // if (!this.validateCSVHeaders(headers, errorLogs)) {
            //     return {
            //         total: jsonData.length,
            //         success: 0,
            //         failed: errorLogs.length,
            //         errors: errorLogs
            //     };
            // }
        }

        const normalizedData = this.normalizeKeys(jsonData);
        const dynamicKeys = this.extractDynamicKeys(normalizedData[0]);
        const groupedProducts = this.groupBySku(normalizedData);

        let successCount = 0;

        for (const itemCode in groupedProducts) {
            const rows = groupedProducts[itemCode];
            const result = await this.processProductGroup(rows, dynamicKeys, errorLogs, organizationId, isUpdate);
            successCount += result.success;
        }

        return {
            total: jsonData.length,
            success: successCount,
            failed: errorLogs.length,
            errors: errorLogs
        };
    }


    private validateProductRowFields(
        row: Record<string, any>,
        lineNumber: number,
        errorLogs: Array<{ lineNumber: number; message: string; value?: any; field?: string; sku?: string; rowData?: any }>
    ): boolean {
        const requiredFields = [
            { key: "name", label: "Product Name" },
            { key: "sku", label: "SKU" },
            { key: "type", label: "Product Type" },
            { key: "Category", label: "Category" },
            { key: "Brand", label: "Brand" },
            { key: "hsn", label: "HSN Code" },
            { key: "gst", label: "GST (%)" },
        ];

        let isValid = true;

        for (const field of requiredFields) {
            const rawValue = row[field.key];
            const value = typeof rawValue === "string" ? rawValue.trim() : rawValue;

            if (value === undefined || value === null || value === "") {
                errorLogs.push({
                    lineNumber,
                    message: `${field.label} is required.`,
                    field: field.key,
                    value: rawValue,
                    rowData: row,
                });
                isValid = false;
            }
        }

        // Validate type
        const rawType = row["type"];
        const type = typeof rawType === "string" ? rawType.toLowerCase().trim() : "";
        if (type && !["simple", "variable"].includes(type)) {
            errorLogs.push({
                lineNumber,
                message: `Product Type must be either 'simple' or 'variable'.`,
                field: "type",
                value: row["type"],
                rowData: row,
            });
            isValid = false;
        }

        // Validate GST
        const gstValue = row["gst"];
        if (gstValue !== undefined && gstValue !== null && isNaN(Number(gstValue))) {
            errorLogs.push({
                lineNumber,
                message: `GST should be a valid number.`,
                field: "gst",
                value: gstValue,
                rowData: row,
            });
            isValid = false;
        }

        return isValid;
    }
    private validateVariantRowFields(
        row: Record<string, any>,
        lineNumber: number,
        errorLogs: Array<{ lineNumber: number; message: string; value?: any; field?: string; sku?: string; rowData?: any }>
    ): boolean {
        const required = [
            { key: "variantSku", label: "Variant SKU" },
            { key: "variantTitle", label: "Variant Title" },

        ];

        let isValid = true;

        for (const field of required) {
            if (!row[field.key]) {
                errorLogs.push({
                    lineNumber,
                    message: `${field.label} is required.`,
                    field: field.key,
                    value: row[field.key],
                    rowData: row,
                });
                isValid = false;
            }
        }

        return isValid;
    }
    private validateCSVHeaders(headers: string[], errorLogs: any[]): boolean {
        const requiredHeaders = [
            "name",
            "Brand",
            "master_sku",
            "type",
            "Category",
            "Sub-Category",
            "hsn",
            "gst"
        ];

        const optionalHeaders = [
            "variantTitle",
            "variantSku",
            "variantHsn",
            "variantGst"
        ];

        // Check for missing required headers
        const missingHeaders = requiredHeaders.filter(header =>
            !headers.some(h => h.toLowerCase() === header.toLowerCase())
        );

        if (missingHeaders.length > 0) {
            errorLogs.push({
                lineNumber: 1,
                message: `Missing required header(s): ${missingHeaders.join(", ")}`,
                value: ""
            });
            return false;
        }

        // Check for duplicate headers (case-insensitive)
        const headerMap = new Map<string, number>();
        headers.forEach(header => {
            const lowerHeader = header.toLowerCase();
            headerMap.set(lowerHeader, (headerMap.get(lowerHeader) || 0) + 1);
        });

        const duplicateHeaders = Array.from(headerMap.entries())
            .filter(([_, count]) => count > 1)
            .map(([header]) => header);

        if (duplicateHeaders.length > 0) {
            errorLogs.push({
                lineNumber: 1,
                message: `Duplicate header(s) found: ${duplicateHeaders.join(", ")}`,
                value: ""
            });
            return false;
        }

        // Validate header format (no special characters, proper spacing)


        return true;
    }
    async getProductsForExport(
        user: any,
        productType: 'simple' | 'variable' | 'both',
        startDate?: string,
        endDate?: string,
    ) {
        const organizationId = await this.getOrganizationId(user);
        const matchStage: any = {
            organizationId: new Types.ObjectId(organizationId),
        };

        if (productType !== 'both') {
            matchStage.type = productType;
        }

        if (startDate || endDate) {
            matchStage.createdAt = {};
            if (startDate) matchStage.createdAt.$gte = new Date(startDate);
            if (endDate) matchStage.createdAt.$lte = new Date(endDate);
        }

        const products = await this.productModel.aggregate([
            { $match: matchStage },

            {
                $lookup: {
                    from: "categories",
                    localField: "firstCategoryId",
                    foreignField: "_id",
                    as: "firstCategoryDetails",
                    pipeline: [{ $project: { name: 1, level: 1, slug: 1 } }],
                },
            },
            {
                $lookup: {
                    from: "categories",
                    localField: "secondCategoryId",
                    foreignField: "_id",
                    as: "secondCategoryDetails",
                    pipeline: [{ $project: { name: 1, level: 1, slug: 1 } }],
                },
            },
            {
                $lookup: {
                    from: "attributevalues",
                    localField: "attributes.value",
                    foreignField: "_id",
                    as: "parentAttributes",
                },
            },
            {
                $lookup: {
                    from: "productvariants",
                    localField: "variantIds",
                    foreignField: "_id",
                    as: "productVariants",
                    pipeline: [
                        {
                            $lookup: {
                                from: "attributevalues",
                                localField: "attributes.value",
                                foreignField: "_id",
                                as: "variantAttributes",
                            },
                        },
                    ],
                },
            },
            {
                $addFields: {
                    variantAttributes: { $setUnion: "$productVariants.variantAttributes" },
                },
            },
            {
                $project: {
                    variantIds: 0,
                    attributes: 0,
                },
            },
        ]);

        return products;
    }
    async bulkProductListing(dto: BrandProductListingDto, user: any) {
        const {
            brandId = [],
        } = dto;

        const orgId = await this.getOrganizationId(user);
        if (!orgId) throw new Error('organizationId missing on user');

        const orgObjectId = new Types.ObjectId(orgId);
        const brandObjectIds = brandId.map((id: string) => new Types.ObjectId(id));

        const baseMatch: any = {
            organizationId: orgObjectId,
            status: true,
            attributes: { $elemMatch: { key: 'brand', value: { $in: brandObjectIds } } },
        };

        //   const searchOr = search
        //     ? [
        //         { name: { $regex: search, $options: 'i' } },
        //         { sku: { $regex: search, $options: 'i' } },
        //       ]
        //     : [];

        const pipeline: any[] = [
            { $match: baseMatch },

            // ...(searchOr.length ? [{ $match: { $or: searchOr } }] : []),

            {
                $lookup: {
                    from: 'productvariants',
                    localField: 'variantIds',
                    foreignField: '_id',
                    as: 'variants',
                },
            },

            {
                $addFields: {
                    variants: {
                        $filter: {
                            input: '$variants',
                            as: 'v',
                            cond: {
                                $and: [
                                    { $eq: ['$$v.status', true] },
                                    // ...(search
                                    //   ? [
                                    //       {
                                    //         $or: [
                                    //           { $regexMatch: { input: '$$v.sku', regex: search, options: 'i' } },
                                    //           { $regexMatch: { input: '$$v.name', regex: search, options: 'i' } },
                                    //         ],
                                    //       },
                                    //     ]
                                    //   : []),
                                ],
                            },
                        },
                    },
                },
            },

            { $unwind: '$variants' },

            {
                $project: {
                    _id: 0,
                    productId: '$_id',
                    variantId: '$variants._id',
                    type: '$type', // 'variable'
                    // prefer variant SKU/name, fallback to product
                    sku: { $ifNull: ['$variants.sku', '$sku'] },
                    name: {
                        $ifNull: [
                            '$variants.name',
                            { $concat: ['$name', ' - ', { $ifNull: ['$variants.sku', 'Variant'] }] },
                        ],
                    },
                    slug: 1,
                    hsn: 1,
                    gst: 1,
                    firstCategoryId: 1,
                    secondCategoryId: 1,
                    attributes: 1,                 // parent attributes (includes brand)
                    variantAttributes: '$variants.attributes',
                    price: '$variants.price',
                    mrp: '$variants.mrp',
                    quantity: '$variants.quantity',
                    status: '$variants.status',
                    createdAt: 1,
                    updatedAt: 1,
                },
            },

            {
                $unionWith: {
                    coll: 'products',
                    pipeline: [
                        { $match: { ...baseMatch, type: 'simple' } },
                        {
                            $project: {
                                _id: 0,
                                productId: '$_id',
                                variantId: null,
                                type: 'simple',
                                sku: '$sku',
                                name: '$name',
                                slug: 1,
                                hsn: 1,
                                gst: 1,
                                firstCategoryId: 1,
                                secondCategoryId: 1,
                                attributes: 1,
                                variantAttributes: [],
                                price: '$price',
                                mrp: '$mrp',
                                quantity: '$quantity',
                                status: '$status',
                                createdAt: 1,
                                updatedAt: 1,
                            },
                        },
                    ],
                },
            },

            { $sort: { createdAt: -1, productId: -1, variantId: -1 } },

            // No pagination: return all docs + total count
            {
                $facet: {
                    data: [], // pass-through of all docs after sort
                    totalCount: [{ $count: 'count' }],
                },
            },
            {
                $project: {
                    data: 1,
                    total: { $ifNull: [{ $arrayElemAt: ['$totalCount.count', 0] }, 0] },
                },
            },
        ];

        const [result] = await this.productModel.aggregate(pipeline).allowDiskUse(true);
        return result ?? { data: [], total: 0 };
    }

    // create a new batch record
    async createUploadBatch({ user, filePath, isUpdate }: {
        user: any; filePath: string; isUpdate: boolean;
    }) {
        const organizationId = await this.getOrganizationId(user);
        return this.uploadBatchModel.create({
            organizationId: new Types.ObjectId(organizationId),
            filePath,
            isUpdate,
            initiatedByUserId: String(user?._id || ''),
            status: 'queued',
        });
    }

    async getUploadBatch(batchId: string) {
        return this.uploadBatchModel
            .findById(batchId)
            .lean<
                UploadBatch & {
                    processedCount?: number;
                    failedCount?: number;
                    isUpdate?: boolean;
                    organizationId: Types.ObjectId; // <-- important
                }
            >();
    }

    async markBatchProcessing(batchId: string) {
        await this.uploadBatchModel.updateOne(
            { _id: new Types.ObjectId(batchId), status: { $ne: 'failed' } },
            { $set: { status: 'processing', updatedAt: new Date() } },
            { strict: false },
        ).exec();
    }

    // progress updates must NEVER flip a completed batch back to "processing"
    async updateBatchProgress(
        batchId: string,
        patch: Partial<UploadBatch> & { processedCount?: number; failedCount?: number }
    ) {
        // do NOT include "status" in $set here.
        const { processedCount, failedCount, ...rest } = patch;

        await this.uploadBatchModel.updateOne(
            {
                _id: new Types.ObjectId(batchId),
                status: { $in: ['queued', 'processing'] }, // guard: only while in-flight
            },
            {
                $set: {
                    ...(processedCount !== undefined ? { processedCount } : {}),
                    ...(failedCount !== undefined ? { failedCount } : {}),
                    ...rest,                        // any extra counters/timestamps (NOT status)
                    updatedAt: new Date(),
                },
            },
            { strict: false },
        ).exec();
    }

    // ALWAYS compute and set final status here
    async finalizeBatch(batchId: string, patch: Partial<UploadBatch>) {
        const doc = await this.uploadBatchModel.findById(batchId);
        if (!doc) return;

        // ensure sane defaults and always-completed
        const failed = patch.failedCount ?? doc.failedCount ?? 0;
        const processed = patch.processedCount ?? doc.processedCount ?? 0;
        const total = patch.totalRows ?? doc.totalRows ?? processed + failed;

        if (patch.processedRows == null) {
            patch.processedRows = Math.min(total, processed + failed);
        }

        // ⬇️ Force completed unless explicitly overridden to 'failed'
        if (!patch.status || patch.status === 'processing' || patch.status === 'queued') {
            patch.status = 'completed';
        }

        await this.uploadBatchModel.findByIdAndUpdate(batchId, patch, { new: false });
    }


    async failBatch(batchId: string, message: string) {
        await this.uploadBatchModel.findByIdAndUpdate(batchId, { status: 'failed', message });
    }

    private async saveErrorsToDatabase(batchId: string, errorLogs: Array<{ lineNumber: number; message: string; value?: any; field?: string; sku?: string; rowData?: any }>) {
        try {
            const errors = errorLogs.map(error => ({
                lineNumber: error.lineNumber,
                message: error.message,
                field: error.field || '',
                sku: error.sku || '',
                value: error.value || '',
                rowData: error.rowData || {},
                timestamp: new Date(),
            }));

            await this.uploadBatchModel.findByIdAndUpdate(
                batchId,
                { $push: { errors: { $each: errors } } },
                { new: true }
            );
        } catch (error) {
            console.error('Failed to save errors to database:', error);
        }
    }

    async getBatchErrors(batchId: string, offset: number = 0, limit: number = 200) {
        try {
            const batch = await this.uploadBatchModel.findById(batchId).lean();
            if (!batch) {
                throw new BadRequestException('Invalid batchId');
            }

            const errors = batch.errors || [];
            const total = errors.length;
            const paginatedErrors = errors.slice(offset, offset + limit);

            return {
                total,
                offset,
                limit,
                data: paginatedErrors.map(error => ({
                    lineNumber: error.lineNumber || '',
                    message: error.message || '',
                    field: error.field || '',
                    sku: error.sku || '',
                    value: error.value || '',
                    timestamp: error.timestamp || '',
                })),
                meta: {
                    batchStatus: batch.status,
                    failedCount: batch.failedCount || 0,
                }
            };
        } catch (error) {
            console.error('Failed to retrieve errors from database:', error);
            throw error;
        }
    }
    async bulkUploadProductsBatch(
        rows: any[],
        user: any,
        isUpdate: boolean,
        batchId: string,
    ) {
        const organizationId = await this.getOrganizationId(user) as string;
        const errorLogs: Array<{ lineNumber: number; message: string; value?: any; field?: string; sku?: string; rowData?: any }> = [];

        const normalizedData = this.normalizeKeys(rows);

        const dynamicKeys = normalizedData.length ? this.extractDynamicKeys(normalizedData[0]) : [];

        const grouped = this.groupBySku(normalizedData);

        let successCount = 0;
        for (const sku of Object.keys(grouped)) {
            const groupRows = grouped[sku];
            const result = await this.processProductGroup(groupRows, dynamicKeys, errorLogs, organizationId, isUpdate);
            successCount += result.success || 0;
        }

        // Save errors to database if any exist
        if (errorLogs.length > 0) {
            await this.saveErrorsToDatabase(batchId, errorLogs);
        }

        return {
            success: successCount,
            failed: errorLogs.length,
            errors: errorLogs,   // worker will write these lines into the error CSV
        };
    }
    async processCsvBufferInBackground(args: {
        buffer: Buffer;
        user: any;
        isUpdate: boolean;
        batchId: string;
    }) {
        const { buffer, user, isUpdate, batchId } = args;

        try {
            await this.markBatchProcessing(batchId);

            // Parse CSV
            const rows = parseCsv(buffer, {
                columns: true,
                bom: true,
                skip_empty_lines: true,
                trim: true,
            }) as Record<string, any>[];

            if (!rows.length) {
                await this.finalizeBatch(batchId, {
                    totalRows: 0,
                    processedCount: 0,
                    failedCount: 0,
                    status: 'failed',
                    message: 'CSV had no data rows.',
                });
                return;
            }

            // Set total upfront so UI can display progress
            await this.updateBatchProgress(batchId, { totalRows: rows.length });

            // Process using your existing pipeline
            const result = await this.bulkUploadProductsBatch(rows, user, isUpdate, batchId);

            // Finalize
            await this.finalizeBatch(batchId, {
                totalRows: rows.length,
                processedCount: result.success ?? 0,
                failedCount: result.failed ?? 0,
                status:
                    (result.success ?? 0) === 0 && (result.failed ?? 0) > 0
                        ? 'failed'
                        : 'completed',
                message: `In-memory upload processed. success=${result.success ?? 0}, failed=${result.failed ?? 0}`,
            });
        } catch (err: any) {
            await this.failBatch(batchId, err?.message || 'Unknown error');
        }
    }
}