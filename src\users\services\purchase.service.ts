import { BadRequestException, HttpException, HttpStatus, Injectable, InternalServerErrorException, NotFoundException, StreamableFile } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import mongoose, { Model, PipelineStage, Types } from "mongoose";
import { CreateInvoicePurchaseDto } from "../dto/packages-purchasing.dto";
import { Purchase, PurchaseDocument, Suspensions } from "../schemas/purchased-packages.schema";
import { User, UserDocument } from "src/users/schemas/user.schema";
import { Pricing, PricingDocument, Services } from "src/organization/schemas/pricing.schema";
import { StaffProfileDetails } from "src/staff/schemas/staff.schema";
import { GetPackagesDto } from "../dto/get-packages.dto";
import { Invoice } from "../schemas/invoice.schema";
import { SessionType } from "src/utils/enums/session-type.enum";
import { DurationUnit } from "src/utils/enums/duration-unit.enum";
import { MembershipStatus, PricingListDTO } from "../dto/pricing-lis.dto";
import { GetInvoicesDto } from "../dto/get-invoices.dto";
import { membership } from "src/membership/schema/membership.schema";
import { Clients } from "../schemas/clients.schema";
import { MailService } from "src/mail/services/mail.service";
import { InvoiceService } from "../services/invoice.service";
import { UpdatePaymentStatusDto } from "src/staff/dto/update-payment-status.dto";
import { CancelOrder } from "src/staff/dto/cancel-order.dto";
import { InvoiceStatus } from "src/utils/enums/invoice-status.enum";
import { PaymentStatus } from "src/utils/enums/payment.enum";
import { Facility, FacilityDocument } from "src/facility/schemas/facility.schema";
import { PaginationDto } from "src/utils/dto/pagination.dto";
import { PaymentMethod } from "src/utils/enums/paymentMethod.enum";
import { DiscountType } from "src/utils/enums/discount.enum";
import moment from "moment-timezone";
import { ExportInvoicesDto } from "../dto/export-invoices.dto";
import { SuspendMembershipDto } from "../dto/suspend-membership.dto";
import { ResumeMembershipDto } from "../dto/resume-suspended-membership.dto";
import { Scheduling } from "src/scheduling/schemas/scheduling.schema";
import { ScheduleStatusType } from "src/scheduling/enums/schedule-status.enum";
import { UpdateQuery } from "mongoose";
import { UsersPipe } from "../pipes/users.pipe";
import { Inventory } from "src/merchandise/schema/inventory.schema";
import { Product, ProductType } from "src/merchandise/schema/product.schema";
import { ProductVariant } from "src/merchandise/schema/product-variant.schema";
import { isArray, Matches } from "class-validator";
import { CustomPackage } from "src/customPackage/schemas/custom-package.schema";
import { ExportZOutReportDto } from "../dto/z-out-report.dto";
import { ClassType } from "src/utils/enums/class-type.enum";
import { ExportScheduleReportDto } from "../dto/schedule-report.dto";
import { Reconciliation } from "src/transactions/schema/reconciliation.schema";
import { PaymentMethodSchema } from "src/paymentMethod/schemas/payment-method.schema";
import { IDatabaseObjectId } from "src/common/database/interfaces/database.objectid.interface";
const numberToWords = require("number-to-words");
const { write, utils } = require("xlsx");
import * as puppeteer from "puppeteer";
import * as Handlebars from "handlebars";
import * as fs from "fs";
import * as path from "path";
import { ExportSalesReportDto, ReportCategory } from "../dto/sales-report.dto";
import { ExportSalesByEmpDto } from "../dto/sales-by-emp.dto";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { IUserDocument } from "../interfaces/user.interface";
import { ENUM_PRODUCT_ITEM_TYPE } from "src/promotions/enums/item-type.enum";
import { PurchaseRequestDto } from "../dto/purchase-request.dto";
import { PurchaseResponseDto } from "../dto/response/purchase-response.dto";
import { TransactionService } from "src/utils/services/transaction.service";
import { CartService } from "src/cart/services/cart.service";
import * as QRCode from "qrcode";
import { v4 as uuidV4 } from "uuid";
import { UploadService } from "src/utils/services/upload.service";
import { UpdatePurchaseSessionDto } from "../dto/update-purchase-session.dto";
import { PricingSessionLogs } from "../schemas/pricingSessions.log";
import { Enrollment } from "src/courses/schemas/enrollment.schema";
import { VoucherRedemption, VoucherRedemptionLogDocument } from "../schemas/voucher-redemption.schema";
import { SchedulingService } from "src/scheduling/services/scheduling.service";
import { PaymentService } from "src/payment/service/payment.service";
const Razorpay = require("razorpay");
import { FamilyShareClientDto, SharePackageDto } from "../dto/share-package.dto";
import { IPaginationOrder } from "src/common/pagination/interfaces/pagination.interface";
import { PaginationService } from "src/common/pagination/services/pagination.service";
import { removeAllListeners } from "process";
import { RoleEntity } from "src/role/repository/entities/role.entity";
import { OrganizationService } from "src/organization/services/organization.service";
import { Organizations } from "src/organization/schemas/organization.schema";
import { Msg91Service } from "src/message/service/msg91.service";
import { PaymentMethod as paymentMethodSchema } from "src/paymentMethod/schemas/payment-method.schema";
import { ExportMultipleZOutReportDto } from "../dto/zout-export-multiple.dto";
import archiver from "archiver";
import { Response } from "express";
import { PassThrough } from "stream";
import { VoucherService } from "src/organization/services/voucher.service";
import { T } from "node_modules/@faker-js/faker/dist/airline-CLphikKp.cjs";

@Injectable()
export class PurchaseService {
    constructor(
        @InjectModel(User.name) private UserModel: Model<User>,
        @InjectModel(Clients.name) private ClientModel: Model<Clients>,
        @InjectModel(Purchase.name) private readonly PurchaseModel: Model<PurchaseDocument>,
        @InjectModel(Organizations.name) private readonly OrganizationsModel: Model<Organizations>,
        @InjectModel(Pricing.name) private PricingModel: Model<Pricing>,
        @InjectModel(StaffProfileDetails.name) private StaffDetailsModel: Model<StaffProfileDetails>,
        @InjectModel(Invoice.name) private InvoiceModel: Model<Invoice>,
        @InjectModel(membership.name) private readonly MembershipModel: Model<membership>,
        @InjectModel(Facility.name) private FacilityModel: Model<Facility>,
        @InjectModel(Scheduling.name) private SchedulingModel: Model<Scheduling>,
        @InjectModel(Inventory.name) private InventoryModel: Model<Inventory>,
        @InjectModel(Product.name) private productModel: Model<Product>,
        @InjectModel(ProductVariant.name) private productVariantModel: Model<ProductVariant>,
        @InjectModel(CustomPackage.name) private customPackageModel: Model<CustomPackage>,
        @InjectModel(Reconciliation.name) private ReconciliationModel: Model<Reconciliation>,
        @InjectModel(paymentMethodSchema.name) private paymentMethodModel: Model<paymentMethodSchema>,
        @InjectModel(PricingSessionLogs.name) private PricingSessionLogsModel: Model<PricingSessionLogs>,
        @InjectModel(Enrollment.name) private EnrollmentModel: Model<Enrollment>,
        @InjectModel(VoucherRedemption.name) private VoucherRedemptionLogModel: Model<VoucherRedemptionLogDocument>,
        @InjectModel(RoleEntity.name) private roleSchema: Model<RoleEntity>,
        private readonly organizationService: OrganizationService,

        private readonly schedulingService: SchedulingService,
        private readonly msg91Service: Msg91Service,
        private readonly usersPipe: UsersPipe,
        private readonly mailService: MailService,
        private readonly cartService: CartService,
        private readonly invoiceService: InvoiceService,
        private readonly uploadService: UploadService,
        private readonly transactionService: TransactionService,
        private readonly paymentService: PaymentService,
        private readonly paginationService: PaginationService,
        private readonly voucherService: VoucherService,
    ) { }

    convertAmountToWords(amount) {
        const amountInWords = numberToWords.toWords(amount);
        return amountInWords.charAt(0).toUpperCase() + amountInWords.slice(1) + " Rupees Only";
    }

    /**
     * Auto enroll client in all available schedules for a course package
     * Handles duplicate enrollments by skipping if user is already enrolled with different purchase
     * @param userId - User to enroll
     * @param packageId - Course package ID
     * @param purchaseId - Current purchase ID
     * @param organizationId - Organization ID
     * @param session - Database session for transaction
     * @returns Array of new enrollments created
     */
    private async autoEnrollInCourseSchedules(
        userId: IDatabaseObjectId,
        packageId: IDatabaseObjectId,
        purchaseId: IDatabaseObjectId,
        organizationId: IDatabaseObjectId,
        session: any,
    ): Promise<any[]> {
        const newEnrollments = [];

        try {
            // Get all schedules for this course package
            const schedulings = await this.SchedulingModel.find({
                packageId: packageId,
                organizationId: organizationId,
                scheduleStatus: { $ne: ScheduleStatusType.CANCELED },
            })
                .session(session)
                .exec();

            if (!schedulings || schedulings.length === 0) {
                return newEnrollments;
            }

            // Get existing enrollments for this user in all schedules of this course
            const existingEnrollments = await this.EnrollmentModel.find({
                schedulingId: { $in: schedulings.map((s) => s._id) },
                userId: userId,
            })
                .session(session)
                .exec();

            // Create a set of schedule IDs where user is already enrolled
            const enrolledScheduleIds = new Set(existingEnrollments.map((e) => e.schedulingId.toString()));

            // Process each schedule
            for (const scheduling of schedulings) {
                const scheduleId = scheduling._id.toString();

                // Skip if user is already enrolled in this schedule (with any purchase)
                if (enrolledScheduleIds.has(scheduleId)) {
                    continue;
                }

                // Check if schedule has capacity
                if (scheduling.classCapacity) {
                    const totalEnrollments = await this.EnrollmentModel.countDocuments({
                        schedulingId: scheduling._id,
                    })
                        .session(session)
                        .exec();

                    if (totalEnrollments >= scheduling.classCapacity) {
                        continue;
                    }
                }

                // Check if schedule is in the future (optional validation)
                // const now = new Date();
                // const scheduleDateTime = new Date(scheduling.date);
                // if (scheduling.to) {
                //     const [hours, minutes] = scheduling.to.split(':');
                //     scheduleDateTime.setHours(parseInt(hours), parseInt(minutes));
                // }

                // if (scheduleDateTime < now) {
                //     continue;
                // }

                // Create new enrollment
                newEnrollments.push({
                    schedulingId: scheduling._id,
                    userId: userId,
                    packageId: packageId,
                    purchaseId: purchaseId,
                });
            }

            return newEnrollments;
        } catch (error) {
            console.error("Error in autoEnrollInCourseSchedules:", error);
            throw error;
        }
    }

    checkRequiredFields(billingDetails: any) {

        // Client Billing Details
        if (!billingDetails[0]?.clientDetails?.customerId) {
            throw new NotFoundException("Client's not found");
        }
        if (!billingDetails[0]?.clientDetails?.name) {
            throw new NotFoundException("Clients Billing name not found");
        }
        if (!billingDetails[0]?.clientDetails?.phone && !billingDetails[0]?.clientDetails?.email) {
            throw new NotFoundException("Client's phone or email not found");
        }
        if (!billingDetails[0]?.clientDetails?.stateId) {
            throw new NotFoundException("Client's state not found");
        }
        if (!billingDetails[0]?.clientDetails?.utCode) {
            throw new NotFoundException("Client's utCode not found");
        }


        // Facility Billing Details
        if (!billingDetails[0]?.billingDetails?.facilityName) {
            throw new NotFoundException("Facility Billing facility name not found");
        }
        if (!billingDetails[0]?.billingDetails?.billingName) {
            throw new NotFoundException("Facility Billing name not found");
        }
        if (!billingDetails[0]?.billingDetails?.addressLine1) {
            throw new NotFoundException("Facility Billing address line 1 not found");
        }
        if (!billingDetails[0]?.billingDetails?.cityId) {
            throw new NotFoundException("Facility Billing city not found");
        }
        if (!billingDetails[0]?.billingDetails?.stateId) {
            throw new NotFoundException("Facility Billing state not found");
        }
        if (!billingDetails[0]?.billingDetails?.email) {
            throw new NotFoundException("Facility Billing email not found");
        }
        if (!billingDetails[0]?.billingDetails?.phone) {
            throw new NotFoundException("Facility Billing phone not found");
        }
        if (!billingDetails[0]?.billingDetails?.utCode) {
            throw new NotFoundException("Facility Billing utCode not found");
        }
    }

    /**
     * @deprecated This is older version of purchase
     * @param createPurchaseDto
     * @param user
     * @returns
     */
    async createPurchase(createPurchaseDto: CreateInvoicePurchaseDto, user: IUserDocument): Promise<any> {
        const session = await this.InvoiceModel.db.startSession();
        session.startTransaction();
        try {
            const {
                organizationId,
                facilityId,
                cartDiscount,
                platform,
                paymentDetails,
                billingAddressId,
                isSplittedPayment,
                amountPaid,
                cartDiscountType,
                returnPurchaseIds,
                returnTotal,
            } = createPurchaseDto;
            let userId = new Types.ObjectId(createPurchaseDto.userId); // user will get package
            let billingUserId = new Types.ObjectId(createPurchaseDto.userId); // User to be billed

            const productsItem = (createPurchaseDto.productsItem || []) as {
                inventoryId: string;
                quantity: number;
            }[];
            let totalEffectiveReturnUnitPrice = 0;
            if (returnPurchaseIds && returnPurchaseIds.length > 0) {
                const returnPurchases = await this.PurchaseModel.aggregate([
                    {
                        $match: {
                            _id: { $in: returnPurchaseIds.map((id) => new Types.ObjectId(id)) },
                            organizationId: new Types.ObjectId(organizationId),
                            userId: new Types.ObjectId(userId),
                            isExpired: false,
                            endDate: { $gte: new Date() },
                            startDate: { $lte: new Date() },
                            isActive: { $ne: false },
                            bundledPricingId: { $exists: false },
                            isExchanged: { $ne: true },
                            exchangedInvoiceId: { $exists: false },
                            sharePass: { $ne: true },
                        },
                    },
                    {
                        $lookup: {
                            from: "invoices",
                            localField: "invoiceId",
                            foreignField: "_id",
                            as: "invoiceDetails",
                        },
                    },
                    {
                        $unwind: {
                            path: "$invoiceDetails",
                            preserveNullAndEmptyArrays: false,
                        },
                    },
                    {
                        $addFields: {
                            matchedPurchaseItem: {
                                $arrayElemAt: [
                                    {
                                        $filter: {
                                            input: "$invoiceDetails.purchaseItems",
                                            as: "item",
                                            cond: {
                                                $eq: ["$$item.packageId", "$packageId"],
                                            },
                                        },
                                    },
                                    0,
                                ],
                            },
                        },
                    },
                    {
                        $project: {
                            _id: 1,
                            invoiceId: 1,
                            "matchedPurchaseItem.unitPrice": 1,
                            "matchedPurchaseItem.quantity": 1,
                            "matchedPurchaseItem.discountExcludeCart": 1,
                            "matchedPurchaseItem.discountIncludeCart": 1,
                            paymentStatus: "$invoiceDetails.paymentStatus",
                        },
                    },
                ]).session(session);

                if (returnPurchases.length !== returnPurchaseIds.length) {
                    throw new BadRequestException("Some selected returns are not eligible (expired or inactive)");
                }

                totalEffectiveReturnUnitPrice = returnPurchases.reduce((total, purchase) => {
                    const item = purchase.matchedPurchaseItem;
                    const unitPrice = item?.unitPrice || 0;
                    const discountExcludeCart = Number(item?.discountExcludeCart) / (item?.quantity >= 1 ? item?.quantity : 1) || 0;
                    const discountIncludeCart = Number(item?.discountIncludeCart) / (item?.quantity >= 1 ? item?.quantity : 1) || 0;

                    const effectiveUnitPrice = purchase.paymentStatus === PaymentStatus.COMPLETED ? unitPrice - (discountExcludeCart + discountIncludeCart) : 0;
                    return total + effectiveUnitPrice;
                }, 0);
            }
            const purchaseItems = (createPurchaseDto.purchaseItems || []) as {
                packageId: string;
                quantity: number;
                isBundledPricing?: boolean;
                bundledPricingId?: string;
            }[];

            const customPackageItems = (createPurchaseDto.customPackageItems || []) as {
                customPackageId: string;
                quantity: number;
            }[];

            const paymentDetailsData = paymentDetails?.map((detail: any) => ({
                paymentMethod: detail.paymentMethod,
                paymentMethodId: detail.paymentMethodId,
                transactionId: detail.transactionId || "",
                amount: detail.amount,
                paymentDate: detail.paymentDate,
                paymentStatus: detail.paymentStatus,
                paymentGateway: detail.paymentGateway || "",
                description: detail.description,
                denominations: detail.paymentMethod === PaymentMethod.CASH ? detail.denominations || {} : {},
            }));
            let membershipId = "";
            let newPurchaseItems = [];

            if ((!purchaseItems || purchaseItems.length === 0) && (!productsItem || productsItem.length === 0) && (!customPackageItems || customPackageItems.length === 0)) {
                throw new BadRequestException("Purchase/Product items are required");
            }

            if (!userId) {
                throw new BadRequestException("User is required");
            }

            const clientUser = await this.UserModel.findOne({ _id: userId }).exec();
            if (!clientUser) {
                throw new NotFoundException("User not found");
            }
            const client = await this.ClientModel.findOne({ userId: clientUser._id }).exec();
            billingUserId = clientUser.parent ?? billingUserId;

            let pipeline = this.usersPipe.billingDetails(billingUserId.toString(), facilityId);
            const billingDetails = await this.UserModel.aggregate(pipeline).exec();

            if (!billingDetails || billingDetails.length === 0 || !billingAddressId) {
                throw new NotFoundException("Insufficient data for billing details");
            }
            let clientBillingDetails = billingDetails[0]?.clientDetails;
            let isForBusiness = false;
            if (billingDetails[0].clientBusinessDetails && billingDetails[0].clientBusinessDetails?._id?.toString() === billingAddressId) {
                clientBillingDetails = billingDetails[0].clientBusinessDetails;
                isForBusiness = true;
                if (!clientBillingDetails.gstNumber) throw new BadRequestException("Please add GST number first for business related purchases");
            }

            this.checkRequiredFields([
                {
                    clientDetails: clientBillingDetails,
                    billingDetails: billingDetails[0]?.billingDetails,
                },
            ]);
            let productBilling: any = {};
            let customPackageBilling: any = {};
            const packageIds = purchaseItems.map((item) => new Types.ObjectId(item.packageId));
            let checkValidPackage = await this.PricingModel.find({ _id: { $in: packageIds }, isActive: true })
                .lean()
                .exec();
            const productId = productsItem.map((item: any) => new Types.ObjectId(item.inventoryId));
            let checkProductInInventory = await this.InventoryModel.find({ _id: { $in: productId } })
                .lean()
                .exec();
            const customPackageIds = customPackageItems.map((item) => new Types.ObjectId(item.customPackageId));
            let checkValidCustomPackage = await this.customPackageModel
                .find({ _id: { $in: customPackageIds }, isActive: true })
                .lean()
                .exec();
            // 1. Get subTotal of products and packages BEFORE discount
            // ✅ Calculate actual post-item-discount effective subtotals
            let productEffectiveTotal = 0;
            let packageEffectiveTotal = 0;
            let customPackageEffectiveTotal = 0;

            for (const item of productsItem) {
                const inventory = checkProductInInventory.find((p) => p._id.toString() === item.inventoryId.toString());
                const unitPrice = Number(inventory.salePrice || 0);
                const discount = (Number(inventory.discount || 0) / 100) * unitPrice;
                const effective = unitPrice - discount;
                productEffectiveTotal += effective * Number(item.quantity || 1);
            }

            for (const item of purchaseItems) {
                const pkg = checkValidPackage.find((p) => p._id.toString() === item.packageId.toString());
                const unitPrice = Number(pkg?.price || 0);
                let discount = 0;

                if (pkg?.discount?.type === DiscountType.FLAT) {
                    discount = Number(pkg.discount.value || 0);
                } else if (pkg?.discount?.type === DiscountType.PERCENTAGE) {
                    discount = (Number(pkg.discount.value || 0) / 100) * unitPrice;
                }

                const effective = unitPrice - discount;
                packageEffectiveTotal += effective * Number(item.quantity || 1);
            }

            for (const item of customPackageItems) {
                const pkg = checkValidCustomPackage.find((p) => p._id.toString() === item.customPackageId.toString());
                const unitPrice = Number(pkg?.unitPrice || 0);
                let discount = 0;

                if (pkg?.discount?.type === DiscountType.FLAT) {
                    discount = Number(pkg.discount.value || 0);
                } else if (pkg?.discount?.type === DiscountType.PERCENTAGE) {
                    discount = (Number(pkg.discount.value || 0) / 100) * unitPrice;
                }

                const effective = unitPrice - discount;
                customPackageEffectiveTotal += effective * Number(item.quantity || 1);
            }

            const totalEffective = productEffectiveTotal + packageEffectiveTotal + customPackageEffectiveTotal;

            // ✅ Calculate proportional cart discount
            let productCartDiscount = 0;
            let packageCartDiscount = 0;
            let customPackageCartDiscount = 0;

            // ✅ Calculate cart discount considering return packages
            let effectiveCartDiscount = cartDiscount || 0;
            let effectiveCartDiscountType = cartDiscountType;
            let returnFlatDiscount = 0;

            if (totalEffectiveReturnUnitPrice > 0) {
                if (cartDiscountType === DiscountType.FLAT) {
                    effectiveCartDiscount += totalEffectiveReturnUnitPrice;
                } else {
                    returnFlatDiscount = totalEffectiveReturnUnitPrice;
                }
            }

            if (effectiveCartDiscountType === DiscountType.FLAT && totalEffective > 0) {
                productCartDiscount = (productEffectiveTotal / totalEffective) * effectiveCartDiscount;
                packageCartDiscount = (packageEffectiveTotal / totalEffective) * effectiveCartDiscount;
                customPackageCartDiscount = (customPackageEffectiveTotal / totalEffective) * effectiveCartDiscount;
            } else if (effectiveCartDiscountType === DiscountType.PERCENTAGE) {
                productCartDiscount = effectiveCartDiscount;
                packageCartDiscount = effectiveCartDiscount;
                customPackageCartDiscount = effectiveCartDiscount;
            }

            if (returnFlatDiscount > 0 && totalEffective > 0) {
                productCartDiscount += (productEffectiveTotal / totalEffective) * returnFlatDiscount;
                packageCartDiscount += (packageEffectiveTotal / totalEffective) * returnFlatDiscount;
                customPackageCartDiscount += (customPackageEffectiveTotal / totalEffective) * returnFlatDiscount;
            }

            if (checkProductInInventory.length !== productId.length && productsItem.length > 0) {
                throw new NotFoundException("Some Product are invalid");
            } else if (checkProductInInventory.length === productId.length && productsItem.length > 0) {
                productBilling = await this.purchaseProduct(productsItem, productCartDiscount, effectiveCartDiscountType, productEffectiveTotal);
            }
            if (customPackageItems.length > 0 && checkValidCustomPackage.length !== customPackageIds.length) {
                throw new NotFoundException("Some custom package are invalid");
            } else if (customPackageItems.length > 0 && checkValidCustomPackage.length === customPackageIds.length) {
                customPackageBilling = await this.purchaseCustomPackage(
                    customPackageItems,
                    customPackageCartDiscount,
                    effectiveCartDiscountType,
                    customPackageEffectiveTotal,
                    checkValidCustomPackage,
                );
            }
            if (checkValidPackage.length !== purchaseItems.length) {
                throw new NotFoundException("Some packages are invalid or inactive");
            }

            let bundledPackageIds = purchaseItems.filter((item) => item?.isBundledPricing == true).map((item) => new Types.ObjectId(item.packageId));

            if (bundledPackageIds?.length > 0) {
                for (const item of purchaseItems) {
                    if (bundledPackageIds.toString().includes(item.packageId.toString())) {
                        let packageDetails = checkValidPackage.find((pkg) => pkg._id.toString() === item.packageId.toString());
                        const checkBundledPackage = await this.PricingModel.find({
                            _id: { $in: packageDetails.pricingIds.map((id) => new Types.ObjectId(id)) },
                            isActive: true,
                        }).exec();
                        if (checkBundledPackage.length !== packageDetails.pricingIds.length) {
                            throw new NotFoundException("Some packages are invalid or inactive in bundled pricing");
                        }
                        checkValidPackage = [...checkValidPackage, ...checkBundledPackage];
                        packageDetails.pricingIds.map((id) => {
                            newPurchaseItems.push({
                                packageId: id,
                                quantity: item.quantity,
                                isBundledPricing: true,
                                bundledPricingId: item.packageId,
                            });
                        });
                    } else {
                        newPurchaseItems.push({
                            packageId: item.packageId,
                            quantity: item.quantity,
                            isBundledPricing: false,
                        });
                    }
                }
            } else {
                newPurchaseItems = [...newPurchaseItems, ...purchaseItems];
            }

            // let pipeline = this.usersPipe.billingDetails(userId, facilityId);
            // const billingDetails = await this.UserModel.aggregate(pipeline).exec();

            if (!billingDetails || billingDetails.length === 0 || !billingAddressId) {
                throw new NotFoundException("Insufficient data for billing details");
            }

            const highestInvoice = await this.InvoiceModel.findOne({ organizationId, facilityId }, { invoiceNumber: 1 }).sort({ invoiceNumber: -1 }).lean();
            const invoiceNumber = highestInvoice ? highestInvoice.invoiceNumber + 1 : 1;

            const highestOrderId = await this.InvoiceModel.findOne({ organizationId, facilityId }, { orderId: 1 }).sort({ orderId: -1 }).lean();
            const orderId = highestOrderId?.orderId ? highestOrderId?.orderId + 1 : 1;
            let invoiceSubTotal = 0;
            let invoiveDiscountExculdeCart = 0;
            let invoiveDiscountIncludeCart = 0;
            let invoiceGstAmount = 0;
            let invoiceAmountAfterGst = 0;
            let roundOff: any;
            let grandTotal = 0;
            let totalFlatCartDiscountAllocated = 0;
            for (let i = 0; i < purchaseItems.length; i++) {
                const item = purchaseItems[i];
                const packageDetails = checkValidPackage.find((pkg) => pkg._id.toString() === item.packageId.toString());
                if (!packageDetails) {
                    throw new NotFoundException(`Package with ID ${item.packageId} not found`);
                }
                const { durationUnit, expiredInDays: expireIn } = packageDetails;

                if (!durationUnit || expireIn == null) {
                    throw new Error(`Invalid duration unit or expiration days in package ${item.packageId}`);
                }
                // const startDate = new Date();
                // const endDate = new Date(startDate);
                const startDate = moment().startOf("day").toDate();
                const endDate = moment().startOf("day").toDate();

                switch (durationUnit) {
                    case DurationUnit.DAYS:
                        endDate.setDate(endDate.getDate() + expireIn);
                        break;
                    case DurationUnit.MONTHS:
                        endDate.setMonth(endDate.getMonth() + expireIn);
                        break;
                    case DurationUnit.YEARS:
                        endDate.setFullYear(endDate.getFullYear() + expireIn);
                        break;
                    default:
                        throw new Error(`Invalid duration unit: ${durationUnit}`);
                }
                let discountExcludeCart = 0;
                let discountIncludeCart = 0;
                let gstAmount = 0;
                let totalAmountExcludeCartDiscount = 0;

                if (packageDetails?.discount?.type === DiscountType.PERCENTAGE) {
                    discountExcludeCart = Number(item.quantity) * Number(((Number(packageDetails?.discount?.value) / 100) * Number(packageDetails?.price)).toFixed(2));
                } else if (packageDetails?.discount?.type === DiscountType.FLAT) {
                    discountExcludeCart = Number(item.quantity) * Number(Number(packageDetails?.discount?.value).toFixed(2));
                }

                const totalUnitPrice = Number(item.quantity) * Number(packageDetails?.price);
                invoiceSubTotal += totalUnitPrice;
                invoiveDiscountExculdeCart += discountExcludeCart;
                totalAmountExcludeCartDiscount = totalUnitPrice - discountExcludeCart;
                if (effectiveCartDiscount && effectiveCartDiscountType === DiscountType.PERCENTAGE) {
                    discountIncludeCart = Number(((Number(effectiveCartDiscount) / 100) * Number(totalAmountExcludeCartDiscount)).toFixed(2));
                    gstAmount = Number((totalUnitPrice - (discountExcludeCart + discountIncludeCart)) * (Number(packageDetails?.tax) / 100));
                    invoiceGstAmount += gstAmount;
                    invoiveDiscountIncludeCart += discountIncludeCart;
                } else if (packageCartDiscount && effectiveCartDiscountType === DiscountType.FLAT) {
                    const denominator = productEffectiveTotal + packageEffectiveTotal;
                    const itemShare = denominator > 0 ? totalAmountExcludeCartDiscount / denominator : 0;
                    // Handle last item for accurate rounding
                    if (i === purchaseItems.length - 1) {
                        discountIncludeCart = Number((Number(packageCartDiscount) - totalFlatCartDiscountAllocated).toFixed(2));
                    } else {
                        discountIncludeCart = Number((itemShare * Number(packageCartDiscount)).toFixed(2));
                        totalFlatCartDiscountAllocated += discountIncludeCart;
                    }

                    invoiveDiscountIncludeCart += discountIncludeCart;
                    gstAmount = Number((totalUnitPrice - (discountExcludeCart + discountIncludeCart)) * (Number(packageDetails?.tax) / 100));
                    invoiceGstAmount += gstAmount;
                } else {
                    gstAmount = Number((totalAmountExcludeCartDiscount * (Number(packageDetails?.tax) / 100)).toFixed(2));
                    invoiceGstAmount += gstAmount;
                }
                item["packageName"] = packageDetails?.name;
                item["expireIn"] = packageDetails?.expiredInDays;
                item["durationUnit"] = packageDetails?.durationUnit;
                item["startDate"] = startDate;
                item["endDate"] = endDate;
                item["unitPrice"] = packageDetails?.price.toFixed(2);
                item["discountType"] = packageDetails?.discount?.type;
                item["discountValue"] = packageDetails?.discount?.value;
                item["discountExcludeCart"] = discountExcludeCart?.toFixed(2);
                item["discountIncludeCart"] = discountIncludeCart?.toFixed(2);
                item["hsnOrSacCode"] = packageDetails?.hsnOrSacCode;
                item["tax"] = packageDetails?.tax;
                item["gstAmount"] = gstAmount.toFixed(2) || 0;
            }

            invoiceAmountAfterGst = invoiceSubTotal + invoiceGstAmount - (invoiveDiscountIncludeCart + invoiveDiscountExculdeCart) || 0;
            if (checkProductInInventory.length === productId.length && productsItem.length > 0) {
                invoiceSubTotal += productBilling?.invoiceSubTotal;
                invoiveDiscountExculdeCart += productBilling?.invoiveDiscountExculdeCart;
                invoiveDiscountIncludeCart += productBilling?.invoiveDiscountIncludeCart;
                invoiceGstAmount += productBilling?.invoiceGstAmount;
                invoiceAmountAfterGst += productBilling?.invoiceAmountAfterGst;
            }
            if (customPackageItems.length > 0) {
                invoiceSubTotal += customPackageBilling?.invoiceSubTotal;
                invoiveDiscountExculdeCart += customPackageBilling?.invoiveDiscountExculdeCart;
                invoiveDiscountIncludeCart += customPackageBilling?.invoiveDiscountIncludeCart;
                invoiceGstAmount += customPackageBilling?.invoiceGstAmount;
                invoiceAmountAfterGst += customPackageBilling?.invoiceAmountAfterGst;
            }
            roundOff = (invoiceAmountAfterGst - Math.floor(invoiceAmountAfterGst)).toFixed(2);
            grandTotal = Math.floor(invoiceAmountAfterGst);
            const invoice = new this.InvoiceModel({
                createdBy: user._id,
                invoiceNumber,
                orderId,
                userId: billingUserId,
                organizationId,
                facilityId,
                purchaseItems,
                productItem: productBilling?.productDetail,
                customPackageItems: customPackageBilling?.productDetail,
                subTotal: invoiceSubTotal?.toFixed(2),
                discount: invoiveDiscountExculdeCart?.toFixed(2),
                cartDiscount: cartDiscount || 0,
                cartDiscountType: cartDiscountType,
                returnDiscount: totalEffectiveReturnUnitPrice || 0,
                cartDiscountAmount: invoiveDiscountIncludeCart?.toFixed(2) || 0,
                totalGstValue: invoiceGstAmount?.toFixed(2) || 0,
                totalAmountAfterGst: invoiceAmountAfterGst.toFixed(2),
                roundOff: roundOff,
                grandTotal: grandTotal.toFixed(2),
                amountInWords: this.convertAmountToWords(Math.floor(grandTotal)),
                invoiceDate: new Date(),
                platform,
                paymentStatus: paymentDetailsData[0].paymentStatus,
                paymentDetails: paymentDetailsData,
                isSplittedPayment,
                amountPaid,
                clientDetails: {
                    _id: client?.userId || "",
                    customerId: client?.clientId || "",
                    name: clientUser.name || "",
                    email: billingDetails[0]?.clientDetails?.email || "",
                    phone: billingDetails[0]?.clientDetails?.phone || "",
                },
                clientBillingDetails: {
                    _id: clientBillingDetails?._id || "",
                    customerId: clientBillingDetails?.customerId || "",
                    name: clientBillingDetails?.name || "",
                    addressLine1: clientBillingDetails?.addressLine1 || "",
                    addressLine2: clientBillingDetails?.addressLine2 || "",
                    postalCode: clientBillingDetails?.postalCode || "",
                    cityId: new Types.ObjectId(clientBillingDetails?.cityId) || "",
                    cityName: clientBillingDetails?.cityName || "",
                    stateId: new Types.ObjectId(clientBillingDetails?.stateId) || "",
                    stateName: clientBillingDetails?.stateName || "",
                    gstNumber: clientBillingDetails?.gstNumber,
                    email: clientBillingDetails?.email || "",
                    phone: clientBillingDetails?.phone || "",
                    utCode: clientBillingDetails?.utCode || "",
                },
                billingDetails: {
                    facilityName: billingDetails[0]?.billingDetails?.facilityName || "",
                    billingName: billingDetails[0]?.billingDetails?.billingName || "",
                    gstNumber: billingDetails[0]?.billingDetails?.gstNumber || "",
                    email: billingDetails[0]?.billingDetails?.email || "",
                    phone: billingDetails[0]?.billingDetails?.phone || "",
                    addressLine1: billingDetails[0]?.billingDetails?.addressLine1 || "",
                    addressLine2: billingDetails[0]?.billingDetails?.addressLine2 || "",
                    postalCode: billingDetails[0]?.billingDetails?.postalCode || "",
                    cityId: new Types.ObjectId(billingDetails[0]?.billingDetails?.cityId) || "",
                    cityName: billingDetails[0]?.billingDetails?.cityName || "",
                    stateId: new Types.ObjectId(billingDetails[0]?.billingDetails?.stateId) || "",
                    stateName: billingDetails[0]?.billingDetails?.stateName || "",
                    utCode: billingDetails[0]?.billingDetails?.utCode || "",
                },
                isForBusiness,
            });

            const savedInvoice = await invoice.save({ session });
            const invoiceId = savedInvoice._id;

            if (returnPurchaseIds && returnPurchaseIds.length > 0) {
                await this.PurchaseModel.updateMany(
                    { _id: { $in: returnPurchaseIds.map((id) => new Types.ObjectId(id)) } },
                    {
                        $set: {
                            isActive: false,
                            isExchanged: true,
                            exchangedInvoiceId: invoiceId,
                            exchangeDate: new Date(),
                        },
                    },
                    { session },
                );

                savedInvoice.returnDetails = {
                    returnPurchaseIds,
                    returnTotal: totalEffectiveReturnUnitPrice,
                    exchangedOn: new Date(),
                };
                await savedInvoice.save({ session });
            }

            const purchases = [];
            const newEnrollments = [];
            for (const item of newPurchaseItems) {
                const packageDetails = checkValidPackage.find((pkg) => pkg._id.toString() === item.packageId.toString());
                if (!packageDetails) {
                    throw new NotFoundException(`Package with ID ${item.packageId} not found`);
                }
                if (packageDetails?.membershipId) {
                    membershipId = packageDetails?.membershipId || "";
                }

                for (let i = 0; i < item.quantity; i++) {
                    const { durationUnit, expiredInDays: expireIn } = packageDetails;
                    if (!durationUnit || expireIn == null) {
                        throw new Error(`Invalid duration unit or expiration days in package ${item.packageId}`);
                    }

                    const startDate = moment().startOf("day").toDate();
                    let endDate = new Date(startDate);
                    switch (durationUnit) {
                        case DurationUnit.DAYS:
                            endDate.setDate(endDate.getDate() + expireIn);
                            break;
                        case DurationUnit.MONTHS:
                            endDate.setMonth(endDate.getMonth() + expireIn);
                            break;
                        case DurationUnit.YEARS:
                            endDate.setFullYear(endDate.getFullYear() + expireIn);
                            break;
                        default:
                            throw new Error(`Invalid duration unit: ${durationUnit}`);
                    }
                    endDate = moment(endDate).endOf("day").toDate();

                    // Temporarily push without QR and insert first to get _id
                    const purchaseDoc = new this.PurchaseModel({
                        invoiceId,
                        packageId: item.packageId,
                        userId: userId,
                        sponsorUser: userId.toString() === billingUserId.toString() ? null : billingUserId,
                        organizationId,
                        facilityId,
                        ...(item?.isBundledPricing == true && {
                            bundledPricingId: item.bundledPricingId,
                        }),
                        purchasedBy: clientUser._id,
                        membershipId: packageDetails?.membershipId || null,
                        purchaseDate: new Date(),
                        paymentStatus: paymentDetailsData[0].paymentStatus,
                        isExpired: false,
                        sessionType: packageDetails.services.sessionType,
                        ...([SessionType.UNLIMITED, SessionType.DAY_PASS].includes(packageDetails.services.sessionType) && {
                            sessionPerDay: packageDetails.services.sessionPerDay,
                        }),
                        ...(packageDetails.services.sessionType === SessionType.DAY_PASS && {
                            dayPassLimit: packageDetails.services.dayPassLimit,
                        }),
                        totalSessions: packageDetails.services.sessionType === SessionType.SINGLE ? 1 : packageDetails.services.sessionCount,
                        sessionConsumed: 0,
                        startDate,
                        endDate,
                    });

                    // Save to generate _id
                    await purchaseDoc.save({ session });
                    // Auto enroll in course schedules if this is a course package
                    if (packageDetails?.services?.type === "courses") {
                        const courseEnrollments = await this.autoEnrollInCourseSchedules(
                            new Types.ObjectId(userId),
                            item.packageId,
                            purchaseDoc._id,
                            new Types.ObjectId(organizationId),
                            session,
                        );
                        newEnrollments.push(...courseEnrollments);
                    }

                    // Generate QR Code with purchase_id
                    // const qrUrl = `https://example.com?purchase_id=${purchaseDoc._id}`;
                    // const qrCodeBuffer = await QRCode.toBuffer(qrUrl, { type: 'png' });

                    const qrData = JSON.stringify({ purchase_id: purchaseDoc._id });
                    const qrCodeBuffer = await QRCode.toBuffer(qrData, { type: "png" });

                    // Upload to S3
                    const s3Result = await this.uploadService.upload(qrCodeBuffer, "purchase-qr/", `purchase-${purchaseDoc._id}.png`);

                    // Update QR URL in doc
                    purchaseDoc.qrCodeUrl = s3Result?.Location || "";
                    await purchaseDoc.save({ session });
                }
            }
            if (newEnrollments.length) await this.EnrollmentModel.insertMany(newEnrollments);

            // if (purchases.length > 0) {
            //     await this.PurchaseModel.insertMany(purchases, { session });
            // }
            if (membershipId != "") {
                const clientProfile = await this.ClientModel.findOne({ userId: userId });
                if (!clientProfile?.membershipId) {
                    let checkCounter = await this.MembershipModel.findById(membershipId);

                    if (!checkCounter?.lastcounter || checkCounter?.lastcounter == 0) {
                        await this.ClientModel.findOneAndUpdate({ userId: userId }, { membershipId: `${checkCounter.prefix}-${checkCounter.counter}`.toString() }, { session });
                        await this.MembershipModel.findByIdAndUpdate(membershipId, { lastcounter: checkCounter.counter + 1 }, { session });
                    } else {
                        await this.ClientModel.findOneAndUpdate({ userId: userId }, { membershipId: `${checkCounter.prefix}-${checkCounter.lastcounter}` }, { session });
                        await this.MembershipModel.findByIdAndUpdate(membershipId, { lastcounter: checkCounter.lastcounter + 1 }, { session });
                    }
                }
            }

            await session.commitTransaction();

            if (paymentDetailsData[0].paymentStatus !== PaymentStatus.PENDING) {
                const organization = await this.UserModel.findById(organizationId).select("email");
                const organizationEmail = organization?.email || "";
                await this.invoiceService.generateInvoice(invoice, organizationEmail);
            }
            return { message: "Purchase created successfully", invoiceId, purchaseCount: purchases.length };
        } catch (error) {
            await session.abortTransaction();
            throw new BadRequestException(error.message); // ✅ This will return HTTP 400
        } finally {
            session.endSession(); // Ensure session is always closed
        }
    }

    /**
     * This is new version(v2) of purchase
     * @param purchaseRequest
     * @param user
     * @returns
     */
    async processPurchase(purchaseRequest: PurchaseRequestDto, user: any): Promise<PurchaseResponseDto> {
        const session = await this.transactionService.startTransaction();

        // Declare variables that need to be accessed in finally block
        let savedInvoice: any = null;
        let invoiceId: any = null;
        let organizationId: string = "";
        let paymentDetailsData: any[] = [];
        let billingUserId: IDatabaseObjectId = null;
        let paymentLinkUrl: string = "";
        let paymentLinkQRCodeBuffer: Buffer = null;
        let paymentLinkId: string = "";
        let client: any = null;

        try {
            // Re-validate the cart
            const { cart, paymentDetails, isSplittedPayment, amountPaid, platform, billingAddressId, date, returnItems: returnPurchaseIds, isCashPayment } = purchaseRequest;

            // Re-validate the cart (including return purchases if provided)
            const validatedCart = await this.cartService.processCart(purchaseRequest.cart, false);

            // Check if the cart has validation errors
            if (validatedCart.validationErrors && validatedCart.validationErrors.length > 0) {
                throw new BadRequestException(`Cart validation failed: ${validatedCart.validationErrors.join(", ")}`);
            }

            // Send error if after applying the voucher final payment cannot go in minus
            if (validatedCart.totalAmountAfterGst < 0) {
                throw new BadRequestException(`Payment amount cannot be negative. Add more items to cart`);
            }

            const { facilityId, userId } = cart;
            // Update organizationId for use in finally block
            organizationId = cart.organizationId;

            // Format payment details
            paymentDetailsData = paymentDetails.map((detail) => ({
                paymentMethod: detail.paymentMethod,
                paymentMethodId: detail.paymentMethodId || "",
                transactionId: detail.transactionId || "",
                amount: detail.amount,
                paymentDate: detail.paymentDate,
                paymentStatus: detail.paymentStatus,
                paymentGateway: detail.paymentGateway || "",
                description: detail.description || "",
                denominations: detail.denominations || {},
            }));
            let tenderedAmount = 0;
            let changeDue = 0;
            if (isSplittedPayment) {
                changeDue = 0;
                tenderedAmount = paymentDetails.reduce((sum, p) => sum + p.amount, 0);
                if (amountPaid < tenderedAmount) {
                    changeDue = tenderedAmount - amountPaid;
                }
            } else if (isCashPayment) {
                tenderedAmount = Object.entries(paymentDetails[0].denominations || {}).reduce((total, [denom, count]) => total + Number(denom) * count, 0);

                changeDue = tenderedAmount - amountPaid;
            }
            client = await this.UserModel.findOne({ _id: userId }).exec();
            billingUserId = client.parent ?? new Types.ObjectId(userId);

            // Get billing details
            const pipeline = this.usersPipe.billingDetails(billingUserId as any, facilityId);
            const billingDetails = await this.UserModel.aggregate(pipeline).exec();

            if (!billingDetails || billingDetails.length === 0 || !billingAddressId) {
                throw new NotFoundException("Insufficient data for billing details");
            }

            let clientBillingDetails = billingDetails[0]?.clientDetails;
            let isForBusiness = false;

            if (billingDetails[0].clientBusinessDetails && billingDetails[0].clientBusinessDetails?._id?.toString() === billingAddressId) {
                clientBillingDetails = billingDetails[0].clientBusinessDetails;
                isForBusiness = true;
                if (!clientBillingDetails.gstNumber) {
                    throw new BadRequestException("Please add GST number first for business related purchases");
                }
            }

            // Check required fields
            this.checkRequiredFields([
                {
                    clientDetails: clientBillingDetails,
                    billingDetails: billingDetails[0]?.billingDetails,
                },
            ]);

            // Prepare purchase items
            const purchaseItems = [];
            const productItems = [];
            const customPackageItems = [];
            const returnPackageItems = [];

            // Process each item based on its type
            for (const item of validatedCart.items) {
                switch (item.itemType) {
                    case ENUM_PRODUCT_ITEM_TYPE.SERVICE:
                        const service = await this.PricingModel.findOne({
                            _id: new Types.ObjectId(item._id),
                            isActive: true,
                        }).lean();

                        if (!service) {
                            throw new NotFoundException(`Service with ID ${item._id} not found or inactive`);
                        }

                        // Check if this is a bundled pricing package
                        if (service.isBundledPricing) {
                            // Validate all bundled packages are active
                            if (!service.pricingIds || service.pricingIds.length === 0) {
                                throw new BadRequestException(`Bundled pricing package ${item._id} has no associated packages`);
                            }

                            const checkBundledPackage = await this.PricingModel.find({
                                _id: { $in: service.pricingIds.map((id) => new Types.ObjectId(id)) },
                                isActive: true,
                            }).exec();

                            if (checkBundledPackage.length !== service.pricingIds.length) {
                                throw new NotFoundException("Some packages are invalid or inactive in bundled pricing");
                            }
                        }
                        purchaseItems.push({
                            packageId: item._id,
                            purchaseIds: [],
                            packageName: item.name,
                            quantity: item.quantity,
                            isBundledPricing: service.isBundledPricing || false,
                            expireIn: service.expiredInDays,
                            durationUnit: service.durationUnit,
                            startDate: date ? new Date(date) : new Date(),
                            endDate: this.calculateEndDate(date ? new Date(date) : new Date(), service.expiredInDays, service.durationUnit),
                            // unitPrice: item?.price?.toFixed(2),
                            price: item.price,
                            unitPrice: item.unitPrice,
                            discountType: item.discountType ? this.mapPromotionTypeToDiscountType(item.discountType as any) : undefined,
                            discountValue: item.discountValue || 0,
                            discountedBy: item?.discountedBy,
                            discountExcludeCart: item?.discountAmount?.toFixed(2) || 0,
                            discountIncludeCart: item.cartDiscountAmount || 0, // Will be calculated later
                            //returnDiscountAmount: item.returnDiscountAmount || 0,
                            //voucherDiscountAmount: item.voucherDiscountAmount || 0,
                            totalDiscountAmount: item.totalDiscountAmount || 0,
                            hsnOrSacCode: item.hsnOrSacCode,
                            tax: item.taxRate,
                            gstAmount: item?.taxAmount?.toFixed(2),
                            promotionLabel: item?.promotionLabel,
                            promotionLabelKey: item?.promotionLabelKey,
                        });
                        break;

                    case ENUM_PRODUCT_ITEM_TYPE.VOUCHER:
                        const voucher = await this.PricingModel.findOne({
                            itemType: ENUM_PRODUCT_ITEM_TYPE.VOUCHER,
                            _id: new Types.ObjectId(item._id),
                            isActive: true,
                        }).lean();

                        if (!voucher) {
                            throw new NotFoundException(`voucher with ID ${item._id} not found or inactive`);
                        }

                        // Check if this is a bundled pricing package
                        if (voucher.isBundledPricing) {
                            // Validate all bundled packages are active
                            if (!voucher.pricingIds || voucher.pricingIds.length === 0) {
                                throw new BadRequestException(`Bundled pricing package ${item._id} has no associated packages`);
                            }

                            const checkBundledPackage = await this.PricingModel.find({
                                _id: { $in: voucher.pricingIds.map((id) => new Types.ObjectId(id)) },
                                isActive: true,
                            }).exec();

                            if (checkBundledPackage.length !== voucher.pricingIds.length) {
                                throw new NotFoundException("Some packages are invalid or inactive in bundled pricing");
                            }
                        }
                        purchaseItems.push({
                            packageId: item._id,
                            purchaseIds: [],
                            packageName: item.name,
                            quantity: item.quantity,
                            isBundledPricing: voucher.isBundledPricing || false,
                            expireIn: voucher.expiredInDays,
                            durationUnit: voucher.durationUnit,
                            startDate: date ? new Date(date) : new Date(),
                            endDate: this.calculateEndDate(date ? new Date(date) : new Date(), voucher.expiredInDays, DurationUnit.DAYS),
                            // unitPrice: item?.price?.toFixed(2),
                            price: item.price,
                            unitPrice: item.unitPrice, // unit price without gst
                            discountType: item.discountType ? this.mapPromotionTypeToDiscountType(item.discountType as any) : undefined,
                            discountValue: item.discountValue || 0,
                            discountedBy: item?.discountedBy,
                            discountExcludeCart: item?.discountAmount?.toFixed(2) || 0,
                            discountIncludeCart: item.cartDiscountAmount || 0, // Will be calculated later
                            //returnDiscountAmount: item.returnDiscountAmount || 0,
                            //voucherDiscountAmount: item.voucherDiscountAmount || 0,
                            totalDiscountAmount: item.totalDiscountAmount || 0,
                            hsnOrSacCode: item.hsnOrSacCode,
                            tax: item.taxRate,
                            gstAmount: item?.taxAmount?.toFixed(2),
                            promotionLabel: item?.promotionLabel,
                            promotionLabelKey: item?.promotionLabelKey,
                        });
                        break;

                    case ENUM_PRODUCT_ITEM_TYPE.PRODUCT:
                        const inventory: any = await this.InventoryModel.findOne({
                            productId: new Types.ObjectId(item._id),
                        }).populate({
                            path: "productId",
                            select: "name",
                        });

                        if (!inventory) {
                            throw new NotFoundException(`Product not found`);
                        }

                        if (inventory.quantity < item.quantity) {
                            throw new BadRequestException(`Insufficient stock for product ${inventory?.productId?.name}`);
                        }

                        productItems.push({
                            inventoryId: item._id,
                            purchaseIds: [],
                            productId: inventory.productId,
                            productVariantId: item.variantId || inventory.productVariantId,
                            productName: item.name,
                            quantity: item.quantity,
                            salePrice: item.price,
                            // mrp: inventory.mrp,
                            price: item.price,
                            unitPrice: item.unitPrice,
                            // mrp: item.discountedPrice + (item.discountAmount || 0) + (item.cartDiscountAmount || 0) + (item.returnDiscountAmount || 0),
                            finalPrice: item.finalPrice / item.quantity,
                            discountType: item.discountType ? this.mapPromotionTypeToDiscountType(item.discountType as any) : undefined,
                            discountedBy: item.discountedBy,
                            discountValue: item.discountValue || 0,
                            discountExcludeCart: item.discountAmount || 0,
                            discountIncludeCart: item.cartDiscountAmount || 0, // Will be calculated later
                            //returnDiscountAmount: item.returnDiscountAmount || 0,
                            //voucherDiscountAmount: item.voucherDiscountAmount || 0,
                            totalDiscountAmount: item.totalDiscountAmount || 0,
                            hsnOrSacCode: item.hsnOrSacCode,
                            tax: item.taxRate,
                            gstAmount: item.taxAmount,
                        });
                        break;

                    case ENUM_PRODUCT_ITEM_TYPE.CUSTOM_PACKAGE:
                        const customPackage = await this.customPackageModel.findOne({
                            _id: new Types.ObjectId(item._id),
                            isActive: true,
                        });

                        if (!customPackage) {
                            throw new NotFoundException(`Custom package with ID ${item._id} not found or inactive`);
                        }

                        customPackageItems.push({
                            customPackageId: item._id,
                            purchaseIds: [],
                            packageName: item.name,
                            quantity: item.quantity,
                            // unitPrice: item.price,
                            price: item.price,
                            unitPrice: item.unitPrice,
                            discountType: item.discountType ? this.mapPromotionTypeToDiscountType(item.discountType as any) : undefined,
                            discountValue: item.discountValue || 0,
                            discountedBy: item.discountedBy,
                            discountExcludeCart: item.discountAmount || 0,
                            discountIncludeCart: item.cartDiscountAmount || 0, // Will be calculated later
                            //returnDiscountAmount: item.returnDiscountAmount || 0,
                            //voucherDiscountAmount: item.voucherDiscountAmount || 0,
                            totalDiscountAmount: item.totalDiscountAmount || 0,
                            hsnOrSacCode: item.hsnOrSacCode,
                            tax: item.taxRate,
                            gstAmount: item.taxAmount,
                        });
                        break;
                }
            }
            if (validatedCart?.returnItems.length > 0) {
                for (const item of validatedCart.returnItems) {
                    returnPackageItems.push({
                        invoiceId: item?.invoiceId,
                        packageId: item?._id,
                        purchaseId: item?.purchaseId,
                        packageName: item?.name,
                        quantity: item?.quantity,
                        expireIn: item?.expiredInDays || "",
                        durationUnit: item?.durationUnit || "",
                        startDate: item?.startDate,
                        endDate: item?.endDate,
                        unitPrice: item?.basePriceToRefund?.toFixed(2),
                        price: item?.gstInclusivePrice?.toFixed(2),
                        tax: item?.tax,
                        hsnOrSacCode: item?.hsnOrSacCode,
                        isInclusiveGst: item?.isInclusiveGst,
                        basePrice: item?.basePriceToRefund?.toFixed(2),
                        gstAmount: item?.gstAmountToRefund?.toFixed(2),
                        totalPrice: item?.gstInclusivePrice?.toFixed(2),
                        itemType: item?.itemType,
                    });
                }
            }

            // No cart-level discount distribution needed with the new format

            // Generate invoice number and order ID
            const highestInvoice = await this.InvoiceModel.findOne({ organizationId, facilityId }, { invoiceNumber: 1 }).sort({ invoiceNumber: -1 }).lean();
            const invoiceNumber = highestInvoice ? highestInvoice.invoiceNumber + 1 : 1;

            const highestOrderId = await this.InvoiceModel.findOne({ organizationId, facilityId }, { orderId: 1 }).sort({ orderId: -1 }).lean();
            const orderId = highestOrderId?.orderId ? highestOrderId?.orderId + 1 : 1;
            // Create invoice
            const invoice = new this.InvoiceModel({
                createdBy: user?._id,
                invoiceNumber,
                orderId,
                userId,
                organizationId,
                facilityId,
                discountedBy: validatedCart?.discountedBy || null,
                paymentBy: purchaseRequest?.paymentBy || user?._id,
                purchaseItems,
                productItem: productItems,
                customPackageItems,
                returnItems: returnPackageItems,
                subTotal: validatedCart?.subTotal?.toFixed(2),
                itemDiscount: validatedCart?.itemDiscount?.toFixed(2),
                cartDiscount: validatedCart?.cartDiscountValue,
                discount: validatedCart?.discount?.toFixed(2),
                cartDiscountType: validatedCart?.cartDiscount > 0 ? validatedCart?.cartDiscountType : undefined,
                cartDiscountAmount: validatedCart?.cartDiscountAmount?.toString(),
                // returnDiscount: validatedCart?.returnDiscount || 0,
                totalReturnBaseValue: validatedCart?.returnDetails?.returnBaseTotal || 0,
                totalReturnGstValue: validatedCart?.returnDetails?.returnGstTotal || 0,
                totalReturnValue: validatedCart?.returnDetails?.returnEffectiveTotal || 0,
                voucherDiscount: validatedCart?.voucherDiscount || 0,
                voucherDetails: validatedCart?.voucherDetails
                    ? {
                        voucherIds: validatedCart?.voucherDetails?.voucherIds,
                        totalVoucherAmount: validatedCart?.voucherDetails?.totalVoucherAmount,
                        usedVoucherAmount: validatedCart?.voucherDetails?.usedVoucherAmount,
                        remainingVoucherAmount: validatedCart?.voucherDetails?.remainingVoucherAmount,
                        appliedOn: new Date(),
                    }
                    : undefined,
                isInclusiveofGst: validatedCart?.isInclusiveofGst,
                totalGstValue: validatedCart?.totalGstValue?.toFixed(2),
                totalAmountAfterGst: validatedCart?.totalAmountAfterGst?.toFixed(2),
                roundOff: validatedCart?.roundOff?.toFixed(2),
                grandTotal: validatedCart?.grandTotal?.toFixed(2),
                amountInWords: this.convertAmountToWords(Math.floor(validatedCart?.grandTotal || 0)),
                invoiceDate: new Date(),
                platform,
                paymentStatus: paymentDetailsData?.[0]?.paymentStatus,
                paymentDetails: paymentDetailsData,
                isSplittedPayment,
                amountPaid,
                clientDetails: {
                    _id: billingDetails?.[0]?.clientDetails?.userId || "",
                    customerId: billingDetails?.[0]?.clientDetails?.customerId || "",
                    name: billingDetails?.[0]?.clientDetails?.name || "",
                    email: billingDetails?.[0]?.clientDetails?.email || "",
                    phone: billingDetails?.[0]?.clientDetails?.phone || "",
                },
                clientBillingDetails: {
                    _id: clientBillingDetails?._id || "",
                    customerId: clientBillingDetails?.customerId || "",
                    name: clientBillingDetails?.name || "",
                    addressLine1: clientBillingDetails?.addressLine1 || "",
                    addressLine2: clientBillingDetails?.addressLine2 || "",
                    postalCode: clientBillingDetails?.postalCode || "",
                    cityId: new Types.ObjectId(clientBillingDetails?.cityId) || "",
                    cityName: clientBillingDetails?.cityName || "",
                    stateId: new Types.ObjectId(clientBillingDetails?.stateId) || "",
                    stateName: clientBillingDetails?.stateName || "",
                    gstNumber: clientBillingDetails?.gstNumber,
                    utCode: clientBillingDetails?.utCode || "",
                },
                billingDetails: {
                    facilityName: billingDetails?.[0]?.billingDetails?.facilityName || "",
                    billingName: billingDetails?.[0]?.billingDetails?.billingName || "",
                    gstNumber: billingDetails?.[0]?.billingDetails?.gstNumber || "",
                    email: billingDetails?.[0]?.billingDetails?.email || "",
                    phone: billingDetails?.[0]?.billingDetails?.phone || "",
                    addressLine1: billingDetails?.[0]?.billingDetails?.addressLine1 || "",
                    addressLine2: billingDetails?.[0]?.billingDetails?.addressLine2 || "",
                    postalCode: billingDetails?.[0]?.billingDetails?.postalCode || "",
                    cityId: new Types.ObjectId(billingDetails[0]?.billingDetails?.cityId) || "",
                    cityName: billingDetails?.[0]?.billingDetails?.cityName || "",
                    stateId: new Types.ObjectId(billingDetails[0]?.billingDetails?.stateId) || "",
                    stateName: billingDetails?.[0]?.billingDetails?.stateName || "",
                    utCode: billingDetails?.[0]?.billingDetails?.utCode || "",
                },
                isForBusiness,
                returnDetails: validatedCart?.returnDetails,
                tenderedAmount: Number(tenderedAmount?.toFixed(2)),
                changeDue: Number(changeDue?.toFixed(2)),
            });
            // Save invoice and store for use in finally block
            // savedInvoice = await invoice.save({ session });
            invoiceId = invoice._id;

            // If paylater option, generate the payment link + qr image and send over mail

            // First get the keyId and keySecret as ther are multiple organisations

            if (paymentDetailsData[0].paymentStatus === PaymentStatus.PENDING) {
                try {
                    const amountInPaise = Math.round(validatedCart.grandTotal * 100);
                    const expireBy = Math.floor(Date.now() / 1000) + 60 * 60;

                    const apiKeys = await this.paymentService.fetchRazorPayApiKey(organizationId);
                    if (apiKeys) {
                        const { keyId, keySecret } = apiKeys;
                        const razorpayInstance = new Razorpay({
                            key_id: keyId,
                            key_secret: keySecret,
                        });

                        // const razorpayInstance = new Razorpay({
                        //     key_id: "rzp_test_gCGM3asXMW22Wi",
                        //     key_secret: "Hqw7Q0mxChvitsigtN6SiJVf",
                        // });

                        const paymentLinkResponse = await razorpayInstance.paymentLink.create({
                            amount: amountInPaise,
                            currency: "INR",
                            accept_partial: false,
                            expire_by: expireBy,
                            reference_id: `INV-${invoiceNumber}`,
                            description: `Payment for invoice #${invoiceNumber}`,
                            customer: {
                                name: client.name,
                                contact: client.mobile,
                                email: client.email,
                            },
                            notify: {
                                sms: false,
                                email: false,
                            },
                            reminder_enable: false,
                            notes: {
                                invoiceId: invoiceId.toString(),
                                userId: userId.toString(),
                                organizationId,
                            },
                        });

                        paymentLinkId = paymentLinkResponse.id;
                        paymentLinkUrl = paymentLinkResponse.short_url || paymentLinkResponse.full_page_url || "";
                        paymentLinkQRCodeBuffer = await QRCode.toBuffer(paymentLinkUrl);
                    }
                } catch (err) {
                    console.error("❌ Razorpay Payment Link Generation Failed:", err);
                }
            }

            // Create purchase records for services
            let membershipId = "";
            const purchases = [];

            const newPurchaseItems: {
                packageId: IDatabaseObjectId;
                quantity: number;
                isBundledPricing?: boolean;
                bundledPricingId?: string;
            }[] = [];

            const newEnrollments = [];

            // Process bundled pricing packages
            for (const item of purchaseItems) {
                const packageDetails = await this.PricingModel.findOne({
                    _id: new Types.ObjectId(item.packageId),
                }).lean();

                if (!packageDetails) {
                    throw new NotFoundException(`Package with ID ${item.packageId} not found`);
                }

                if (packageDetails?.membershipId) {
                    membershipId = packageDetails?.membershipId.toString() || "";
                }

                // Handle bundled pricing packages
                if (packageDetails.isBundledPricing && packageDetails.pricingIds && packageDetails.pricingIds.length > 0) {
                    // Add bundled packages to newPurchaseItems
                    for (const bundledId of packageDetails.pricingIds) {
                        newPurchaseItems.push({
                            packageId: bundledId,
                            quantity: item.quantity,
                            isBundledPricing: true,
                            bundledPricingId: item.packageId,
                        });
                    }
                } else {
                    // Add regular package to newPurchaseItems
                    newPurchaseItems.push({
                        packageId: item.packageId,
                        quantity: item.quantity,
                        isBundledPricing: false,
                    });
                }
            }

            // Create purchase records for all items (bundled and regular)
            for (const item of newPurchaseItems) {
                const packageDetails = await this.PricingModel.findOne({
                    _id: new Types.ObjectId(item.packageId),
                });

                if (!packageDetails) {
                    throw new NotFoundException(`Package with ID ${item.packageId} not found`);
                }

                if (packageDetails?.membershipId && !membershipId) {
                    membershipId = packageDetails?.membershipId.toString() || "";
                }

                for (let i = 0; i < item.quantity; i++) {
                    const startDate = date ? new Date(date) : new Date();
                    const endDate = this.calculateEndDate(startDate, packageDetails.expiredInDays, packageDetails.durationUnit);

                    const purchaseDoc = new this.PurchaseModel({
                        invoiceId,
                        packageId: item.packageId,
                        userId,
                        sponsorUser: billingUserId ?? userId,
                        consumers: [userId],
                        organizationId,
                        itemType: packageDetails.itemType,
                        facilityId,
                        ...(item.isBundledPricing && {
                            bundledPricingId: item.bundledPricingId,
                        }),
                        purchasedBy: user._id,
                        membershipId: packageDetails?.membershipId ? packageDetails?.membershipId : null,
                        voucherAmount: packageDetails?.itemType === ENUM_PRODUCT_ITEM_TYPE.VOUCHER ? packageDetails?.price : undefined,
                        amountConsumed: packageDetails?.itemType === ENUM_PRODUCT_ITEM_TYPE.VOUCHER ? 0 : undefined,
                        purchaseDate: new Date(),
                        paymentStatus: paymentDetailsData[0].paymentStatus,
                        isExpired: false,
                        sessionType: packageDetails.services?.sessionType,
                        ...(packageDetails.services?.sessionType === (SessionType.UNLIMITED || SessionType.DAY_PASS) && {
                            sessionPerDay: packageDetails.services?.sessionPerDay,
                        }),
                        ...(packageDetails.services?.sessionType === SessionType.DAY_PASS && {
                            dayPassLimit: packageDetails.services?.dayPassLimit,
                        }),
                        totalSessions: packageDetails.services?.sessionType === SessionType.SINGLE ? 1 : packageDetails.services?.sessionCount,
                        sessionConsumed: 0,
                        startDate: startDate, // new Date(startDate.setHours(0, 0, 0, 0)),
                        endDate: endDate, // new Date(endDate.setHours(23, 59, 59, 999)),
                    });
                    purchases.push(purchaseDoc);

                    // Purchase ids in invoice items
                    if (item.isBundledPricing) {
                        invoice.purchaseItems.find((i) => i.packageId.toString() === item.bundledPricingId.toString()).purchaseIds.push(purchaseDoc._id);
                    } else {
                        invoice.purchaseItems.find((i) => i.packageId.toString() === item.packageId.toString()).purchaseIds.push(purchaseDoc._id);
                    }

                    // Auto enroll in course schedules if this is a course package
                    if (packageDetails?.services?.type === ClassType.COURSES) {
                        const courseEnrollments = await this.autoEnrollInCourseSchedules(
                            new Types.ObjectId(userId),
                            item.packageId,
                            purchaseDoc._id,
                            new Types.ObjectId(organizationId),
                            session,
                        );
                        newEnrollments.push(...courseEnrollments);
                    }
                }
            }

            // Handle return purchases if provided
            if (validatedCart.returnItems && validatedCart.returnItems) {
                const ids = validatedCart.returnItems.map((item) => item.purchaseId);
                await this.PurchaseModel.updateMany(
                    { _id: { $in: ids } },
                    {
                        $set: {
                            isExpired: true,
                            isActive: false,
                            isExchanged: true,
                            exchangedInvoiceId: invoiceId,
                            exchangeDate: new Date(),
                        },
                    },
                    { session },
                );

                // Add return details to the invoice
                invoice.returnDetails = {
                    returnPurchaseIds: ids,
                    returnTotal: validatedCart.returnDiscount || 0,
                    exchangedOn: new Date(),
                };
            }

            if (validatedCart.returnCustomPackageDetails && validatedCart.returnCustomPackageDetails?.returnPurchaseIds?.length > 0) {
                await this.InvoiceModel.updateMany(
                    {
                        "customPackageItems._id": {
                            $in: validatedCart.returnCustomPackageDetails?.returnPurchaseIds,
                        },
                    },
                    {
                        $set: {
                            "customPackageItems.$.isReturned": true,
                            "customPackageItems.$.returnDate": Date.now(),
                        },
                    },
                    { session },
                );

                // Add return details to the invoice
                invoice.returnCustomPackageDetails = validatedCart.returnCustomPackageDetails;
            }

            // Handle voucher purchases if provided
            if (validatedCart.voucherDetails && validatedCart.voucherDetails.voucherIds?.length > 0) {
                const ids = validatedCart.voucherDetails.voucherIds || [];
                const voucher = await this.PurchaseModel.findOne({ _id: { $in: ids } });

                const usedVoucherAmount = validatedCart.voucherDetails.usedVoucherAmount || 0;

                // Store previous balance for logging
                const previousBalance = voucher.voucherAmount - voucher.amountConsumed;

                voucher.amountConsumed += usedVoucherAmount;
                voucher.exchangeDate = new Date();

                // Check is expired
                if (previousBalance < usedVoucherAmount) {
                    throw new BadRequestException("Voucher does not have enough balance to cover the purchase.");
                }

                if ((voucher.amountConsumed + usedVoucherAmount) >= voucher.voucherAmount) {
                    voucher.isExpired = true;
                    voucher.isActive = false;
                }

                await voucher.save({ session });

                // Create voucher redemption log
                await this.voucherService.logRedemption({
                    voucher,
                    userId: new Types.ObjectId(userId),
                    organizationId: new Types.ObjectId(organizationId),
                    amountRedeemed: usedVoucherAmount,
                    createdBy: user._id,
                    invoiceId,
                }, { session });
            }

            // Save invoice and store for use in finally block
            savedInvoice = await invoice.save({ session });

            if (purchases.length > 0) {
                await this.PurchaseModel.insertMany(purchases, { session });

                // Fetch the newly created purchase documents (optional, if not available already)
                const createdPurchases: any[] = await this.PurchaseModel.find({ invoiceId })
                    .populate([{ path: "packageId", select: "_id name" }])
                    .session(session);

                // Loop over each purchase to generate and attach QR code
                for (const purchaseDoc of createdPurchases) {
                    try {
                        if (![ENUM_PRODUCT_ITEM_TYPE.SERVICE, ENUM_PRODUCT_ITEM_TYPE.VOUCHER].includes(purchaseDoc.itemType)) continue;
                        const jsonData: any = {
                            organization_id: purchaseDoc.organizationId,
                            purchase_id: purchaseDoc._id,
                            package_id: purchaseDoc.packageId?._id,
                            name: purchaseDoc.packageId?.name,
                            startDate: purchaseDoc.startDate,
                            endDate: purchaseDoc.endDate,
                            itemType: purchaseDoc.itemType,
                            price: purchaseDoc.voucherAmount || purchaseDoc.packageId?.price || 0,
                        };

                        if (purchaseDoc.itemType === ENUM_PRODUCT_ITEM_TYPE.VOUCHER) {
                            jsonData.voucherCode = purchaseDoc.voucherCode;
                        }

                        const qrData = JSON.stringify(jsonData);
                        const qrCodeBuffer = await QRCode.toBuffer(qrData, { type: "png" });

                        const s3Result = await this.uploadService.upload(qrCodeBuffer, "purchase-qr/", `purchase-${purchaseDoc._id.toString()}.png`);

                        purchaseDoc.qrCodeUrl = s3Result?.Location || "";
                        await purchaseDoc.save({ session });
                    } catch (qrError) {
                        console.error(`Failed to generate QR for purchase ID: ${purchaseDoc._id}`, qrError);
                        // Optionally continue without failing the transaction
                    }
                }
            }

            if (newEnrollments.length) {
                await this.EnrollmentModel.insertMany(newEnrollments, { session });
            }

            // Update inventory for products
            for (const item of productItems) {
                const matchQuery = {
                    productId: item.inventoryId,
                };
                if (item?.productVariantId) {
                    matchQuery["productVariantId"] = item?.productVariantId;
                }
                await this.InventoryModel.findOneAndUpdate(matchQuery, { $inc: { quantity: -Number(item.quantity) } }, { session });
            }

            // Handle membership assignment if needed
            if (membershipId !== "") {
                const clientProfile = await this.ClientModel.findOne({ userId });
                if (!clientProfile?.membershipId) {
                    const checkCounter = await this.MembershipModel.findById(membershipId);

                    if (!checkCounter) {
                        throw new NotFoundException("This pricing includes membership. Mapped membership not found");
                    }

                    if (!checkCounter?.lastcounter || checkCounter?.lastcounter == 0) {
                        await this.ClientModel.findOneAndUpdate({ userId }, { membershipId: `${checkCounter.prefix}-${checkCounter.counter}`.toString() }, { session });
                        await this.MembershipModel.findByIdAndUpdate(membershipId, { lastcounter: checkCounter.counter + 1 }, { session });
                    } else {
                        await this.ClientModel.findOneAndUpdate({ userId }, { membershipId: `${checkCounter.prefix}-${checkCounter.lastcounter}`.toString() }, { session });
                        await this.MembershipModel.findByIdAndUpdate(membershipId, { lastcounter: checkCounter.lastcounter + 1 }, { session });
                    }
                }
            }

            // Commit transaction
            await session.commitTransaction();

            return {
                invoiceId: invoiceId.toString(),
                invoiceNumber,
                purchaseItems: invoice.purchaseItems,
                orderId,
                grandTotal: validatedCart.grandTotal,
                amountPaid: validatedCart.amountPaid,
            };
        } catch (error) {
            // Abort transaction on error
            await session.abortTransaction();

            // Log the error for debugging
            console.error("Error processing purchase:", error);

            // Return structured error response
            if (error instanceof NotFoundException || error instanceof BadRequestException || error instanceof HttpException) {
                throw error;
            }

            // For unexpected errors, throw internal server error
            throw new InternalServerErrorException("An error occurred while processing the purchase");
        } finally {
            await session.endSession();
            // Generate invoice if payment is not pending and invoice was saved successfully
            try {
                if (savedInvoice && invoiceId && paymentDetailsData && paymentDetailsData.length > 0 && paymentDetailsData[0].paymentStatus !== PaymentStatus.PENDING) {
                    const organization = await this.UserModel.findById(organizationId).select("email");
                    const organizationEmail = organization?.email || "";
                    const createdBy = await this.UserModel.findById(savedInvoice.paymentBy || savedInvoice.createdBy, { name: 1, firstName: 1, lastName: 1 });
                    const data = savedInvoice.toObject();

                    await Promise.all(
                        (data.purchaseItems || []).map(async (item: any) => {
                            const purchaseData: any = await this.PurchaseModel.findOne({
                                $or: [{ userId: data.userId }, { sponsorUser: data.userId }],
                                packageId: item?.packageId,
                                invoiceId,
                            })
                                .populate([{ path: "packageId", select: "_id isInclusiveGst isInclusiveofGst isinclusiveGst finalPrice" }])
                                .lean();

                            const pkg = purchaseData?.packageId || {};
                            const isInclusiveofGst = pkg?.isInclusiveofGst ?? pkg?.isInclusiveGst ?? pkg?.isinclusiveGst ?? false;

                            item.isInclusiveofGst = isInclusiveofGst;
                            item.finalPrice = pkg?.finalPrice ?? null;
                        }),
                    );
                    data.createdByName = createdBy?.name || `${createdBy?.firstName} ${createdBy?.lastName}`;
                    // const returnPurchaseItemsDetails = await Promise.all(
                    //     data.returnDetails?.returnPurchaseIds?.map(async (purchaseId: any) => {
                    //         const purchaseData = await this.PurchaseModel.findOne({
                    //             _id: purchaseId,
                    //         }).lean();
                    //         const item = await this.PricingModel.findOne({
                    //             _id: purchaseData.packageId,
                    //         });
                    //         return {
                    //             packageId: item?._id,
                    //             purchaseIds: purchaseId,
                    //             classType: item?.services?.type || "",
                    //             isBundledPricing: item?.isBundledPricing,
                    //             sessionType: purchaseData?.sessionType,
                    //             name: item?.name,
                    //             price: item?.price,
                    //             tax: item?.tax,
                    //             hsnOrSacCode: item?.hsnOrSacCode || "",
                    //             expiredInDays: item?.expiredInDays,
                    //             durationUnit: item?.durationUnit,
                    //             discountType: "",
                    //             discountValue: 0,
                    //             quantity: 1,
                    //             startDate: purchaseData?.startDate,
                    //             endDate: purchaseData?.endDate,
                    //             // isInclusiveofGst: item?.isInclusiveofGst,
                    //             // finalPrice: item?.finalPrice,
                    //         };
                    //     }) || [],
                    // );

                    const appliedVoucherItems = await Promise.all(
                        data.voucherDetails?.voucherIds?.map(async (purchaseId: any) => {
                            const purchaseData = await this.PurchaseModel.findOne({
                                _id: purchaseId,
                            }).lean();
                            const item = await this.PricingModel.findOne({
                                _id: purchaseData.packageId,
                            });
                            return {
                                packageId: item?._id,
                                purchaseIds: purchaseId,
                                itemType: purchaseData?.itemType,
                                name: item?.name,
                                price: purchaseData.voucherAmount,
                                tax: item?.tax || 0,
                                hsnOrSacCode: item?.hsnOrSacCode || "",
                                expiredInDays: item?.expiredInDays,
                                durationUnit: item?.durationUnit,
                                discountType: "",
                                discountValue: 0,
                                quantity: 1,
                                startDate: purchaseData?.startDate,
                                endDate: purchaseData?.endDate,
                                // isInclusiveofGst: item?.isInclusiveofGst,
                                // finalPrice: item?.finalPrice,
                            };
                        }) || [],
                    );

                    data["appliedVoucherItems"] = appliedVoucherItems;
                    await this.invoiceService.generateInvoice(data, organizationEmail);
                }

                if (savedInvoice && paymentDetailsData?.[0]?.paymentStatus === PaymentStatus.PENDING) {
                    const organization = await this.UserModel.findById(organizationId).select("email");
                    const organizationEmail = organization?.email || "";

                    await this.invoiceService.sendPendingInvoicePaymentEmailWithQR(savedInvoice, paymentLinkQRCodeBuffer, paymentLinkUrl);
                }
            } catch (invoiceError) {
                console.error("Error generating invoice:", invoiceError);
            }
        }
    }

    private calculateEndDate(startDate: Date, expireIn: number, durationUnit: string): Date {
        const endDate = new Date(startDate);

        switch (durationUnit) {
            case DurationUnit.DAYS:
                endDate.setDate(endDate.getDate() + expireIn);
                break;
            case DurationUnit.MONTHS:
                endDate.setMonth(endDate.getMonth() + expireIn);
                break;
            case DurationUnit.YEARS:
                endDate.setFullYear(endDate.getFullYear() + expireIn);
                break;
            default:
                throw new Error(`Invalid duration unit: ${durationUnit}`);
        }

        return endDate;
    }

    private mapPromotionTypeToDiscountType(promotionType: DiscountType): string {
        switch (promotionType) {
            case DiscountType.PERCENTAGE:
                return DiscountType.PERCENTAGE;
            case DiscountType.FLAT:
                return DiscountType.FLAT;
            default:
                return DiscountType.FLAT;
        }
    }

    async purchaseProduct(productItem: any, cartDiscount: any, cartDiscountType: any, productSubTotal: number) {
        let invoiceSubTotal = 0;
        let invoiveDiscountExculdeCart = 0;
        let invoiveDiscountIncludeCart = 0;
        let invoiceGstAmount = 0;
        let invoiceAmountAfterGst = 0;
        let roundOff: any;
        let grandTotal = 0;
        let discountExcludeCart = 0;
        let discountIncludeCart = 0;
        let gstAmount = 0;
        let totalAmountExcludeCartDiscount = 0;
        let productDetail: any = {};
        let variantProductDetail = {};
        let finalProductDetail: any = {};
        let finalProduct = [];
        let totalFlatCartDiscountAllocated = 0;
        for (let i = 0; i < productItem.length; i++) {
            const item = productItem[i];
            const inventoryDetail: any = await this.InventoryModel.findOne({ _id: item.inventoryId });

            if (!inventoryDetail) {
                throw new NotFoundException(`Product  with ID ${item.productId} not found`);
            }
            const { mrp, salePrice, discount, quantity } = inventoryDetail;
            if (quantity === 0) {
                throw new BadRequestException("The Product is Out of Stock.");
            }
            if (quantity < item.quantity) {
                throw new BadRequestException("The requested purchase quantity exceeds the available stock.");
            }

            if (item.productType === ProductType.SIMPLE) {
                productDetail = await this.productModel.findOne({ _id: item.productId });
            }
            if (item.productType === ProductType.VARIABLE) {
                variantProductDetail = await this.productVariantModel.findOne({ _id: item.productVariantId }).populate({ path: "productId" });
                productDetail = await this.productModel.findOne({ _id: item.productId });
            }
            const totalUnitPrice = Number(item.quantity) * Number(inventoryDetail?.salePrice);
            invoiceSubTotal += totalUnitPrice;

            discountExcludeCart = Number(item.quantity) * Number(((Number(inventoryDetail?.discount) / 100) * Number(inventoryDetail?.salePrice)).toFixed(2));

            invoiveDiscountExculdeCart += discountExcludeCart;
            totalAmountExcludeCartDiscount = totalUnitPrice - discountExcludeCart;
            if (cartDiscount && cartDiscountType === DiscountType.PERCENTAGE) {
                discountIncludeCart = Number(((Number(cartDiscount) / 100) * Number(totalAmountExcludeCartDiscount)).toFixed(2));
                gstAmount = Number((totalUnitPrice - (discountExcludeCart + discountIncludeCart)) * (Number(productDetail?.gst) / 100));
                invoiceGstAmount += gstAmount;
                invoiveDiscountIncludeCart += discountIncludeCart;
            } else if (cartDiscount && cartDiscountType === DiscountType.FLAT) {
                const itemShare = totalAmountExcludeCartDiscount / productSubTotal;

                if (i === productItem.length - 1) {
                    discountIncludeCart = Number((Number(cartDiscount) - totalFlatCartDiscountAllocated).toFixed(2));
                } else {
                    discountIncludeCart = Number((itemShare * Number(cartDiscount)).toFixed(2));
                    totalFlatCartDiscountAllocated += discountIncludeCart;
                }

                invoiveDiscountIncludeCart += discountIncludeCart;
                gstAmount = Number((totalUnitPrice - (discountExcludeCart + discountIncludeCart)) * (Number(productDetail?.gst) / 100));
                invoiceGstAmount += gstAmount;
            } else {
                gstAmount = Number((totalAmountExcludeCartDiscount * (Number(productDetail?.gst) / 100)).toFixed(2));
                invoiceGstAmount += gstAmount;
            }

            invoiceAmountAfterGst = invoiceSubTotal + invoiceGstAmount - (invoiveDiscountIncludeCart + invoiveDiscountExculdeCart) || 0;
            roundOff = (invoiceAmountAfterGst - Math.floor(invoiceAmountAfterGst)).toFixed(2);
            grandTotal = Math.floor(invoiceAmountAfterGst);
            finalProductDetail = item.productType === ProductType.VARIABLE ? variantProductDetail : productDetail;
            const hsnOrSacCode: any = productDetail.hsn || "";
            finalProduct.push({
                productName: finalProductDetail.title || finalProductDetail.name,
                sku: finalProductDetail.sku,
                productId: item.productId,
                productVariantId: item.productVariantId,
                quantity: item.quantity,
                discountExcludeCart,
                discountIncludeCart,
                hsnOrSacCode: hsnOrSacCode,
                salePrice: inventoryDetail.salePrice,
                finalPrice: inventoryDetail.discountPrice,
                discount: item.discount,
                tax: item.tax,
                mrp: item.mrp,
                gstAmount: gstAmount,
            });
            await this.InventoryModel.findOneAndUpdate({ _id: item.inventoryId }, { $inc: { quantity: -Number(item.quantity) } }, { new: true });
        }

        return {
            invoiceSubTotal,
            invoiveDiscountExculdeCart,
            invoiveDiscountIncludeCart,
            invoiceGstAmount,
            invoiceAmountAfterGst,
            roundOff,
            grandTotal,
            discountExcludeCart,
            discountIncludeCart,
            gstAmount,
            totalAmountExcludeCartDiscount,
            productDetail: finalProduct,
        };
    }

    async purchaseCustomPackage(customPackageItems: any, cartDiscount: any, cartDiscountType: any, customPackageSubTotal: number, checkValidCustomPackage: any) {
        let invoiceSubTotal = 0;
        let invoiveDiscountExculdeCart = 0;
        let invoiveDiscountIncludeCart = 0;
        let invoiceGstAmount = 0;
        let invoiceAmountAfterGst = 0;
        let roundOff: any;
        let grandTotal = 0;
        let finalProduct = [];
        for (let i = 0; i < customPackageItems.length; i++) {
            const item = customPackageItems[i];
            let gstAmount = 0;
            let discountIncludeCart = 0;
            let discountExcludeCart = 0;
            let totalAmountExcludeCartDiscount = 0;
            let totalFlatCartDiscountAllocated = 0;
            const packageData = checkValidCustomPackage.find((pkg) => pkg._id.toString() === item.customPackageId.toString());
            if (!packageData) throw new NotFoundException(`Custom package  with ID ${item.customPackageId} not found`);
            const totalUnitPrice = Number(item.quantity) * Number(packageData?.quantity * Number(packageData?.unitPrice));
            invoiceSubTotal += totalUnitPrice;

            if (packageData?.discount?.type === DiscountType.PERCENTAGE) {
                discountExcludeCart =
                    Number(item.quantity) * Number(((Number(packageData?.discount?.value) / 100) * Number(packageData?.quantity * Number(packageData?.unitPrice))).toFixed(2));
            } else if (packageData?.discount?.type === DiscountType.FLAT) {
                discountExcludeCart = Number(item.quantity) * Number(Number(packageData?.discount?.value).toFixed(2));
            }

            invoiveDiscountExculdeCart += discountExcludeCart;
            totalAmountExcludeCartDiscount = totalUnitPrice - discountExcludeCart;
            if (cartDiscount && cartDiscountType === DiscountType.PERCENTAGE) {
                discountIncludeCart = Number(((Number(cartDiscount) / 100) * Number(totalAmountExcludeCartDiscount)).toFixed(2));
                gstAmount = Number((totalUnitPrice - (discountExcludeCart + discountIncludeCart)) * (Number(packageData?.tax) / 100));
                invoiceGstAmount += gstAmount;
                invoiveDiscountIncludeCart += discountIncludeCart;
            } else if (cartDiscount && cartDiscountType === DiscountType.FLAT) {
                const itemShare = totalAmountExcludeCartDiscount / customPackageSubTotal;

                if (i === customPackageItems.length - 1) {
                    discountIncludeCart = Number((Number(cartDiscount) - totalFlatCartDiscountAllocated).toFixed(2));
                } else {
                    discountIncludeCart = Number((itemShare * Number(cartDiscount)).toFixed(2));
                    totalFlatCartDiscountAllocated += discountIncludeCart;
                }

                invoiveDiscountIncludeCart += discountIncludeCart;
                gstAmount = Number((totalUnitPrice - (discountExcludeCart + discountIncludeCart)) * (Number(packageData?.tax) / 100));
                invoiceGstAmount += gstAmount;
            } else {
                gstAmount = Number((totalAmountExcludeCartDiscount * (Number(packageData?.tax) / 100)).toFixed(2));
                invoiceGstAmount += gstAmount;
            }
            invoiceAmountAfterGst = invoiceSubTotal + invoiceGstAmount - (invoiveDiscountIncludeCart + invoiveDiscountExculdeCart) || 0;
            roundOff = (invoiceAmountAfterGst - Math.floor(invoiceAmountAfterGst)).toFixed(2);
            grandTotal = Math.floor(invoiceAmountAfterGst);
            finalProduct.push({
                packageName: packageData.name,
                customPackageId: packageData._id,
                quantity: packageData.quantity,
                discountExcludeCart,
                discountIncludeCart,
                hsnOrSacCode: packageData.hsnOrSacCode || "",
                unitPrice: packageData.unitPrice,
                finalPrice: packageData.total,
                discountType: packageData?.discount?.type,
                discountValue: packageData?.discount?.value,
                tax: packageData.tax,
                gstAmount: gstAmount,
            });
        }

        return {
            invoiceSubTotal,
            invoiveDiscountExculdeCart,
            invoiveDiscountIncludeCart,
            invoiceGstAmount,
            invoiceAmountAfterGst,
            roundOff,
            grandTotal,
            productDetail: finalProduct,
        };
    }
    async formatInvoiceData(invoice: any) {
        let pipeline = this.usersPipe.invoiceAllData(invoice._id);
        const invoiceData = await this.InvoiceModel.aggregate(pipeline);
        return invoiceData[0];
    }
    async purchaseListByClassType(classType: string, user: any, pricingListDTO: PricingListDTO): Promise<any> {
        let { startDate, endDate, facilityIds, serviceCategoryIds, page, pageSize, search } = pricingListDTO;
        if (!classType) {
            throw new BadRequestException("Class type is required");
        }

        pageSize = pageSize ?? 10;
        page = page ?? 1;
        const skip = pageSize * (page - 1);
        const query: any = {
            itemType: ENUM_PRODUCT_ITEM_TYPE.SERVICE,
        };
        let ordId;

        if (user.role.type === ENUM_ROLE_TYPE.ORGANIZATION) {
            query.organizationId = user._id;
            ordId = user._id;
        }

        if ([ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.TRAINER].includes(user.role.type)) {
            const staffDetails = await this.StaffDetailsModel.findOne({ userId: user._id }, { organizationId: 1, facilityId: 1 }).exec();

            query.organizationId = staffDetails.organizationId;
            query.facilityId = { $in: staffDetails.facilityId.map((id) => new Types.ObjectId(id)) };
            ordId = staffDetails.organizationId;
        }

        if (startDate) {
            query.startDate = { $gte: new Date(startDate) };
        }
        if (endDate) {
            query.endDate = { $lte: new Date(endDate) };
        }
        if (facilityIds) {
            query.facilityId = { $in: facilityIds.map((id) => new Types.ObjectId(id)) };
        }

        const pipeline: any = [
            {
                $match: query,
            },
            {
                $lookup: {
                    from: "pricings",
                    localField: "packageId",
                    foreignField: "_id",
                    as: "pricingDetails",
                    pipeline: [
                        {
                            $match: {
                                "services.type": classType,
                                ...(serviceCategoryIds && {
                                    "services.serviceCategory": { $in: serviceCategoryIds.map((id) => new Types.ObjectId(id)) },
                                }),
                            },
                        },
                        {
                            $project: {
                                name: 1,
                                services: 1,
                            },
                        },
                    ],
                },
            },
            {
                $unwind: {
                    path: "$pricingDetails",
                    preserveNullAndEmptyArrays: false,
                },
            },
            {
                $lookup: {
                    from: "users",
                    localField: "userId",
                    foreignField: "_id",
                    as: "userDetails",
                    pipeline: [
                        {
                            $project: {
                                _id: 1,
                                name: 1,
                                email: 1,
                                mobile: 1,
                                firstName: 1,
                                lastName: 1,
                            },
                        },
                    ],
                },
            },
            {
                $unwind: {
                    path: "$userDetails",
                    preserveNullAndEmptyArrays: false,
                },
            },
            {
                $lookup: {
                    from: "clients",
                    localField: "userId",
                    foreignField: "userId",
                    as: "userProfileDetails",
                    pipeline: [
                        {
                            $project: {
                                clientId: 1,
                                organizationId: 1,
                                membershipId: 1,
                            },
                        },
                    ],
                },
            },
            {
                $unwind: {
                    path: "$userProfileDetails",
                    preserveNullAndEmptyArrays: false,
                },
            },
            {
                $match: {
                    "userProfileDetails.organizationId": new Types.ObjectId(ordId),
                },
            },
            {
                $lookup: {
                    from: "facilities",
                    localField: "facilityId",
                    foreignField: "_id",
                    as: "facilityDetails",
                    pipeline: [
                        {
                            $project: {
                                _id: 1,
                                facilityName: 1,
                            },
                        },
                    ],
                },
            },
            {
                $unwind: {
                    path: "$facilityDetails",
                    preserveNullAndEmptyArrays: false,
                },
            },
            {
                $lookup: {
                    from: "services",
                    localField: "pricingDetails.services.serviceCategory",
                    foreignField: "_id",
                    as: "servicesDetails",
                    pipeline: [
                        {
                            $project: {
                                name: 1,
                            },
                        },
                    ],
                },
            },
            {
                $unwind: {
                    path: "$servicesDetails",
                    preserveNullAndEmptyArrays: false,
                },
            },
            {
                $lookup: {
                    from: "users",
                    localField: "sharedBy",
                    foreignField: "_id",
                    as: "sharedByUser",
                    pipeline: [
                        {
                            $project: {
                                _id: 1,
                                firstName: 1,
                                lastName: 1,
                            },
                        },
                    ],
                },
            },
            {
                $unwind: {
                    path: "$sharedByUser",
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $addFields: {
                    sharedByName: {
                        $cond: [{ $ifNull: ["$sharedByUser._id", false] }, { $concat: ["$sharedByUser.firstName", " ", "$sharedByUser.lastName"] }, null],
                    },
                },
            },

            {
                $lookup: {
                    from: "schedulings",
                    let: {
                        purchaseId: "$_id",
                        clientId: "$userId",
                    },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        { $eq: ["$purchaseId", "$$purchaseId"] },
                                        { $eq: ["$clientId", "$$clientId"] },
                                        { $ne: ["$scheduleStatus", ScheduleStatusType.CANCELED] },
                                    ],
                                },
                            },
                        },
                        {
                            $group: {
                                _id: "$date",
                            },
                        },
                    ],
                    as: "bookedDates",
                },
            },
            {
                $addFields: {
                    consumedDayPassLimit: { $size: "$bookedDates" },
                },
            },
            {
                $addFields: {
                    remainingSessions: {
                        $switch: {
                            branches: [
                                {
                                    case: { $eq: ["$pricingDetails.services.sessionType", "single"] },
                                    then: { $cond: [{ $eq: ["$sessionConsumed", 0] }, 1, 0] },
                                },
                                {
                                    case: { $eq: ["$pricingDetails.services.sessionType", "multiple"] },
                                    then: {
                                        $max: [{ $subtract: ["$totalSessions", "$sessionConsumed"] }, 0],
                                    },
                                },
                                {
                                    case: { $eq: ["$pricingDetails.services.sessionType", "day_pass"] },
                                    then: {
                                        $concat: [
                                            {
                                                $toString: {
                                                    $max: [
                                                        {
                                                            $subtract: [{ $subtract: ["$dayPassLimit", "$consumedDayPassLimit"] }, "$sessionShared"],
                                                        },
                                                        0,
                                                    ],
                                                },
                                            },
                                            " day pass(es)",
                                        ],
                                    },
                                },
                            ],
                            default: 9999,
                        },
                    },
                },
            },
            {
                $project: {
                    customerId: "$userProfileDetails.clientId",
                    // clientName: '$userDetails.name',
                    clientName: {
                        $concat: ["$userDetails.firstName", " ", "$userDetails.lastName"],
                    },
                    membershipId: "$userProfileDetails.membershipId",
                    clientId: "$userDetails._id",
                    facilityId: "$facilityDetails._id",
                    packageName: "$pricingDetails.name",
                    packageId: "$pricingDetails._id",
                    date: "$purchaseDate",
                    email: "$userDetails.email",
                    mobile: "$userDetails.mobile",
                    remainingSessions: "$remainingSessions",
                    location: "$facilityDetails.facilityName",
                    serviceCategory: "$servicesDetails.name",
                    paymentStatus: 1,
                    classType: "$pricingDetails.services.type",
                    suspensions: 1,
                    startDate: 1,
                    endDate: 1,
                    isActive: 1,
                    createdAt: 1,
                    updatedAt: 1,
                    sessionType: 1,
                    sharedBy: 1,
                    sharedByName: 1,
                    invoiceId: 1,
                },
            },
        ];
        if (search) {
            pipeline.push({
                $match: {
                    $or: [
                        { clientName: { $regex: search, $options: "i" } },
                        { email: { $regex: search, $options: "i" } },
                        { packageName: { $regex: search, $options: "i" } },
                        { location: { $regex: search, $options: "i" } },
                        { serviceCategory: { $regex: search, $options: "i" } },
                        { mobile: { $regex: search, $options: "i" } },
                    ],
                },
            });
        }

        const facetPipeline = [
            {
                $facet: {
                    total: [{ $count: "count" }],
                    data: [
                        { $sort: { createdAt: -1 } },
                        { $skip: skip },
                        { $limit: pageSize },
                        {
                            $set: {
                                suspensions: {
                                    $filter: {
                                        input: { $cond: ["$suspensions", "$suspensions", []] },
                                        as: "item",
                                        cond: {
                                            $and: [{ $ne: ["$$item.isResumed", true] }, { $gt: ["$$item.endDate", new Date()] }],
                                        },
                                    },
                                },
                            },
                        },
                        {
                            $set: {
                                isSuspended: {
                                    $cond: [{ $gt: [{ $size: "$suspensions" }, 0] }, true, false],
                                },
                            },
                        },
                    ],
                },
            },
            {
                $project: {
                    total: { $arrayElemAt: ["$total.count", 0] },
                    data: 1,
                },
            },
        ];

        const result = await this.PurchaseModel.aggregate([...pipeline, ...facetPipeline]);
        return result[0];
    }

    async allTypeOfPurchaseList(user: any, pricingListDTO: PricingListDTO): Promise<any> {
        let { startDate, endDate, facilityIds, serviceCategoryIds, page, pageSize, search, classType, status } = pricingListDTO;

        pageSize = pageSize ?? 10;
        page = page ?? 1;
        const skip = pageSize * (page - 1);
        const query: any = { membershipId: { $exists: true, $ne: null } };
        let ordId;

        if (user.role.type === ENUM_ROLE_TYPE.ORGANIZATION) {
            query.organizationId = user._id;
            ordId = user._id;
        }

        if ([ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.TRAINER].includes(user.role.type)) {
            const staffDetails = await this.StaffDetailsModel.findOne({ userId: user._id }, { organizationId: 1, facilityId: 1 }).exec();

            query.organizationId = staffDetails.organizationId;
            query.facilityId = { $in: staffDetails.facilityId.map((id) => new Types.ObjectId(id)) };
            ordId = staffDetails.organizationId;
        }

        if (facilityIds) {
            query.facilityId = { $in: facilityIds.map((id) => new Types.ObjectId(id)) };
        }

        if (status === MembershipStatus.RENEWALS) {
            const today = new Date();
            const startRange = new Date(today);
            const endRange = new Date(today);

            startRange.setDate(startRange.getDate() - 7);
            endRange.setDate(endRange.getDate() + 10);

            query.endDate = { $gte: startRange, $lte: endRange };
        } else if (status === MembershipStatus.EXPIRED) {
            query.endDate = { $lt: new Date() };
        } else if (status === MembershipStatus.ACTIVE) {
            query.startDate = { $lte: new Date() };
            query.endDate = { $gt: new Date() };
        } else {
            if (startDate) {
                query.startDate = { $gte: new Date(startDate) };
            }
            if (endDate) {
                query.endDate = { $lte: new Date(endDate) };
            }
        }

        const pipeline: any = [
            {
                $match: query,
            },
            {
                $lookup: {
                    from: "pricings",
                    localField: "packageId",
                    foreignField: "_id",
                    as: "pricingDetails",
                    pipeline: [
                        {
                            $match: {
                                ...(classType?.length > 0 && { "services.type": { $in: classType } }),
                                ...(serviceCategoryIds && {
                                    "services.serviceCategory": { $in: serviceCategoryIds.map((id) => new Types.ObjectId(id)) },
                                }),
                            },
                        },
                        {
                            $project: {
                                name: 1,
                                services: 1,
                            },
                        },
                    ],
                },
            },
            {
                $unwind: {
                    path: "$pricingDetails",
                    preserveNullAndEmptyArrays: false,
                },
            },
            {
                $lookup: {
                    from: "users",
                    localField: "userId",
                    foreignField: "_id",
                    as: "userDetails",
                    pipeline: [
                        {
                            $project: {
                                _id: 1,
                                name: 1,
                                email: 1,
                                mobile: 1,
                                firstName: 1,
                                lastName: 1,
                            },
                        },
                    ],
                },
            },
            {
                $unwind: {
                    path: "$userDetails",
                    preserveNullAndEmptyArrays: false,
                },
            },
            {
                $lookup: {
                    from: "clients",
                    localField: "userId",
                    foreignField: "userId",
                    as: "userProfileDetails",
                    pipeline: [
                        {
                            $project: {
                                clientId: 1,
                                organizationId: 1,
                                membershipId: 1,
                            },
                        },
                    ],
                },
            },
            {
                $unwind: {
                    path: "$userProfileDetails",
                    preserveNullAndEmptyArrays: false,
                },
            },
            {
                $match: {
                    "userProfileDetails.organizationId": new Types.ObjectId(ordId),
                },
            },
            {
                $lookup: {
                    from: "facilities",
                    localField: "facilityId",
                    foreignField: "_id",
                    as: "facilityDetails",
                    pipeline: [
                        {
                            $project: {
                                _id: 1,
                                facilityName: 1,
                            },
                        },
                    ],
                },
            },
            {
                $unwind: {
                    path: "$facilityDetails",
                    preserveNullAndEmptyArrays: false,
                },
            },
            {
                $lookup: {
                    from: "services",
                    localField: "pricingDetails.services.serviceCategory",
                    foreignField: "_id",
                    as: "servicesDetails",
                    pipeline: [
                        {
                            $project: {
                                name: 1,
                            },
                        },
                    ],
                },
            },
            {
                $unwind: {
                    path: "$servicesDetails",
                    preserveNullAndEmptyArrays: false,
                },
            },
            {
                $lookup: {
                    from: "schedulings",
                    let: {
                        purchaseId: "$_id",
                        clientId: "$userId",
                    },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        { $eq: ["$purchaseId", "$$purchaseId"] },
                                        { $eq: ["$clientId", "$$clientId"] },
                                        { $ne: ["$scheduleStatus", ScheduleStatusType.CANCELED] },
                                    ],
                                },
                            },
                        },
                        {
                            $group: {
                                _id: "$date",
                            },
                        },
                    ],
                    as: "bookedDates",
                },
            },
            {
                $addFields: {
                    consumedDayPassLimit: { $size: "$bookedDates" },
                },
            },
            {
                $addFields: {
                    remainingSessions: {
                        $switch: {
                            branches: [
                                {
                                    case: { $eq: ["$pricingDetails.services.sessionType", "single"] },
                                    then: { $cond: [{ $eq: ["$sessionConsumed", 0] }, 1, 0] },
                                },
                                {
                                    case: { $eq: ["$pricingDetails.services.sessionType", "multiple"] },
                                    then: {
                                        $max: [{ $subtract: ["$totalSessions", "$sessionConsumed"] }, 0],
                                    },
                                },
                                {
                                    case: { $eq: ["$pricingDetails.services.sessionType", "day_pass"] },
                                    then: {
                                        $concat: [
                                            {
                                                $toString: {
                                                    $max: [
                                                        {
                                                            $subtract: [{ $subtract: ["$dayPassLimit", "$consumedDayPassLimit"] }, "$sessionShared"],
                                                        },
                                                        0,
                                                    ],
                                                },
                                            },
                                            " day pass(es)",
                                        ],
                                    },
                                },
                            ],
                            default: 9999,
                        },
                    },
                },
            },
            {
                $project: {
                    customerId: "$userProfileDetails.clientId",
                    // clientName: '$userDetails.name',
                    clientName: {
                        $concat: ["$userDetails.firstName", " ", "$userDetails.lastName"],
                    },
                    membershipId: "$userProfileDetails.membershipId",
                    clientId: "$userDetails._id",
                    facilityId: "$facilityDetails._id",
                    packageName: "$pricingDetails.name",
                    date: "$purchaseDate",
                    email: "$userDetails.email",
                    mobile: "$userDetails.mobile",
                    remainingSessions: "$remainingSessions",
                    location: "$facilityDetails.facilityName",
                    serviceCategory: "$servicesDetails.name",
                    paymentStatus: 1,
                    classType: "$pricingDetails.services.type",
                    suspensions: 1,
                    startDate: 1,
                    endDate: 1,
                    isActive: 1,
                    createdAt: 1,
                    updatedAt: 1,
                    sessionType: 1,
                },
            },
        ];
        if (search) {
            pipeline.push({
                $match: {
                    $or: [
                        { clientName: { $regex: search, $options: "i" } },
                        { email: { $regex: search, $options: "i" } },
                        { packageName: { $regex: search, $options: "i" } },
                        { location: { $regex: search, $options: "i" } },
                        { serviceCategory: { $regex: search, $options: "i" } },
                        { mobile: { $regex: search, $options: "i" } },
                    ],
                },
            });
        }

        const facetPipeline = [
            {
                $facet: {
                    total: [{ $count: "count" }],
                    data: [
                        { $sort: { createdAt: -1 } },
                        { $skip: skip },
                        { $limit: pageSize },
                        {
                            $set: {
                                suspensions: {
                                    $filter: {
                                        input: { $cond: ["$suspensions", "$suspensions", []] },
                                        as: "item",
                                        cond: {
                                            $and: [{ $ne: ["$$item.isResumed", true] }, { $gt: ["$$item.endDate", new Date()] }],
                                        },
                                    },
                                },
                            },
                        },
                        {
                            $set: {
                                isSuspended: {
                                    $cond: [{ $gt: [{ $size: "$suspensions" }, 0] }, true, false],
                                },
                            },
                        },
                    ],
                },
            },
            {
                $project: {
                    total: { $arrayElemAt: ["$total.count", 0] },
                    data: 1,
                },
            },
        ];

        const result = await this.PurchaseModel.aggregate([...pipeline, ...facetPipeline]);
        return result[0];
    }

    async getAllPurchases(filter: Record<string, any>, classType?: string): Promise<Purchase[]> {
        let purchases = await this.PurchaseModel.find(filter).populate([
            { path: "organizationId", select: "_id name email" },
            { path: "userId", select: "_id firstName lastName email" },
            { path: "packageId", select: "_id name price expiredInDays durationUnit services" },
            { path: "purchasedBy", select: "_id name firstName lastName email" },
        ]);
        if (classType) {
            purchases = purchases.filter((item: any) => item.packageId?.services?.type === classType);
        }
        return purchases;
    }

    async getPurchaseById(id: string, user: any): Promise<Purchase> {
        const purchase = await this.PurchaseModel.findOne({
            itemType: ENUM_PRODUCT_ITEM_TYPE.SERVICE,
            _id: id,
        }).exec();
        if (!purchase) {
            throw new NotFoundException(`Purchase with ID ${id} not found`);
        }
        return purchase;
    }
    async deletePurchaseById(id: string, user: any): Promise<Purchase> {
        const deletedPurchase = await this.PurchaseModel.findByIdAndDelete(id).exec();
        if (!deletedPurchase) {
            throw new NotFoundException(`Purchase with ID ${id} not found`);
        }
        return deletedPurchase;
    }

    async activePricingByUserId(userId: string, body: PaginationDto, user: any): Promise<any> {
        const { page, pageSize } = body;
        const skip = pageSize * (page - 1);
        const query: any = {
            itemType: ENUM_PRODUCT_ITEM_TYPE.SERVICE,
            consumers: new Types.ObjectId(userId),
        };

        if (user.role.type === ENUM_ROLE_TYPE.ORGANIZATION) {
            query.organizationId = new Types.ObjectId(user._id);
            // query.userId = new Types.ObjectId(userId);
        } else if ([ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.TRAINER].includes(user.role.type)) {
            const staffDetails = await this.StaffDetailsModel.findOne({ userId: user._id }, { organizationId: 1, facilityId: 1 }).exec();

            query.organizationId = new Types.ObjectId(staffDetails.organizationId);
            // query.userId = new Types.ObjectId(userId);
            //query.facilityId = { $in: staffDetails.facilityId.map((id) => new Types.ObjectId(id)) };
        } else if (user.role.type === ENUM_ROLE_TYPE.USER) {
        } else {
            throw new BadRequestException("Access Denied");
        }

        query.isExpired = false;
        query.endDate = { $gte: new Date() };
        query.startDate = { $lte: new Date() };
        query.isActive = { $ne: false };

        const pipeline: any = [
            {
                $match: query,
            },
            {
                $lookup: {
                    from: "invoices",
                    localField: "invoiceId",
                    foreignField: "_id",
                    as: "invoiceDetails",
                },
            },
            {
                $unwind: { path: "$invoiceDetails", preserveNullAndEmptyArrays: false },
            },
            {
                $addFields: {
                    matchedPurchaseItem: {
                        $arrayElemAt: [
                            {
                                $filter: {
                                    input: "$invoiceDetails.purchaseItems",
                                    as: "item",
                                    cond: {
                                        $eq: [
                                            "$$item.packageId",
                                            {
                                                $cond: {
                                                    if: { $gt: [{ $ifNull: ["$bundledPricingId", null] }, null] },
                                                    then: "$bundledPricingId",
                                                    else: "$packageId",
                                                },
                                            },
                                        ],
                                    },
                                },
                            },
                            0,
                        ],
                    },
                },
            },
            {
                $lookup: {
                    from: "pricings",
                    localField: "packageId",
                    foreignField: "_id",
                    as: "pricingDetails",
                    pipeline: [
                        {
                            $lookup: {
                                from: "services",
                                let: {
                                    serviceCategoryId: "$services.serviceCategory",
                                    relationshipIds: { $ifNull: ["$services.relationShip.serviceCategory", []] },
                                    serviceSubTypeIds: { $ifNull: ["$services.relationShip.subTypeIds", []] }, // Extracting subTypeIds (appointmentType)
                                    serviceTypes: "$services.appointmentType", // Extracting appointmentType._id from pricing services
                                },
                                pipeline: [
                                    {
                                        $match: {
                                            $expr: {
                                                $or: [{ $eq: ["$_id", "$$serviceCategoryId"] }, { $in: ["$_id", "$$relationshipIds"] }],
                                            },
                                        },
                                    },
                                    {
                                        $project: { _id: 1, name: 1, appointmentType: 1 },
                                    },
                                    {
                                        $addFields: {
                                            filteredAppointmentType: {
                                                $filter: {
                                                    input: "$appointmentType",
                                                    as: "appt",
                                                    cond: {
                                                        $or: [
                                                            { $in: ["$$appt._id", "$$serviceTypes"] }, // Check in services.appointmentType
                                                            { $in: ["$$appt._id", "$$serviceSubTypeIds"] }, // Check in services.relationShip.subTypeIds
                                                        ],
                                                    },
                                                },
                                            },
                                        },
                                    },
                                ],
                                as: "serviceCategories",
                            },
                        },
                        {
                            $lookup: {
                                from: "services",
                                localField: "services.relationShip.subTypeIds",
                                foreignField: "appointmentType._id",
                                as: "subTypeDetails",
                            },
                        },
                        {
                            $addFields: {
                                subTypeNames: {
                                    $map: { input: "$subTypeDetails", as: "subType", in: "$$subType.name" },
                                },
                            },
                        },
                        {
                            $addFields: {
                                appointmentTypeName: {
                                    $reduce: {
                                        input: "$serviceCategories",
                                        initialValue: [],
                                        in: { $concatArrays: ["$$value", "$$this.filteredAppointmentType.name"] },
                                    },
                                },
                            },
                        },
                        {
                            $addFields: {
                                appointmentTypeName: { $concatArrays: ["$appointmentTypeName", "$subTypeNames"] },
                            },
                        },
                        {
                            $project: {
                                name: 1,
                                "services.type": 1,
                                serviceCategoryNames: {
                                    $map: { input: "$serviceCategories", as: "category", in: "$$category.name" },
                                },
                                appointmentTypeName: 1,
                                price: 1,
                                "services.sessionType": 1,
                                "services.sessionCount": 1,
                            },
                        },
                    ],
                },
            },
            {
                $unwind: { path: "$pricingDetails", preserveNullAndEmptyArrays: false },
            },
            {
                $addFields: {
                    remainingSession: {
                        $cond: {
                            if: { $eq: ["$totalSessions", Infinity] },
                            then: "Unlimited",
                            else: {
                                $subtract: [
                                    {
                                        $cond: {
                                            if: { $eq: ["$sharePass", true] },
                                            then: "$totalSessions",
                                            else: "$totalSessions",
                                        },
                                    },
                                    "$sessionConsumed",
                                ],
                            },
                        },
                    },
                },
            },
            {
                $match: { $or: [{ remainingSession: { $gt: 0 } }, { remainingSession: "Unlimited" }] },
            },
            {
                $lookup: {
                    from: "pricings",
                    localField: "bundledPricingId",
                    foreignField: "_id",
                    as: "bundledPricingDetails",
                    pipeline: [{ $project: { _id: 1, name: 1 } }],
                },
            },
            {
                $addFields: {
                    bundledPricingName: { $arrayElemAt: ["$bundledPricingDetails.name", 0] },
                    isBundledPricing: { $gt: [{ $size: "$bundledPricingDetails" }, 0] },
                },
            },
            {
                $lookup: {
                    from: "facilities",
                    localField: "facilityId",
                    foreignField: "_id",
                    as: "facilityDetails",
                    pipeline: [{ $project: { facilityName: 1 } }],
                },
            },
            {
                $unwind: { path: "$facilityDetails", preserveNullAndEmptyArrays: false },
            },
            {
                $project: {
                    _id: 1,
                    packageId: 1,
                    userId: 1,
                    consumers: 1,
                    updatedAt: 1,
                    createdAt: 1,
                    packageName: "$pricingDetails.name",
                    location: "$facilityDetails.facilityName",
                    sessionType: 1,
                    expiryDate: "$endDate",
                    type: "$pricingDetails.services.type",
                    serviceNames: "$pricingDetails.serviceCategoryNames",
                    appointmentTypeName: "$pricingDetails.appointmentTypeName",
                    // price: "$pricingDetails.price",
                    isFamilyShared: { $gt: [{ $size: "$consumers" }, 1] },
                    isOwner: { $eq: ["$userId", new Types.ObjectId(userId)] },
                    price: {
                        $cond: {
                            if: "$sharePass",
                            then: 0,
                            // else: "$matchedPurchaseItem.unitPrice",
                            else: {
                                $cond: {
                                    if: "$matchedPurchaseItem.price",
                                    then: "$matchedPurchaseItem.price",
                                    else: "$matchedPurchaseItem.unitPrice",
                                },
                            },
                        },
                    },
                    bundledPricingName: 1,
                    isBundledPricing: 1,
                    totalSessions: {
                        $cond: {
                            if: { $eq: ["$totalSessions", Infinity] },
                            then: "unlimited",
                            else: "$totalSessions",
                        },
                    },
                    sessionConsumed: 1,
                    startDate: 1,
                    suspensions: 1,
                    isActive: 1,
                    isExpired: 1,
                    remainingSession: 1,
                    dayPassLimit: 1,
                    purchaseDate: 1,
                },
            },
            {
                $facet: {
                    total: [{ $count: "count" }],
                    data: [
                        { $sort: { updatedAt: -1 } },
                        { $skip: skip },
                        { $limit: pageSize },
                        {
                            $set: {
                                suspensions: {
                                    $filter: {
                                        input: { $cond: ["$suspensions", "$suspensions", []] },
                                        as: "item",
                                        cond: {
                                            $and: [{ $ne: ["$$item.isResumed", true] }, { $gt: ["$$item.endDate", new Date()] }],
                                        },
                                    },
                                },
                            },
                        },
                        {
                            $set: {
                                isSuspended: {
                                    $cond: [{ $gt: [{ $size: "$suspensions" }, 0] }, true, false],
                                },
                            },
                        },
                    ],
                },
            },
            {
                $project: {
                    total: { $arrayElemAt: ["$total.count", 0] },
                    data: 1,
                },
            },
        ];

        const result = await this.PurchaseModel.aggregate(pipeline);
        if (result.length && result[0].data) {
            const dayPassPurchases = result.length ? result[0].data.filter((item) => item.sessionType === SessionType.DAY_PASS) : [];
            const dayPassPurchaseIds = dayPassPurchases.map((item) => new Types.ObjectId(item._id));

            const sessionsCount = await this.SchedulingModel.aggregate([
                {
                    $match: {
                        purchaseId: { $in: dayPassPurchaseIds },
                        scheduleStatus: { $nin: [ScheduleStatusType.CANCELED] },
                    },
                },
                {
                    $group: {
                        _id: { purchaseId: "$purchaseId", date: "$date" },
                    },
                },
            ]);

            const sessionsByPurchaseId = sessionsCount.reduce((acc, session) => {
                const purchaseId = session._id.purchaseId.toString();
                const date = session._id.date.toISOString().split("T")[0];
                if (!acc[purchaseId]) acc[purchaseId] = [];
                acc[purchaseId].push(date);
                return acc;
            }, {});

            const todayUTC = new Date();
            todayUTC.setUTCHours(0, 0, 0, 0);
            const todayISTDate = todayUTC.toISOString().split("T")[0];

            for (const item of result[0]?.data || []) {
                if (item.sessionType === SessionType.DAY_PASS) {
                    const bookedDates = sessionsByPurchaseId[item._id.toString()] || [];
                    //const remainingDays = bookedDates.filter((date) => date < todayISTDate);
                    const consumedDayPassLimit = bookedDates.length;
                    const assignedDayPassLimit = item.dayPassLimit || 0;
                    item.remainingSession = !isNaN(assignedDayPassLimit - consumedDayPassLimit) ? `${assignedDayPassLimit - consumedDayPassLimit} x Day Pass(es)` : 0;
                }
            }
        }

        return {
            data: result.length ? result[0].data : [],
            count: result.length ? result[0].total : 0,
        };
    }

    async inactivePricingByUserId(userId: string, body: PaginationDto, organizationId: IDatabaseObjectId): Promise<any> {
        const { page, pageSize } = body;
        const skip = pageSize * (page - 1);

        const query: any = {
            itemType: ENUM_PRODUCT_ITEM_TYPE.SERVICE,
        };
        query.consumers = new Types.ObjectId(userId);
        query.organizationId = new Types.ObjectId(organizationId);

        query.$or = [{ isActive: false }, { isExpired: true }, { remainingSession: { $lte: 0 } }, { endDate: { $lt: new Date() } }];

        const pipeline: any = [
            {
                $lookup: {
                    from: "pricings",
                    localField: "packageId",
                    foreignField: "_id",
                    as: "pricingDetails",
                    pipeline: [
                        {
                            $lookup: {
                                from: "services",
                                let: {
                                    serviceCategoryId: "$services.serviceCategory",
                                    relationshipIds: { $ifNull: ["$services.relationShip.serviceCategory", []] },
                                    serviceSubTypeIds: { $ifNull: ["$services.relationShip.subTypeIds", []] }, // Extracting subTypeIds (appointmentType)
                                    serviceTypes: "$services.appointmentType", // Extracting appointmentType._id from pricing services
                                },
                                pipeline: [
                                    {
                                        $match: {
                                            $expr: {
                                                $or: [{ $eq: ["$_id", "$$serviceCategoryId"] }, { $in: ["$_id", "$$relationshipIds"] }],
                                            },
                                        },
                                    },
                                    {
                                        $project: { _id: 1, name: 1, appointmentType: 1 },
                                    },
                                    {
                                        $addFields: {
                                            filteredAppointmentType: {
                                                $filter: {
                                                    input: "$appointmentType",
                                                    as: "appt",
                                                    cond: {
                                                        $or: [
                                                            { $in: ["$$appt._id", "$$serviceTypes"] }, // Check in services.appointmentType
                                                            { $in: ["$$appt._id", "$$serviceSubTypeIds"] }, // Check in services.relationShip.subTypeIds
                                                        ],
                                                    },
                                                },
                                            },
                                        },
                                    },
                                ],
                                as: "serviceCategories",
                            },
                        },
                        {
                            $lookup: {
                                from: "services",
                                localField: "services.relationShip.subTypeIds",
                                foreignField: "appointmentType._id",
                                as: "subTypeDetails",
                            },
                        },
                        {
                            $addFields: {
                                subTypeNames: {
                                    $map: { input: "$subTypeDetails", as: "subType", in: "$$subType.name" },
                                },
                            },
                        },
                        {
                            $addFields: {
                                appointmentTypeName: {
                                    $reduce: {
                                        input: "$serviceCategories",
                                        initialValue: [],
                                        in: { $concatArrays: ["$$value", "$$this.filteredAppointmentType.name"] },
                                    },
                                },
                            },
                        },
                        {
                            $addFields: {
                                appointmentTypeName: { $concatArrays: ["$appointmentTypeName", "$subTypeNames"] },
                            },
                        },
                        {
                            $project: {
                                name: 1,
                                "services.type": 1,
                                serviceCategoryNames: {
                                    $map: { input: "$serviceCategories", as: "category", in: "$$category.name" },
                                },
                                appointmentTypeName: 1,
                                price: 1,
                                "services.sessionType": 1,
                                "services.sessionCount": 1,
                            },
                        },
                    ],
                },
            },
            {
                $unwind: { path: "$pricingDetails", preserveNullAndEmptyArrays: false },
            },
            {
                $addFields: {
                    remainingSession: {
                        $cond: {
                            if: { $eq: ["$totalSessions", Infinity] },
                            then: "Unlimited",
                            else: {
                                $subtract: [
                                    {
                                        $cond: {
                                            if: { $eq: ["$sharePass", true] },
                                            then: "$totalSessions",
                                            else: "$totalSessions",
                                        },
                                    },
                                    "$sessionConsumed",
                                ],
                            },
                        },
                    },
                },
            },
            {
                $match: query,
            },
            {
                $lookup: {
                    from: "invoices",
                    localField: "invoiceId",
                    foreignField: "_id",
                    as: "invoiceDetails",
                },
            },
            {
                $unwind: { path: "$invoiceDetails", preserveNullAndEmptyArrays: false },
            },
            {
                $addFields: {
                    matchedPurchaseItem: {
                        $arrayElemAt: [
                            {
                                $filter: {
                                    input: "$invoiceDetails.purchaseItems",
                                    as: "item",
                                    cond: {
                                        $eq: [
                                            "$$item.packageId",
                                            {
                                                $cond: {
                                                    if: { $gt: [{ $ifNull: ["$bundledPricingId", null] }, null] },
                                                    then: "$bundledPricingId",
                                                    else: "$packageId",
                                                },
                                            },
                                        ],
                                    },
                                },
                            },
                            0,
                        ],
                    },
                },
            },
            {
                $lookup: {
                    from: "pricings",
                    localField: "bundledPricingId",
                    foreignField: "_id",
                    as: "bundledPricingDetails",
                    pipeline: [{ $project: { _id: 1, name: 1 } }],
                },
            },
            {
                $addFields: {
                    bundledPricingName: { $arrayElemAt: ["$bundledPricingDetails.name", 0] },
                    isBundledPricing: { $gt: [{ $size: "$bundledPricingDetails" }, 0] },
                },
            },
            {
                $lookup: {
                    from: "facilities",
                    localField: "facilityId",
                    foreignField: "_id",
                    as: "facilityDetails",
                    pipeline: [{ $project: { facilityName: 1 } }],
                },
            },
            {
                $unwind: { path: "$facilityDetails", preserveNullAndEmptyArrays: false },
            },
            {
                $project: {
                    _id: 1,
                    userId: 1,
                    consumers: 1,
                    packageId: 1,
                    updatedAt: 1,
                    createdAt: 1,
                    packageName: "$pricingDetails.name",
                    location: "$facilityDetails.facilityName",
                    sessionType: 1,
                    expiryDate: "$endDate",
                    type: "$pricingDetails.services.type",
                    serviceNames: "$pricingDetails.serviceCategoryNames",
                    appointmentTypeName: "$pricingDetails.appointmentTypeName",
                    // price: "$pricingDetails.price",
                    isFamilyShared: { $gt: [{ $size: "$consumers" }, 1] },
                    isOwner: { $eq: ["$userId", new Types.ObjectId(userId)] },
                    price: {
                        $cond: {
                            if: "$sharePass",
                            then: 0,
                            else: {
                                $cond: {
                                    if: "$matchedPurchaseItem.price",
                                    then: "$matchedPurchaseItem.price",
                                    else: "$matchedPurchaseItem.unitPrice",
                                },
                            },
                        },
                    },
                    bundledPricingName: 1,
                    isBundledPricing: 1,
                    totalSessions: {
                        $cond: {
                            if: { $eq: ["$totalSessions", Infinity] },
                            then: "unlimited",
                            else: "$totalSessions",
                        },
                    },
                    sessionConsumed: 1,
                    isActive: 1,
                    startDate: 1,
                    isExpired: 1,
                    suspensions: 1,
                    remainingSession: 1,
                    paymentStatus: 1,
                    dayPassLimit: 1,
                },
            },
            {
                $facet: {
                    total: [{ $count: "count" }],
                    data: [
                        { $sort: { updatedAt: -1 } },
                        { $skip: skip },
                        { $limit: pageSize },
                        {
                            $set: {
                                suspensions: {
                                    $filter: {
                                        input: { $cond: ["$suspensions", "$suspensions", []] },
                                        as: "item",
                                        cond: {
                                            $and: [{ $ne: ["$$item.isResumed", true] }, { $gt: ["$$item.endDate", new Date()] }],
                                        },
                                    },
                                },
                            },
                        },
                        {
                            $set: {
                                isSuspended: {
                                    $cond: [{ $gt: [{ $size: "$suspensions" }, 0] }, true, false],
                                },
                            },
                        },
                    ],
                },
            },
            {
                $project: {
                    total: { $arrayElemAt: ["$total.count", 0] },
                    data: 1,
                },
            },
        ];

        const result = await this.PurchaseModel.aggregate(pipeline);
        if (result.length && result[0].data) {
            const dayPassPurchases = result.length ? result[0].data.filter((item) => item.sessionType === SessionType.DAY_PASS) : [];
            const dayPassPurchaseIds = dayPassPurchases.map((item) => new Types.ObjectId(item._id));

            const sessionsCount = await this.SchedulingModel.aggregate([
                {
                    $match: {
                        purchaseId: { $in: dayPassPurchaseIds },
                        scheduleStatus: { $nin: [ScheduleStatusType.CANCELED] },
                    },
                },
                {
                    $group: {
                        _id: { purchaseId: "$purchaseId", date: "$date" },
                    },
                },
            ]);

            const sessionsByPurchaseId = sessionsCount.reduce((acc, session) => {
                const purchaseId = session._id.purchaseId.toString();
                const date = session._id.date.toISOString().split("T")[0];
                if (!acc[purchaseId]) acc[purchaseId] = [];
                acc[purchaseId].push(date);
                return acc;
            }, {});

            const todayUTC = new Date();
            todayUTC.setUTCHours(0, 0, 0, 0);
            const todayISTDate = todayUTC.toISOString().split("T")[0];

            for (const item of result[0]?.data || []) {
                if (item.sessionType === SessionType.DAY_PASS) {
                    const bookedDates = sessionsByPurchaseId[item._id.toString()] || [];
                    //const remainingDays = bookedDates.filter((date) => date < todayISTDate);
                    const consumedDayPassLimit = bookedDates.length;
                    const assignedDayPassLimit = item.dayPassLimit || 0;
                    item.remainingSession = !isNaN(assignedDayPassLimit - consumedDayPassLimit) ? `${assignedDayPassLimit - consumedDayPassLimit} x Day Pass(es)` : 0;
                }
            }
        }

        return {
            data: result.length ? result[0].data : [],
            count: result.length ? result[0].total : 0,
        };
    }

    private async getOrganizationId(user: IUserDocument) {
        const { role } = user;
        switch (role.type) {
            case ENUM_ROLE_TYPE.USER:
                return user._id;

            case ENUM_ROLE_TYPE.ORGANIZATION:
                return user._id;

            case ENUM_ROLE_TYPE.FRONT_DESK_ADMIN:
            case ENUM_ROLE_TYPE.TRAINER:
            case ENUM_ROLE_TYPE.WEB_MASTER:
                const staffDetails = await this.StaffDetailsModel.findOne({ userId: user._id }, { organizationId: 1 });
                if (!staffDetails) return null;
                return staffDetails.organizationId;

            default:
                null;
        }
    }

    async getInvoiceList(organizationId: IDatabaseObjectId, body: GetInvoicesDto) {
        const { search, facilityId, classType, serviceCategory, startDate, endDate, paymentStatus, page, pageSize } = body;
        const skip = pageSize * (page - 1);
        const filter: any = { organizationId: organizationId };
        const serviceCategoryIds = serviceCategory.map((_id) => new Types.ObjectId(_id));
        if (facilityId.length) {
            filter["facilityId"] = { $in: facilityId.map((_id) => new Types.ObjectId(_id)) };
        }
        // if(classType){
        //     filter['classType'] = classType
        // }
        // if(serviceCategory.length){
        //     filter['serviceCategoryId'] = { $in: serviceCategory.map(_id => new Types.ObjectId(_id))}
        // }
        if (paymentStatus) {
            filter["paymentStatus"] = paymentStatus;
        }
        if (startDate && endDate) {
            filter["invoiceDate"] = {
                $gte: startDate,
                $lte: endDate,
            };
        }

        const agg: PipelineStage[] = [
            {
                $match: filter,
            },

            {
                $unwind: {
                    path: "$purchaseItems",
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $unwind: {
                    path: "$customPackageItems",
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $unwind: {
                    path: "$productItem",
                    preserveNullAndEmptyArrays: true,
                },
            },
            ...(serviceCategory.length || classType || search
                ? [
                    {
                        $lookup: {
                            from: "pricings",
                            localField: "purchaseItems.packageId",
                            foreignField: "_id",
                            as: "pricing",
                            pipeline: [
                                {
                                    $project: {
                                        name: 1,
                                        services: 1,
                                        hsnOrSacCode: 1,
                                    },
                                },
                            ],
                        },
                    },
                    {
                        $match: {
                            pricing: {
                                $ne: [],
                            },
                        },
                    },
                ]
                : []),
            ...(classType
                ? [
                    {
                        $match: {
                            "pricing.services.type": classType,
                        },
                    },
                ]
                : []),
            ...(serviceCategoryIds.length
                ? [
                    {
                        $match: {
                            "pricing.services.serviceCategory": { $in: serviceCategoryIds },
                        },
                    },
                ]
                : []),
            ...(search
                ? [
                    {
                        $lookup: {
                            from: "users",
                            localField: "userId",
                            foreignField: "_id",
                            as: "client",
                            pipeline: [
                                {
                                    $project: {
                                        name: 1,
                                        firstName: 1,
                                        lastName: 1,
                                        mobile: 1,
                                    },
                                },
                            ],
                        },
                    },
                    {
                        $set: {
                            client: {
                                $arrayElemAt: ["$client", 0],
                            },
                            pricing: {
                                $arrayElemAt: ["$pricing", 0],
                            },
                        },
                    },
                    {
                        $addFields: {
                            searched: {
                                $cond: [
                                    {
                                        $or: [
                                            {
                                                $regexMatch: {
                                                    input: "$client.name",
                                                    regex: search,
                                                    options: "i",
                                                },
                                            },
                                            {
                                                $regexMatch: {
                                                    input: "$client.mobile",
                                                    regex: search,
                                                    options: "i",
                                                },
                                            },
                                            {
                                                $regexMatch: {
                                                    input: "$pricing.name",
                                                    regex: search,
                                                    options: "i",
                                                },
                                            },
                                            {
                                                $regexMatch: {
                                                    input: {
                                                        $toString: "$orderId",
                                                    },
                                                    regex: search,
                                                    options: "i",
                                                },
                                            },
                                        ],
                                    },
                                    true,
                                    false,
                                ],
                            },
                        },
                    },
                    {
                        $match: {
                            searched: true,
                        },
                    },
                ]
                : []),
            {
                $group: {
                    _id: "$_id",
                    updatedAt: {
                        $first: "$updatedAt",
                    },
                },
            },
            {
                $facet: {
                    total: [
                        {
                            $count: "count",
                        },
                    ],
                    invoiceIds: [
                        {
                            $sort: {
                                updatedAt: -1,
                            },
                        },
                        {
                            $skip: skip,
                        },
                        {
                            $limit: pageSize,
                        },
                    ],
                },
            },
            {
                $replaceRoot: {
                    newRoot: {
                        total: {
                            $arrayElemAt: ["$total.count", 0],
                        },
                        invoiceIds: "$invoiceIds",
                    },
                },
            },
        ];

        const idsList = await this.InvoiceModel.aggregate(agg);
        const { total = 0, invoiceIds } = idsList.length ? idsList[0] : { total: 0, invoiceIds: [] };
        const populateFields = [
            { path: "createdBy", select: "_id name firstName lastName email" },
            { path: "paymentBy", select: "_id name firstName lastName email" },
            {
                path: "facilityId",
                select: "_id facilityName address profilePicture paymentMethods", // ✅ Remove comma before 'paymentMethods'
                populate: {
                    path: "paymentMethods.paymentMethodId",
                    model: "PaymentMethod",
                    select: "_id name methodType shortId", // ✅ Added shortId
                },
            },
        ];

        let invoices = await this.InvoiceModel.find({ _id: { $in: invoiceIds.map((item) => item._id) } })
            .sort({ createdAt: -1 })
            .populate(populateFields);
        const updatedInvoices = invoices.map((invoice: any) => {
            if (invoice.facilityId && invoice.facilityId.paymentMethods) {
                // ✅ Convert to plain JavaScript object
                const invoiceObject = JSON.parse(JSON.stringify(invoice));

                const updatedPaymentDetails = invoiceObject.paymentDetails.map((detail: any) => {
                    const matchedMethod = invoiceObject.facilityId.paymentMethods.find(
                        (pm: any) =>
                            pm.paymentMethodId &&
                            pm.paymentMethodId.shortId &&
                            detail.paymentMethod &&
                            pm.paymentMethodId.shortId.trim().toLowerCase() === detail.paymentMethod.trim().toLowerCase(),
                    );

                    return {
                        ...detail,
                        paymentMethodName: matchedMethod ? matchedMethod.paymentMethodId.name : "Unknown",
                    };
                });

                // ✅ Update the converted object
                invoiceObject.paymentDetails = updatedPaymentDetails;
                invoiceObject.updatedPaymentDetails = updatedPaymentDetails;

                return invoiceObject; // ✅ Return modified invoice
            }

            return invoice; // ✅ Return original if no changes were made
        });
        invoices = updatedInvoices;
        return {
            count: total,
            data: invoices.map((item: any) => ({
                _id: item._id,
                createdBy: item.createdBy ? item.createdBy._id : null,
                createdByName: item.createdBy ? (item.createdBy.name ? item.createdBy.name : item.createdBy.firstName + " " + item.createdBy.lastName) : null,
                paymentBy: item.paymentBy ? item.paymentBy._id : null,
                paymentByName: item.paymentBy ? (item.paymentBy.name ? item.paymentBy.name : item.paymentBy.firstName + " " + item.paymentBy.lastName) : null,
                invoiceDate: item?.invoiceDate,
                invoiceNumber: item?.invoiceNumber || "",
                orderId: item?.orderId || "",
                organizationId: item.organizationId,
                userId: item?.userId,
                userName: item?.clientDetails?.name || "",
                customerId: item?.clientDetails?.customerId || "",
                staffId: null,
                staffName: "",
                totalItems: item.purchaseItems.length,
                purchaseItems: item.purchaseItems.map((pricing: any) => ({
                    _id: pricing?._id,
                    packageId: pricing?.packageId,
                    name: pricing?.packageName,
                    hsnOrSacCode: pricing?.hsnOrSacCode || "",
                    tax: pricing?.tax,
                    price: pricing?.price || pricing?.unitPrice,
                    unitPrice: pricing?.unitPrice,
                    quantity: pricing?.quantity,
                })),
                productItems: item.productItem.map((product: any) => ({
                    _id: product?._id,
                    productId: product?.productId,
                    productName: product?.productName,
                    hsnorSacCode: product?.hsnOrSacCode || "",
                    price: product?.salePrice,
                    unitPrice: product?.unitPrice,
                    slePrice: product?.salePrice,
                    quantity: product?.quantity,
                })),
                customPackageItems: item.customPackageItems.map((pricing: any) => ({
                    _id: pricing?._id,
                    customPackageId: pricing?.customPackageId,
                    name: pricing?.packageName,
                    hsnOrSacCode: pricing?.hsnOrSacCode || "",
                    price: pricing?.unitPrice,
                    unitPrice: pricing?.unitPrice,
                    quantity: pricing?.quantity,
                })),
                facilityId: item.facilityId._id,
                facilityName: item.facilityId.facilityName,
                discount: item?.discount || 0,
                subTotal: item?.subTotal,
                total: item?.grandTotal,
                paymentStatus: item?.paymentStatus,
                paymentMethod: item?.paymentDetails?.map((detail) => detail.paymentMethod).join(", "),
                transactionId: item?.paymentDetails[0]?.transactionId,
                paymentDate: item?.paymentDetails[0]?.paymentDate,
                invoiceStatus: item?.invoiceStatus,
                refundStatus: item?.refundStatus,
                platform: item?.platform,
                updatedPaymentDetails: item?.updatedPaymentDetails?.map((detail) => detail.paymentMethodName).join(", "),
                paymentReason: item?.paymentReason,
            })),
        };
    }

    async exportInvoiceList(user: IUserDocument, body: ExportInvoicesDto, userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone) {
        const organizationId = await this.getOrganizationId(user);
        const { facilityId = [], classType, serviceCategory = [], startDate, endDate, paymentStatus } = body;
        const filter: any = { organizationId };
        if (facilityId.length) {
            filter.facilityId = { $in: facilityId.map((_id) => new Types.ObjectId(_id)) };
        }
        // if(classType){
        //     filter.classType = classType
        // }
        // if(serviceCategory.length){
        //     filter.serviceCategoryId = { $in: serviceCategory.map(_id => new Types.ObjectId(_id))}
        // }
        if (paymentStatus) {
            filter.paymentStatus = paymentStatus;
        }
        if (startDate && endDate) {
            filter.invoiceDate = {
                $gte: new Date(new Date(startDate).setHours(0, 0, 0, 0)),
                $lte: new Date(new Date(endDate).setHours(23, 59, 59, 999)),
            };
        }

        const serviceCategoryIds = serviceCategory.map((_id) => new Types.ObjectId(_id));

        const orgData = await this.OrganizationsModel.findOne({ userId: organizationId }, "isInclusiveofGst").lean();
        const isInclusiveofGst = Boolean(orgData?.isInclusiveofGst);

        const pipeline: PipelineStage[] = [
            { $match: filter },
            { $unwind: { path: "$purchaseItems", preserveNullAndEmptyArrays: true } },
            ...(serviceCategoryIds.length || classType
                ? [
                    {
                        $lookup: {
                            from: "pricings",
                            localField: "purchaseItems.packageId",
                            foreignField: "_id",
                            as: "pricing",
                            pipeline: [{ $project: { name: 1, services: 1, hsnOrSacCode: 1 } }],
                        },
                    },
                    { $match: { pricing: { $ne: [] } } },
                ]
                : []),
            ...(classType ? [{ $match: { "pricing.services.type": classType } }] : []),
            ...(serviceCategoryIds.length ? [{ $match: { "pricing.services.serviceCategory": { $in: serviceCategoryIds } } }] : []),
            {
                $group: { _id: "$_id", updatedAt: { $first: "$updatedAt" } },
            },
            {
                $facet: {
                    total: [{ $count: "count" }],
                    invoiceIds: [{ $sort: { updatedAt: -1 } }],
                },
            },
            {
                $replaceRoot: {
                    newRoot: {
                        total: { $arrayElemAt: ["$total.count", 0] },
                        invoiceIds: "$invoiceIds",
                    },
                },
            },
        ];
        const aggResult = await this.InvoiceModel.aggregate(pipeline);
        const { total = 0, invoiceIds = [] } = aggResult[0] || { total: 0, invoiceIds: [] };
        const populateFields = [
            { path: "userId", select: "_id name firstName lastName email" },
            { path: "createdBy", select: "_id name firstName lastName email" },
            { path: "cancelledBy", select: "firstName lastName name" },
            // { path: 'organizationId', select: '_id name email' },
            {
                path: "facilityId",
                select: "_id facilityName paymentMethods name",
                populate: { path: "paymentMethods.paymentMethodId", model: "PaymentMethod", select: "_id name methodType" },
            },
            // {
            //     path: "purchaseItems.packageId",
            //     select: "_id name price services expiredInDays durationUnit hsnOrSacCode",
            // },
        ];
        const invoices = await this.InvoiceModel.find({ _id: { $in: invoiceIds.map((item) => item._id) } })
            .sort({ updatedAt: -1 })
            .populate(populateFields);
        const clients = await this.ClientModel.find({ userId: { $in: invoices.map((item: any) => item?.userId?._id) } }, { userId: 1, clientId: 1 });

        const clientMap = new Map(clients.map((c) => [c.userId.toString(), c.clientId]));

        const columns = isInclusiveofGst
            ? `Client ID,Order ID,Client Name,Billed To,Billing Address,Location,Order Date (${userTimezone}),Sub Total (Incl. Tax) (INR),Discount (Incl. Tax) (INR),Amount After Discount (Incl. Tax) (INR),GST Amount (INR),Amount (Incl. Tax) (INR),Return Amount (Incl. Tax) (INR),Grand Total (INR),Staff,Payment Method,Payment Status,UT Code,Notes/TXN ID,Cancelled By,Reason\n`
            : `Client ID,Order ID,Client Name,Billed To,Billing Address,Location,Order Date (${userTimezone}),Sub Total (Excl. Tax) (INR),Discount (Excl. Tax) (INR),Amount After Discount (Excl. Tax) (INR),GST Amount (INR),Amount (Incl. Tax) (INR),Return Amount (Incl. Tax) (INR),Grand Total (INR),Staff,Payment Method,Payment Status,UT Code,Notes/TXN ID,Cancelled By,Reason\n`;

        const rows = invoices.map((item: any) => {
            const address = [
                item.clientBillingDetails?.addressLine1,
                item.clientBillingDetails?.addressLine2,
                item.clientBillingDetails?.cityName,
                item.clientBillingDetails?.stateName,
                item.clientBillingDetails?.country,
            ]
                .filter(Boolean)
                .join(", ");

            const creator = item.createdBy?.name || [item.createdBy?.firstName, item.createdBy?.lastName].filter(Boolean).join(" ") || "";

            const cancelledBy = item.cancelledBy?.name || [item.cancelledBy?.firstName, item.cancelledBy?.lastName].filter(Boolean).join(" ") || "";

            const paymentDetails = Array.isArray(item.paymentDetails) ? item.paymentDetails : item.paymentDetails ? [item.paymentDetails] : [];
            const updatedPaymentDetails = paymentDetails.map((detail) => {
                const matchedMethod = item.facilityId.paymentMethods.find((pm) => pm.shortId.trim().toLowerCase() === detail.paymentMethod.trim().toLowerCase());
                const cleanDetail = JSON.parse(JSON.stringify(detail));
                return {
                    ...cleanDetail,
                    paymentMethodName: matchedMethod?.name ?? "Unknown",
                };
            });

            const totalDiscount = (item.itemDiscount || 0) + (item.cartDiscountAmount || 0);
            const subTotal =
                (item.productItem || []).reduce((sum, obj) => sum + ((obj.price ?? obj.salePrice) || 0) * (obj.quantity || 0), 0) +
                (item.purchaseItems || []).reduce((sum, obj) => sum + ((obj.price ?? obj.unitPrice) || 0) * (obj.quantity || 0), 0) +
                (item.customPackageItems || []).reduce((sum, obj) => sum + ((obj.price ?? obj.unitPrice) || 0), 0);
            const totalGst =
                (item.productItem || []).reduce((sum, obj) => (sum + obj.gstAmount + 0), 0) +
                (item.purchaseItems || []).reduce((sum, obj) => (sum + obj.gstAmount + 0), 0) +
                (item.customPackageItems || []).reduce((sum, obj) => (sum + obj.gstAmount + 0), 0);
            const amountAfterDiscount = subTotal - totalDiscount;
            const totalAmount = isInclusiveofGst ? amountAfterDiscount - (item.voucherDiscount || 0) : amountAfterDiscount + (totalGst || 0) - (item.voucherDiscount || 0);

            let paymentMethod = item.voucherDiscount ? `Voucher-${item.voucherDiscount}` : "";
            if (item.paymentStatus === PaymentStatus.COMPLETED) {
                const methods = updatedPaymentDetails?.map((payment) => `${payment.paymentMethodName || "UNKNOWN"}-${payment.amount}`).join(" | ");

                paymentMethod = paymentMethod ? `${paymentMethod} | ${methods}` : methods;
            }

            return [
                clientMap.get(item?.userId?._id?.toString()) ?? "",
                item.orderId ?? "",
                item.clientDetails?.name ?? "",
                item.clientBillingDetails?.name ?? "",
                `"${address}"`,
                item.facilityId?.facilityName ?? "",
                moment(item.invoiceDate).tz(userTimezone).format("DD/MM/YYYY"),
                subTotal?.toFixed(2) ?? 0,
                totalDiscount.toFixed(2),
                amountAfterDiscount.toFixed(2),
                totalGst.toFixed(2) ?? 0,
                totalAmount?.toFixed(2) ?? 0,
                ((item?.totalReturnValue ?? 0) * -1).toFixed(2) ?? 0,
                (totalAmount - item.totalReturnValue)?.toFixed(2) ?? 0,
                creator,
                paymentMethod,
                this.getPaymentStatusLabel(item.paymentStatus),
                item.clientBillingDetails?.utCode ?? "",
                updatedPaymentDetails
                    .map((p) => p?.transactionId)
                    .filter(Boolean)
                    .join(", "),
                cancelledBy,
                item.paymentReason ? `"${item.paymentReason}"` : "",
            ].join(",");
        });

        const footer = [
            `Generated By,${(user.firstName || "").toUpperCase()} ${(user.lastName || "").toUpperCase()}`,
            `Date,${moment().tz(userTimezone).format("DD/MM/YYYY HH:mm:ss")}`,
        ];

        const csv = [columns, ...rows, ...footer].join("\n");

        return { count: total, data: csv };
    }

    async getInvoiceDetails(organizationId: IDatabaseObjectId, invoiceId: string) {
        let invoice: any = await this.InvoiceModel.findOne({
            _id: invoiceId,
            organizationId: organizationId,
        }).populate([
            { path: "createdBy", select: "_id name firstName lastName email" },
            { path: "cancelledBy", select: "_id firstName lastName name" },
            { path: "paymentBy", select: "_id name firstName lastName email" },
            { path: "discountedBy", select: "_id name firstName lastName email" },
            {
                path: "facilityId",
                select: "_id facilityName paymentMethods name",
                populate: { path: "paymentMethods.paymentMethodId", model: "PaymentMethod", select: "_id name methodType" },
            }, // Nested population
            {
                path: "purchaseItems.discountedBy",
                select: "_id name firstName lastName email",
            },
        ]);
        if (invoice && invoice.facilityId && invoice.facilityId.paymentMethods) {
            const updatedPaymentDetails = invoice.paymentDetails.map((detail) => {
                const matchedMethod = invoice.facilityId.paymentMethods.find((pm) => pm.shortId.trim().toLowerCase() === detail.paymentMethod.trim().toLowerCase());
                const cleanDetail = JSON.parse(JSON.stringify(detail));
                return {
                    ...cleanDetail,
                    paymentMethodName: matchedMethod ? matchedMethod.name : "Unknown",
                };
            });
            invoice.updatedPaymentDetails = updatedPaymentDetails;
        }

        if (!invoice) {
            throw new NotFoundException("Invoice not found or you do not have access to it");
        }

        const purchaseItemsDetails = await Promise.all(
            invoice.purchaseItems.map(async (item: any) => {
                const purchaseData: any = await this.PurchaseModel.findOne({
                    $or: [
                        {
                            userId: invoice.userId,
                        },
                        {
                            sponsorUser: invoice.userId,
                        },
                    ],
                    packageId: item?.packageId,
                    invoiceId: invoiceId,
                })
                    .populate([{ path: "packageId", select: "_id services isInclusiveofGst  finalPrice " }])
                    .lean();
                const pkg = purchaseData?.packageId || {};
                const isInclusiveofGst = pkg?.isInclusiveofGst ?? pkg?.isInclusiveofGst ?? false;
                return {
                    _id: item?._id,
                    packageId: item?.packageId,
                    purchaseIds: item?.purchaseIds,
                    discountedBy: item?.discountedBy ? item?.discountedBy._id : null,
                    discountedByName: item?.discountedBy
                        ? item?.discountedBy.name
                            ? item?.discountedBy.name
                            : item?.discountedBy.firstName + " " + item?.discountedBy.lastName
                        : null,
                    classType: purchaseData?.packageId?.services?.type || "",
                    isBundledPricing: item?.isBundledPricing,
                    sessionType: purchaseData?.sessionType,
                    itemType: purchaseData?.itemType,
                    name: item?.packageName,
                    price: item.price ?? item?.unitPrice,
                    unitPrice: item?.unitPrice,
                    tax: item?.tax,
                    hsnOrSacCode: item?.hsnOrSacCode || "",
                    expiredInDays: item?.expireIn,
                    durationUnit: item?.durationUnit,
                    discountType: item?.discountType || "",
                    discountValue: item?.discountExcludeCart || 0,
                    quantity: item?.quantity,
                    startDate: item?.startDate,
                    endDate: item?.endDate,
                    promotionLabel: item?.promotionLabel,
                    discountExcludeCart: item?.discountExcludeCart,
                    discountIncludeCart: item?.discountIncludeCart,
                    returnDiscountAmount: item?.returnDiscountAmount,
                    voucherDiscountAmount: item?.voucherDiscountAmount,
                    isInclusiveofGst,
                    finalPrice: pkg?.finalPrice ?? null,
                };
            }),
        );

        // const returnPurchaseItemsDetails = await Promise.all(
        //     invoice.returnItems?.map(async (purchaseId: any) => {
        //         const purchaseData = await this.PurchaseModel.findOne({
        //             _id: purchaseId,
        //         }).lean();
        //         const item = await this.PricingModel.findOne({
        //             _id: purchaseData.packageId,
        //         });
        //         return {
        //             packageId: item?._id,
        //             purchaseIds: purchaseId,
        //             classType: item?.services?.type || "",
        //             isBundledPricing: item?.isBundledPricing,
        //             sessionType: purchaseData?.sessionType,
        //             itemType: purchaseData?.itemType,
        //             name: item?.name,
        //             price: item?.price,
        //             tax: item?.tax,
        //             hsnOrSacCode: item?.hsnOrSacCode || "",
        //             expiredInDays: item?.expiredInDays,
        //             durationUnit: item?.durationUnit,
        //             discountType: "",
        //             discountValue: 0,
        //             quantity: 1,
        //             startDate: purchaseData?.startDate,
        //             endDate: purchaseData?.endDate,
        //             exchangedOn: invoice.returnDetails?.exchangedOn,
        //         };
        //     }) || [],
        // );

        const appliedVoucherItemsDetails = await Promise.all(
            invoice.voucherDetails?.voucherIds?.map(async (purchaseId: any) => {
                const purchaseData = await this.PurchaseModel.findOne({
                    _id: purchaseId,
                }).lean();
                const item = await this.PricingModel.findOne({
                    _id: purchaseData.packageId,
                });
                return {
                    packageId: item?._id,
                    purchaseIds: purchaseId,
                    itemType: purchaseData?.itemType,
                    name: item?.name,
                    price: item?.price,
                    tax: item?.tax,
                    hsnOrSacCode: item?.hsnOrSacCode || "",
                    expiredInDays: item?.expiredInDays,
                    durationUnit: item?.durationUnit,
                    discountType: "",
                    discountValue: 0,
                    quantity: 1,
                    startDate: purchaseData?.startDate,
                    endDate: purchaseData?.endDate,
                    exchangedOn: invoice.voucherDetails?.appliedOn,
                };
            }) || [],
        );

        const customPackageItemDetails = invoice.customPackageItems.map((item: any) => {
            return {
                _id: item?._id,
                customPackageId: item?.customPackageId,
                name: item?.packageName,
                discountedBy: item?.discountedBy ? item?.discountedBy._id : null,
                discountedByName: item?.discountedBy
                    ? item?.discountedBy.name
                        ? item?.discountedBy.name
                        : item?.discountedBy.firstName + " " + item?.discountedBy.lastName
                    : null,
                price: item?.price || item?.unitPrice,
                unitPrice: item?.unitPrice,
                tax: item?.tax,
                hsnOrSacCode: item?.hsnOrSacCode || "",
                discountType: item?.discountType || "",
                discountValue: item?.discountExcludeCart || 0,
                quantity: item?.quantity,
                discountExcludeCart: item?.discountExcludeCart,
                discountIncludeCart: item?.discountIncludeCart,
                returnDiscountAmount: item?.returnDiscountAmount,
                voucherDiscountAmount: item?.voucherDiscountAmount,
            };
        });

        const returnPurchaseCustomItemsDetails = await Promise.all(
            invoice.returnCustomPackageDetails?.returnPurchaseIds?.map(async (purchaseId: any) => {
                const purchase: any = await this.InvoiceModel.findOne(
                    {
                        "customPackageItems._id": purchaseId,
                    },
                    {
                        customPackageItems: {
                            $elemMatch: {
                                _id: purchaseId,
                            },
                        },
                    },
                );
                const packagaeId = purchase?.customPackageItems?.length ? purchase?.customPackageItems[0]?.customPackageId : null;
                if (!packagaeId) {
                    throw new InternalServerErrorException("Unable to fetch return custom item");
                }
                const item = await this.customPackageModel.findOne({
                    _id: packagaeId,
                });
                if (!item) {
                    throw new InternalServerErrorException("Unable to fetch return custom item");
                }
                return {
                    packageId: item?._id,
                    purchaseIds: purchaseId,
                    isBundledPricing: false,
                    name: item?.name,
                    price: item?.unitPrice,
                    tax: item?.tax,
                    hsnOrSacCode: item?.hsnOrSacCode || "",
                    discountType: "",
                    discountValue: 0,
                    quantity: item.quantity,
                    exchangedOn: invoice.returnCustomPackageDetails?.exchangedOn,
                };
            }) || [],
        );

        const productItemDetails = invoice.productItem.map((item: any) => ({
            _id: item?._id,
            productId: item?.productId,
            productVariantId: item?.productVariantId || null,
            name: item?.productName || "",
            quantity: item?.quantity || 1,
            price: item?.price || item?.salePrice || 0,
            unitPrice: item?.unitPrice || item?.salePrice || 0,
            mrp: item?.mrp || 0,
            discountType: item?.discountType || "percentage",
            discountValue: item?.discountExcludeCart || 0,
            discountIncludeCart: item?.discountIncludeCart || 0,
            hsnOrSacCode: item?.hsnOrSacCode || "",
            tax: item?.tax || 0,
            gstAmount: item?.gstAmount || 0,
        }));

        const clientBillingUTCode = invoice.clientBillingDetails.utCode;
        const billingUTCode = invoice.billingDetails.utCode;
        const totalGSTValue = invoice.totalGstValue;

        if (clientBillingUTCode === billingUTCode) {
            invoice.cgst = totalGSTValue / 2;
            invoice.sgst = totalGSTValue / 2;
            invoice.igst = 0;
        } else {
            invoice.igst = totalGSTValue;
            invoice.cgst = 0;
            invoice.sgst = 0;
        }
        return {
            _id: invoice._id,
            createdBy: invoice.createdBy ? invoice.createdBy._id : null,
            createdByName: invoice.createdBy ? `${invoice.createdBy.name || `${invoice.createdBy.firstName || ""} ${invoice.createdBy.lastName || ""}`}` : "",
            paymentBy: invoice.paymentBy ? invoice.paymentBy._id : null,
            paymentByName: (invoice.paymentBy ? `${invoice.paymentBy.name || `${invoice.paymentBy.firstName || ""} ${invoice.paymentBy.lastName || ""}`}` : "").trim(),
            discountedBy: invoice.discountedBy ? invoice.discountedBy._id : null,
            discountedByName: invoice.discountedBy ? `${invoice.discountedBy.name || `${invoice.discountedBy.firstName || ""} ${invoice.discountedBy.lastName || ""}`}` : "",
            invoiceDate: invoice.invoiceDate,
            invoiceNumber: invoice.invoiceNumber,
            userId: invoice.userId,
            orderId: invoice?.orderId || "",
            organizationId: invoice.organizationId,
            facilityId: invoice?.facilityId,
            facilityName: invoice?.billingDetails?.facilityName,
            billingDetails: invoice?.billingDetails,
            clientDetails: invoice?.clientDetails,
            clientBillingDetails: invoice?.clientBillingDetails,
            totalItems: purchaseItemsDetails.length + productItemDetails.length + customPackageItemDetails.length,
            purchaseItems: purchaseItemsDetails,
            returnPurchaseItems: invoice?.returnItems || [],
            returnCustomPackageItems: returnPurchaseCustomItemsDetails || [],
            appliedVoucherItems: appliedVoucherItemsDetails || [],
            customPackageItems: customPackageItemDetails,
            productItem: productItemDetails,
            subTotal: invoice?.subTotal,
            cartDiscount: invoice?.cartDiscount || 0,
            itemDiscount: invoice?.itemDiscount || 0,
            returnDiscount: invoice?.returnDiscount || (invoice?.returnCustomPackageDetails?.returnTotal ?? 0) + (invoice?.returnDetails?.returnTotal ?? 0),
            discount: invoice?.discount || 0,
            cartDiscountType: invoice?.cartDiscountType || "",
            cartDiscountAmount: invoice?.cartDiscountAmount || 0,
            voucherDiscount: invoice?.voucherDiscount || 0,
            totalGstValue: invoice?.totalGstValue || 0,
            totalAmountAfterGst: invoice?.totalAmountAfterGst || 0,
            roundOff: invoice?.roundOff || 0,
            grandTotal: invoice?.grandTotal || 0,
            amountInWords: invoice?.amountInWords ? invoice?.amountInWords : "",
            paymentStatus: invoice.paymentStatus,
            paymentDetails: invoice.paymentDetails,
            invoiceStatus: invoice.invoiceStatus,
            refundStatus: invoice.refundStatus,
            refundAmount: invoice.refundAmount,
            platform: invoice.platform,
            voucherItems: invoice.voucherItems,
            voucherDetails: invoice.voucherDetails,
            cgst: invoice.cgst,
            sgst: invoice.sgst,
            igst: invoice.igst,
            isSplittedPayment: invoice?.isSplittedPayment,
            tenderedAmount: invoice?.tenderedAmount,
            changeDue: invoice?.changeDue,
            updatedPaymentDetails: invoice.updatedPaymentDetails,
            paymentReason: invoice.paymentReason,
            cancelledBy: invoice?.cancelledBy?.name
                ? invoice.cancelledBy?.name
                : invoice.cancelledBy?.firstName
                    ? invoice.cancelledBy?.firstName + " " + invoice.cancelledBy?.lastName
                    : null,
        };
    }

    async getDownloadInvoice(user: IUserDocument, invoiceId: string) {
        const organizationId = await this.getOrganizationId(user);

        const invoice: any = await this.InvoiceModel.findOne({
            _id: invoiceId,
            organizationId: organizationId,
        });

        if (!invoice) {
            throw new NotFoundException("Invoice not found or you do not have access to it");
        }

        // if (invoice.paymentStatus !== PaymentStatus.COMPLETED) {
        //     throw new BadRequestException("Invoice is not paid");
        // }

        if (!invoice.invoiceFilePath) {
            throw new BadRequestException("Invoice file not found");
        }
        return invoice.invoiceFilePath;
    }
    catch(error) {
        throw error.message;
    }

    async changePaymentStatus(organizationId: IDatabaseObjectId, body: UpdatePaymentStatusDto, userId: any) {
        // const organizationId = await this.getOrganizationId(user);
        if (!organizationId) {
            throw new HttpException("Access denied", HttpStatus.FORBIDDEN);
        }
        const invoiceStatus = body.status == PaymentStatus.COMPLETED ? InvoiceStatus.COMPLETED : InvoiceStatus.PENDING;
        let paymentDetailsData = undefined;
        if (body.status == PaymentStatus.COMPLETED) {
            paymentDetailsData = body.paymentDetails?.map((detail) => ({
                paymentMethod: detail.paymentMethod,
                paymentMethodId: detail.paymentMethodId,
                transactionId: detail.transactionId || "",
                amount: detail.amount,
                paymentDate: detail.paymentDate,
                paymentStatus: detail.paymentStatus,
                paymentGateway: detail.paymentGateway || "",
                description: detail.description,
                denominations: detail.paymentMethod === PaymentMethod.CASH ? detail.denominations || {} : {},
            }));
        }
        const invoice = await this.InvoiceModel.findOne({ _id: body.invoiceId, organizationId: organizationId });
        if (!invoice) {
            throw new NotFoundException("Order not found");
        }
        if (invoice.paymentStatus === PaymentStatus.COMPLETED) {
            throw new BadRequestException("Order already paid");
        }
        invoice.paymentStatus = body.status;
        invoice.isSplittedPayment = body.isSplittedPayment;
        invoice.amountPaid = body.amountPaid;
        invoice.invoiceStatus = invoiceStatus;
        invoice.paymentDetails = paymentDetailsData;
        invoice.paymentBy = userId;
        await invoice.save();
        if (invoice.paymentStatus !== PaymentStatus.PENDING) {
            const organization = await this.UserModel.findById(organizationId).select("email");
            const organizationEmail = organization?.email || "";
            const createdBy = await this.UserModel.findById(invoice.createdBy, { name: 1, firstName: 1, lastName: 1 });
            const data: any = invoice.toObject();
            data.createdByName = createdBy?.name || `${createdBy?.firstName} ${createdBy?.lastName}`.trim();
            await this.invoiceService.generateInvoice(data, organizationEmail);
        }
        if (invoice.paymentStatus === PaymentStatus.COMPLETED) {
            await this.PurchaseModel.updateMany(
                { invoiceId: invoice._id },
                {
                    $set: {
                        paymentStatus: body.status,
                    },
                },
            );
        }
        return this.getInvoiceDetails(organizationId, body.invoiceId);
    }

    async cancelOrder(organizationId: IDatabaseObjectId, user: IUserDocument, body: CancelOrder) {
        // const organizationId = await this.getOrganizationId(user);
        if (!organizationId) {
            throw new HttpException("Access denied", HttpStatus.FORBIDDEN);
        }
        const invoiceDetails = await this.InvoiceModel.findOne({ _id: body.invoiceId, organizationId: organizationId });
        const packageDetails = invoiceDetails.purchaseItems;
        let totalSession = 0;
        let packageName = "";
        if (packageDetails.length > 0 && body?.paymentStatus === PaymentStatus.CANCELED) {
            for (let index = 0; index < packageDetails.length; index++) {
                const element: any = packageDetails[index];
                const purchaseDetail = await this.PurchaseModel.findOne({
                    $and: [
                        {
                            $or: [{ packageId: element.packageId }, { bundledPricingId: element.packageId }],
                        },
                        {
                            $or: [{ userId: invoiceDetails.userId }, { sponsorUser: invoiceDetails.userId }],
                        },
                    ],
                    invoiceId: body.invoiceId,
                });
                const schedulingDetails = await this.SchedulingModel.find({ purchaseId: purchaseDetail._id, scheduleStatus: { $ne: ScheduleStatusType.CANCELED } });
                for (let i = 0; i < schedulingDetails.length; i++) {
                    const schedule = schedulingDetails[i];
                    totalSession = totalSession + schedule.sessions;
                }
                packageName = element.packageName;
            }
        }
        if (totalSession > 0) {
            throw new BadRequestException(`Order cannot be canceled as there are ${totalSession} scheduled sessions with the ${packageName} `);
        }

        const invoice = await this.InvoiceModel.findOneAndUpdate(
            { _id: body.invoiceId, organizationId: organizationId },
            {
                $set: {
                    paymentStatus: body?.paymentStatus,
                    paymentReason: body?.reason,
                    cancelledBy: user._id,
                },
            },
        );
        if (!invoice) {
            throw new NotFoundException("Order not found");
        }

        await this.PurchaseModel.updateMany(
            { invoiceId: invoice._id },
            {
                $set: {
                    isActive: false,
                    isExpired: true,
                    paymentStatus: body?.paymentStatus,
                },
            },
        );

        return this.getInvoiceDetails(organizationId, body.invoiceId);
    }

    async getServiceByPurchaseId(purchaseId: string) {
        return null;
    }

    async suspendMembership(user: IUserDocument, body: SuspendMembershipDto) {
        const { purchaseId, fromDate, endDate, notes } = body;

        // Find active purchase
        const purchase: any = await this.PurchaseModel.findOne({
            _id: new Types.ObjectId(purchaseId),
            $expr: {
                $and: [
                    {
                        $lte: [{ $dateToString: { format: "%Y-%m-%d", date: "$startDate" } }, fromDate.toISOString().split("T")[0]],
                    },
                    {
                        $gte: [{ $dateToString: { format: "%Y-%m-%d", date: "$endDate" } }, endDate.toISOString().split("T")[0]],
                    },
                ],
            },
        }).populate("packageId", "services.type");

        if (!purchase) {
            throw new NotFoundException("No active membership found for the given dates");
        }

        // Check for upcoming suspensions
        const now = new Date();
        const upcomingSuspensions = purchase.suspensions?.filter((suspension: Suspensions) => suspension.endDate > now && !suspension.isResumed);

        if (upcomingSuspensions?.length > 0) {
            // Check if any upcoming suspension overlaps with requested dates
            if (upcomingSuspensions.some((suspension: Suspensions) => suspension.fromDate <= endDate && suspension.endDate >= fromDate)) {
                const suspendedDates = upcomingSuspensions.map(
                    (suspension) => `${moment(suspension.fromDate).format("DD/MM/YYYY")} to ${moment(suspension.endDate).format("DD/MM/YYYY")}`,
                );
                throw new BadRequestException(`Membership is already suspended for dates: ${suspendedDates.join(", ")}`);
            }

            // Only allow one upcoming suspension
            throw new BadRequestException("Only one upcoming suspension is allowed at a time");
        }
        // Validate pricing
        // const pricing = await this.PricingModel.findOne({ _id: purchase.packageId });
        // if (!pricing?.membershipId) {
        //     throw new NotFoundException("This package is not part of any membership");
        // }

        // Validate dates
        const timeDifference = endDate.getTime() - fromDate.getTime();
        if (timeDifference < 0) {
            throw new BadRequestException("From date must be less than end date");
        }

        // Check if the requested dates are already scheduled
        if ([ClassType.PERSONAL_APPOINTMENT, ClassType.BOOKINGS].includes(purchase.packageId?.services?.type)) {
            const scheduledSessions = await this.SchedulingModel.findOne({
                clientId: purchase.userId,
                purchaseId: new mongoose.Types.ObjectId(purchaseId),
                scheduleStatus: { $ne: ScheduleStatusType.CANCELED },
                date: { $gte: fromDate, $lte: endDate },
            });
            if (scheduledSessions) {
                throw new BadRequestException(`Selected dates already have a scheduled session on ${moment(scheduledSessions.date).format("DD/MM/YYYY")}`);
            }
        } else {
            const enrollments = await this.EnrollmentModel.find({ purchaseId: new mongoose.Types.ObjectId(purchaseId) });
            if (enrollments.length > 0) {
                const scheduleIds = enrollments.map((s) => s.schedulingId);
                const scheduledSessions = await this.SchedulingModel.find({
                    scheduleStatus: { $ne: ScheduleStatusType.CANCELED },
                    _id: { $in: scheduleIds },
                    date: { $gte: fromDate, $lte: endDate },
                }).select("_id date");
                if (scheduledSessions.length > 0)
                    throw new BadRequestException(
                        `Selected dates already have enrolled sessions between ${moment(fromDate).format("DD/MM/YYYY")} and ${moment(endDate).format("DD/MM/YYYY")}`,
                    );
            }
        }

        // Calculate new expiry date
        const updatedExpiryDate = new Date(purchase.endDate.getTime() + timeDifference);

        // Update purchase with new suspension
        await this.PurchaseModel.updateOne(
            { _id: purchaseId },
            {
                $set: { endDate: updatedExpiryDate },
                $push: {
                    suspensions: {
                        fromDate: fromDate,
                        endDate: endDate,
                        notes: notes,
                    },
                },
            },
        );

        return true;
    }

    async resumeMembership(user: IUserDocument, body: ResumeMembershipDto) {
        const { purchaseId, suspensionId, fromDate } = body;

        // Find purchase with specific suspension
        const purchase = await this.PurchaseModel.findOne({
            _id: purchaseId,
            suspensions: {
                $elemMatch: {
                    _id: suspensionId,
                    // isAtivated: false // Only get non-activated suspensions
                },
            },
        });

        if (!purchase) {
            throw new NotFoundException("No active suspended membership found");
        }

        const suspension = purchase.suspensions.find((s: any) => s._id.toString() === suspensionId);
        if (!suspension) {
            throw new NotFoundException("Suspension period not found");
        }

        // Validate suspension dates
        if (fromDate > suspension.endDate) {
            throw new BadRequestException("Suspension period has already ended");
        }

        if (fromDate < suspension.fromDate) {
            throw new BadRequestException("From date must be greater than suspension start date");
        }

        // Calculate actual suspension duration until current date
        const suspensionDuration = suspension.endDate.getTime() - suspension.fromDate.getTime();
        const actualSuspensionDuration = fromDate.getTime() - suspension.fromDate.getTime();
        const remainingDuration = suspension.endDate.getTime() - fromDate.getTime();
        const totalDuration = actualSuspensionDuration <= 0 ? suspensionDuration : remainingDuration;
        const timeStamp = purchase.endDate.getTime() - totalDuration;
        const updatedExpiryDate = new Date(timeStamp);

        // check if the new expiry date is covering all the scheduled sessions else send error
        const scheduledSessions = await this.SchedulingModel.findOne({
            userId: purchase.userId,
            purchaseId: purchaseId,
            scheduleStatus: { $ne: ScheduleStatusType.CANCELED },
            date: { $gt: updatedExpiryDate, $lte: purchase.endDate },
        });

        if (scheduledSessions) {
            throw new BadRequestException(`New expiry date is not covering all the scheduled sessions of ${moment(scheduledSessions.date).format("DD/MM/YYYY")} date`);
        }

        const updatePayload: UpdateQuery<PurchaseDocument> = {
            $set: {
                endDate: updatedExpiryDate,
            },
        };

        if (suspension.fromDate >= fromDate) {
            updatePayload.$pull = { suspensions: { _id: suspensionId } };
        } else {
            updatePayload.$set["suspensions.$.isResumed"] = true;
            updatePayload.$set["suspensions.$.endDate"] = fromDate;
        }

        // Update purchase
        await this.PurchaseModel.updateOne({ _id: purchaseId, "suspensions._id": suspensionId }, updatePayload);

        return true;
    }

    private async getOrganizationandFacility(user: IUserDocument) {
        const { role } = user;

        switch (role.type) {
            case ENUM_ROLE_TYPE.ORGANIZATION:
                return { organizationId: user._id };

            case ENUM_ROLE_TYPE.FRONT_DESK_ADMIN:
            case ENUM_ROLE_TYPE.WEB_MASTER:
            case ENUM_ROLE_TYPE.TRAINER:
                const staffDetails = await this.StaffDetailsModel.findOne({ userId: user._id }, { organizationId: 1, facilityId: 1 });

                if (!staffDetails) {
                    throw new Error("Staff details not found for the user.");
                }

                if (!staffDetails.facilityId) {
                    throw new Error("Facility not assigned to this staff user.");
                }

                return {
                    organizationId: staffDetails.organizationId,
                    facilityId: staffDetails.facilityId,
                };

            default:
                throw new Error("Unauthorized role or insufficient permissions.");
        }
    }

    async zOutReportByEmp(user: any, body: ExportZOutReportDto, userTimezone: string) {
        const facilityDetails = await this.getOrganizationandFacility(user);
        const { facilityIds, responseType, zOutId } = body;
        let data = {};
        const objectId = new Types.ObjectId(zOutId);
        const facilityName = await this.FacilityModel.findOne({ _id: { $in: facilityIds.map((_id) => new Types.ObjectId(_id)) } }, { facilityName: 1, paymentMethods: 1 });
        const activePaymentMethods = facilityName?.paymentMethods?.filter((method: any) => method.isActive === true) || [];
        const latestReconciliation = await this.ReconciliationModel.findOne({
            _id: objectId,
            facilityId: { $in: facilityIds.map((_id) => new Types.ObjectId(_id)) },
            organizationId: new Types.ObjectId(facilityDetails.organizationId),
        });

        if (!latestReconciliation) {
            throw new Error("Reconciliation document not found.");
        }

        const secondLastReconciliation = await this.ReconciliationModel.findOne({
            facilityId: latestReconciliation.facilityId,
            organizationId: new Types.ObjectId(facilityDetails.organizationId),
            createdAt: { $lt: latestReconciliation.createdAt },
        }).sort({ createdAt: -1 });

        let gteDate: Date;

        if (secondLastReconciliation) {
            gteDate = secondLastReconciliation.createdAt;
        } else {
            const firstInvoice = await this.InvoiceModel.findOne(
                {
                    facilityId: latestReconciliation.facilityId,
                    organizationId: new Types.ObjectId(facilityDetails.organizationId),
                },
                { createdAt: 1 },
            )
                .sort({ createdAt: 1 })
                .lean();

            gteDate = firstInvoice?.["createdAt"] || new Date(0);
        }
        const lteDate = latestReconciliation.createdAt;
        const currentDate = new Date();
        const dayOfWeek = currentDate.toLocaleString("en-US", { weekday: "short" }).toLowerCase();
        const startISTTime = moment(gteDate).tz(userTimezone).format("HH:mm");
        const currentISTTime = moment(currentDate).tz(userTimezone).format("HH:mm");
        const endISTTime = moment(lteDate).tz(userTimezone).format("HH:mm");

        (data["day"] = dayOfWeek), (data["startDate"] = `${moment(gteDate).tz(userTimezone).format("DD/MM/YYYY")}`);
        data["endDate"] = `${moment(lteDate).tz(userTimezone).format("DD/MM/YYYY")}`;
        data["currentDate"] = `${moment(currentDate).tz(userTimezone).format("DD/MM/YYYY")}`;
        (data["role"] = user.role ? user.role.type : ""), (data["name"] = user?.name);
        data["firstName"] = user?.firstName;
        data["lastName"] = user?.lastName;
        data["startTime"] = startISTTime;
        data["currentTime"] = currentISTTime;
        data["endTime"] = endISTTime;
        data["facilityName"] = facilityName?.facilityName;

        const filter: any = {
            organizationId: new Types.ObjectId(facilityDetails.organizationId),
            paymentStatus: PaymentStatus.COMPLETED,
            updatedAt: {
                $gte: gteDate,
                $lte: lteDate,
            },
        };
        if (Array.isArray(facilityIds) && facilityIds.length > 0) {
            filter["facilityId"] = { $in: facilityIds.map((_id) => new Types.ObjectId(_id)) };
        }
        let pipeline: any = [
            { $match: filter },
            {
                $addFields: {
                    purchaseItemsSafe: { $ifNull: ["$purchaseItems", []] },
                    productItemSafe: { $ifNull: ["$productItem", []] },
                    customPackageItemsSafe: { $ifNull: ["$customPackageItems", []] },
                    returnItemsSafe: { $ifNull: ["$returnItems", []] },
                },
            },
            {
                $addFields: {
                    serviceItems: {
                        $map: {
                            input: "$purchaseItemsSafe",
                            as: "item",
                            in: {
                                $mergeObjects: [
                                    "$$item",
                                    {
                                        itemType: "service",
                                        isInclusiveofGst: { $ifNull: ["$isInclusiveofGst", false] },
                                        orderId: "$orderId",
                                        voucherDiscount: { $ifNull: ["$voucherDiscount", 0] },
                                    },
                                ],
                            },
                        },
                    },
                    productItems: {
                        $map: {
                            input: "$productItemSafe",
                            as: "item",
                            in: {
                                $mergeObjects: [
                                    "$$item",
                                    {
                                        itemType: "product",
                                        isInclusiveofGst: { $ifNull: ["$isInclusiveofGst", false] },
                                        orderId: "$orderId",
                                        voucherDiscount: { $ifNull: ["$voucherDiscount", 0] },
                                    },
                                ],
                            },
                        },
                    },
                    customPackageItems: {
                        $map: {
                            input: "$customPackageItemsSafe",
                            as: "item",
                            in: {
                                $mergeObjects: [
                                    "$$item",
                                    {
                                        itemType: "service",
                                        isInclusiveofGst: { $ifNull: ["$isInclusiveofGst", false] },
                                        orderId: "$orderId",
                                        voucherDiscount: { $ifNull: ["$voucherDiscount", 0] },
                                    },
                                ],
                            },
                        },
                    },
                    returnPackageItems: {
                        $map: {
                            input: "$returnItemsSafe",
                            as: "item",
                            in: {
                                $mergeObjects: [
                                    "$$item",
                                    {
                                        itemType: "return",
                                        isInclusiveofGst: { $ifNull: ["$isInclusiveofGst", false] },
                                        orderId: "$orderId",
                                        voucherDiscount: { $ifNull: ["$voucherDiscount", 0] },
                                    },
                                ],
                            },
                        },
                    },
                    completedPayments: {
                        $let: {
                            vars: {
                                paymentsArray: {
                                    $cond: [{ $isArray: "$paymentDetails" }, "$paymentDetails", { $ifNull: [["$paymentDetails"], []] }],
                                },
                            },
                            in: {
                                $filter: {
                                    input: "$$paymentsArray",
                                    as: "payment",
                                    cond: { $eq: ["$$payment.paymentStatus", PaymentStatus.COMPLETED] },
                                },
                            },
                        },
                    },
                },
            },
            {
                $facet: {
                    payments: [
                        { $unwind: "$completedPayments" },
                        {
                            $group: {
                                _id: {
                                    facilityId: "$facilityId",
                                    paymentMethod: "$completedPayments.paymentMethod",
                                },
                                totalPaid: { $sum: "$completedPayments.amount" },
                                totalSales: { $sum: "$grandTotal" },
                            },
                        },
                        {
                            $group: {
                                _id: "$_id.facilityId",
                                totalSales: { $sum: "$totalSales" },
                                paymentMethodSummary: {
                                    $push: {
                                        paymentMethod: "$_id.paymentMethod",
                                        totalAmount: "$totalPaid",
                                    },
                                },
                            },
                        },
                    ],
                    invoices: [
                        {
                            $project: {
                                facilityId: 1,
                                allItems: { $concatArrays: ["$serviceItems", "$productItems", "$customPackageItems", "$returnPackageItems"] },
                            },
                        },
                        {
                            $group: {
                                _id: "$facilityId",
                                invoices: { $push: "$allItems" },
                            },
                        },
                        {
                            $addFields: {
                                flattenedItems: {
                                    $reduce: {
                                        input: "$invoices",
                                        initialValue: [],
                                        in: { $concatArrays: ["$$value", "$$this"] },
                                    },
                                },
                            },
                        },
                    ],
                },
            },
            {
                $project: {
                    data: {
                        $map: {
                            input: "$payments",
                            as: "payment",
                            in: {
                                _id: "$$payment._id",
                                totalSales: "$$payment.totalSales",
                                paymentMethodSummary: "$$payment.paymentMethodSummary",
                                invoices: {
                                    $let: {
                                        vars: {
                                            inv: {
                                                $arrayElemAt: [
                                                    {
                                                        $filter: {
                                                            input: "$invoices",
                                                            as: "i",
                                                            cond: { $eq: ["$$i._id", "$$payment._id"] },
                                                        },
                                                    },
                                                    0,
                                                ],
                                            },
                                        },
                                        in: "$$inv.flattenedItems",
                                    },
                                },
                            },
                        },
                    },
                },
            },
            { $unwind: "$data" },
            {
                $replaceRoot: {
                    newRoot: "$data",
                },
            },
            {
                $lookup: {
                    from: "facilities",
                    localField: "_id",
                    foreignField: "_id",
                    as: "facility",
                },
            },
            {
                $project: {
                    _id: 1,
                    facilityName: { $arrayElemAt: ["$facility.facilityName", 0] },
                    totalSales: 1,
                    invoiceCount: { $size: "$invoices" },
                    invoices: 1,
                    paymentMethodSummary: 1,
                },
            },
        ];
        const salesData = await this.InvoiceModel.aggregate(pipeline).exec();
        if (responseType === "stream") {
            const workbook = salesData.length
                ? await this.generateExcelReport(salesData, latestReconciliation, data, activePaymentMethods)
                : await this.generateEmptyExcel(data, latestReconciliation, activePaymentMethods);

            const excelBuffer = write(workbook, { type: "buffer", bookType: "xlsx" });

            return new StreamableFile(excelBuffer, {
                type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                disposition: `attachment; filename=Z-Out_Report_${moment().format("YYYY-MM-DD")}.xlsx`,
            });
        }
        if (responseType === "pdf") {
            const pdfBuffer = salesData.length
                ? await this.generateZOutReportPDF(data, salesData[0], latestReconciliation, activePaymentMethods)
                : await this.generateZOutReportPDF(data, [], latestReconciliation, activePaymentMethods); // empty case also handled

            return new StreamableFile(new Uint8Array(pdfBuffer), {
                type: "application/pdf",
                disposition: `attachment; filename=Z-Out_Report_${moment().format("YYYY-MM-DD")}.pdf`,
            });
        }
        return this.formatReportData(salesData);
    }

    private async generateExcelReport(facilities, latestReconciliation, data, activePaymentMethods) {
        const workbook = utils.book_new();

        const paymentMethodDocs = await this.paymentMethodModel.find({}).sort({ name: 1 });
        const shortIdToName = new Map(paymentMethodDocs.map((pm: any) => [pm.shortId, pm.name]));

        facilities?.forEach((facility) => {
            const paymentSummary = facility?.paymentMethodSummary || [];

            let cashSales = 0;
            let otherPaymentsTotalAmount = 0;
            let otherPaymentsTotalCollected = 0;
            let otherPaymentsTotalOverUnder = 0;

            const otherPayments = [["--- Other Payments ---"], ["Label", "Amount (INR)", "Collected", "Over/Under"]];

            const paymentRows = paymentSummary.map((payment) => {
                const readableName = shortIdToName.get(payment?.paymentMethod) || payment?.paymentMethod;
                const method = payment?.paymentMethod;
                const reconciliation = latestReconciliation?.otherPayments.find((e) => e?.method === method) || {};

                if (payment?.paymentMethod === PaymentMethod.CASH) {
                    cashSales += payment?.totalAmount || 0;
                } else {
                    const amount = Number(payment?.totalAmount || 0);
                    const collected = Number(reconciliation?.collected || 0);
                    const overUnder = Number(reconciliation?.overUnder || 0);
                    otherPaymentsTotalAmount += amount;
                    otherPaymentsTotalCollected += collected;
                    otherPaymentsTotalOverUnder += overUnder;

                    otherPayments.push([readableName, `₹ ${amount.toFixed(2)}`, `₹ ${collected.toFixed(2)}`, `₹ ${overUnder.toFixed(2)}`]);
                }

                return ["POS", readableName, `₹ ${(payment?.totalAmount || 0).toFixed(2)}`];
            });
            const existingLabels = new Set(otherPayments.slice(2).map((row) => row[0]));
            const ignoredShortIds = ["cash", "splitPayment"];
            activePaymentMethods?.forEach((method) => {
                const methodName = method.name;
                const methodShortId = method.shortId;

                if (!ignoredShortIds.includes(methodShortId) && !existingLabels.has(methodName)) {
                    otherPayments.push([methodName, "₹ 0.00", "₹ 0.00", "₹ 0.00"]);
                }
            });
            otherPayments.push(
                ["", "- - - - - - - - -", "- - - - - - - - -", "- - - - - - - - -"],
                [
                    "Total",
                    `₹ ${(otherPaymentsTotalAmount || 0).toFixed(2)}`,
                    `₹ ${(otherPaymentsTotalCollected || 0).toFixed(2)}`,
                    `₹ ${(latestReconciliation?.onlineOverUnder || 0).toFixed(2)}`,
                ],
                [],
                [],
            );

            let totalAmount = 0;
            const invoiceRows = (facility?.invoices || []).map((invoice) => {
                let isInclusiveGst = invoice.isInclusiveofGst;
                const itemName = invoice?.itemType === "service" ? invoice?.packageName : invoice?.productName;
                const quantity = Number(invoice?.quantity || 0);
                let price = isInclusiveGst ? Number(invoice?.price || 0) : Number(invoice?.unitPrice || 0);
                const unitPrice = invoice?.itemType === "service" ? price : Number(invoice?.salePrice || 0);
                const subTotal = unitPrice * quantity;
                const discountExcludeCart = Number(invoice?.discountExcludeCart || 0);
                const discountIncludeCart = Number(invoice?.discountIncludeCart || 0);
                const voucherDiscount = Number(invoice?.voucherDiscountAmount || 0);
                const returnDiscount = Number(invoice?.returnDiscountAmount || 0);
                const totalDiscount = discountExcludeCart + discountIncludeCart + returnDiscount + voucherDiscount;
                const gstAmount = Number(invoice?.gstAmount || 0);
                const amountBeforeGst = subTotal - totalDiscount - (isInclusiveGst ? gstAmount : 0);
                const itemTotal = isInclusiveGst ? subTotal - totalDiscount : subTotal + gstAmount - totalDiscount;
                totalAmount += itemTotal;

                return [
                    itemName,
                    quantity,
                    `₹ ${unitPrice.toFixed(2)}`,
                    `₹ ${totalDiscount.toFixed(2)}`,
                    `₹ ${amountBeforeGst.toFixed(2)}`,
                    `₹ ${gstAmount.toFixed(2)}`,
                    `₹ ${itemTotal.toFixed(2)}`,
                ];
            });

            const totalPayments = paymentSummary.reduce((sum, p) => sum + (p?.totalAmount || 0), 0);

            const totals = [
                ["--- Totals ---"],
                ["Label", "Amount (INR)"],
                ["All Over/Under (Cash+Other)", `₹ ${((latestReconciliation?.overUnder || 0) + (latestReconciliation?.onlineOverUnder || 0)).toFixed(2)}`],
                ["All Payments", `₹ ${(totalPayments || 0).toFixed(2)}`],
                [],
                [],
            ];
            let totalCash = latestReconciliation?.startingAmount + cashSales - latestReconciliation?.pettyAmount;
            const cashSection = [
                ["--- Cash Payments ---"],
                ["Label", "Amount (INR)"],
                ["Starting Cash", `₹ ${(latestReconciliation?.startingAmount || 0).toFixed(2)}`],
                ["Cash Sales", `₹ ${cashSales.toFixed(2)}`],
                ["Petty Cash Out(-)", `₹ ${(latestReconciliation?.pettyAmount || 0).toFixed(2)}`],
                ["", "- - - - - - - - -"],
                ["Total", `₹ ${(totalCash || 0).toFixed(2)}`],
                [],
                ["Drawer Cash", `₹ ${(latestReconciliation?.drawerAmount || 0).toFixed(2)}`],
                ["Leave Cash", `₹ ${(latestReconciliation?.leaveAmount || 0).toFixed(2)}`],
                ["Cash Over/Under", `₹ ${(latestReconciliation?.overUnder || 0).toFixed(2)}`],
                ["", `- - - - - - - - -`],
                ["Cash Deposit Amount", `₹ ${(latestReconciliation?.depositAmount || 0).toFixed(2)}`],
                [],
                [],
            ];

            const combinedData = [
                ["Z-Out Summary Report -", data?.facilityName || "Unnamed Facility"],
                ["From", `${data?.startDate} @ ${data?.startTime}`],
                ["To", `${data?.currentDate} @ ${data?.currentTime}`],
                [],
                [],
                ...cashSection,
                ...otherPayments,
                ...totals,
                ["--- All Payments ---"],
                ["Source", "Method", "Sales"],
                ...paymentRows,
                ["", "", "- - - - - - - - -"],
                ["Total", "", `₹ ${totalPayments.toFixed(2)}`],
                [],
                [],
                ["--- Accounts ---"],
                ["Item", "Qty", "Unit Price", "Discount", "Amount Before GST", "GST", "Total"],
                ...invoiceRows,
                ["", "", "", "", "", "", "- - - - - - - - -"],
                ["Grand Total", "", "", "", "", "", `₹ ${totalAmount.toFixed(2)}`],
                [],
                [],
                [
                    `Report Prepared By ${data.name ? data.name : `${data.firstName || ""} ${data.lastName || ""}`.trim()} on ${data?.day?.toUpperCase()}, ${data?.currentDate} ,${data?.currentTime
                    }`,
                ],
            ];

            const sheet = utils.aoa_to_sheet(combinedData);
            utils.book_append_sheet(workbook, sheet, data?.facilityName?.substring(0, 30) || "Report");
        });

        return workbook;
    }

    private async generateEmptyExcel(data, latestReconciliation, activePaymentMethods) {
        const workbook = utils.book_new();

        const paymentMethodDocs = await this.paymentMethodModel.find({}).sort({ name: 1 });
        const shortIdToName = new Map(paymentMethodDocs.map((pm: any) => [pm.shortId, pm.name]));

        let cashSales = 0;
        let otherPaymentsTotalAmount = 0;
        let otherPaymentsTotalCollected = 0;
        let otherPaymentsTotalOverUnder = 0;

        const otherPayments = [["--- Other Payments ---"], ["Label", "Amount (INR)", "Collected", "Over/Under"]];

        (latestReconciliation?.otherPayments || [])?.forEach((payment) => {
            const label = shortIdToName?.get(payment?.method) || payment?.method;
            const amount = Number(payment?.amount || 0);
            const collected = Number(payment?.collected || 0);
            const overUnder = Number(payment?.overUnder || 0);

            otherPaymentsTotalAmount += amount;
            otherPaymentsTotalCollected += collected;
            otherPaymentsTotalOverUnder += overUnder;

            otherPayments.push([label, `₹ ${amount.toFixed(2)}`, `₹ ${collected.toFixed(2)}`, `₹ ${overUnder.toFixed(2)}`]);
        });
        const existingLabels = new Set(otherPayments.slice(2).map((row) => row[0]));
        const ignoredShortIds = ["cash", "splitPayment"];
        activePaymentMethods?.forEach((method) => {
            const methodName = method.name;
            const methodShortId = method.shortId;

            if (!ignoredShortIds.includes(methodShortId) && !existingLabels.has(methodName)) {
                otherPayments.push([methodName, "₹ 0.00", "₹ 0.00", "₹ 0.00"]);
            }
        });
        otherPayments.push(
            ["", "- - - - - - - - -", "- - - - - - - - -", "- - - - - - - - -"],
            ["Total", `₹ ${otherPaymentsTotalAmount?.toFixed(2)}`, `₹ ${otherPaymentsTotalCollected?.toFixed(2)}`, `₹ ${otherPaymentsTotalOverUnder?.toFixed(2)}`],
            [],
            [],
        );

        let totalCash = latestReconciliation?.startingAmount + cashSales - latestReconciliation?.pettyAmount;

        const cashSection = [
            ["--- Cash Payments ---"],
            ["Label", "Amount (INR)"],
            ["Starting Cash", `₹ ${(latestReconciliation?.startingAmount || 0).toFixed(2)}`],
            ["Cash Sales", `₹ ${cashSales.toFixed(2)}`],
            ["Petty Cash Out(-)", `₹ ${(latestReconciliation?.pettyAmount || 0).toFixed(2)}`],
            ["", "- - - - - - - - -"],
            ["Total", `₹ ${(totalCash || 0).toFixed(2)}`],
            [],
            ["Drawer Cash", `₹ ${(latestReconciliation?.drawerAmount || 0).toFixed(2)}`],
            ["Leave Cash", `₹ ${(latestReconciliation?.leaveAmount || 0).toFixed(2)}`],
            ["Cash Over/Under", `₹ ${(latestReconciliation?.overUnder || 0).toFixed(2)}`],
            ["", `- - - - - - - - -`],
            ["Cash Deposit Amount", `₹ ${(latestReconciliation?.depositAmount || 0).toFixed(2)}`],
            [],
            [],
        ];

        const totals = [
            ["--- Totals ---"],
            ["Label", "Amount (INR)"],
            ["All Over/Under (Cash+Other)", `₹ ${((latestReconciliation?.overUnder || 0) + (latestReconciliation?.onlineOverUnder || 0)).toFixed(2)}`],
            ["All Payments", `₹ ${(latestReconciliation?.totalAmount || 0).toFixed(2)}`],
            [],
            [],
        ];

        const combinedData = [
            ["Z-Out Summary Report -", data?.facilityName || "Unnamed Facility"],
            // [`Report Generated By - ${data?.name || `${data.firstName || ""} ${data.lastName || ""}`.trim()} (${data.role}) on ${data?.day?.toUpperCase()}, ${data?.currentDate}`],
            ["From", `${data?.startDate} @ ${data?.startTime}`],
            ["To", `${data?.currentDate} @ ${data?.currentTime}`],
            [],
            [],
            ...cashSection,
            ...otherPayments,
            ...totals,
            [
                `Report Prepared By ${data.name || `${data.firstName || ""} ${data.lastName || ""}`.trim()} on ${data?.day?.toUpperCase()}, ${data?.currentDate}, ${data?.currentTime
                }`,
            ],
        ];

        const sheet = utils.aoa_to_sheet(combinedData);
        utils.book_append_sheet(workbook, sheet, data?.facilityName?.substring(0, 30) || "Report");

        return workbook;
    }

    private formatReportData(facilities) {
        return {
            message: "Z-Out Report fetched successfully",
            data: facilities.map((facility) => ({
                facilityId: facility._id,
                facilityName: facility.facilityName,
                totalSales: facility.totalSales,
                paymentMethods: facility.paymentMethodSummary,
                items: facility.invoices,
            })),
        };
    }

    async scheduleAtGlance(user: IUserDocument, body: ExportScheduleReportDto, userTimezone: string) {
        const facilityDetails = await this.getOrganizationandFacility(user);
        const { facilityIds, startDate, endDate, staffIds, classType, scheduleStatus, serviceCategoryIds } = body;

        const filter: any = {
            organizationId: new Types.ObjectId(facilityDetails.organizationId),
            // serviceCategoryId: { $in: serviceCategoryIds.map(id => new Types.ObjectId(id)) },
        };

        if (serviceCategoryIds && serviceCategoryIds.length) {
            filter["serviceCategoryId"] = { $in: serviceCategoryIds.map((id) => new Types.ObjectId(id)) };
        }

        if (Array.isArray(facilityIds) && facilityIds.length > 0) {
            filter["facilityId"] = { $in: facilityIds.map((_id) => new Types.ObjectId(_id)) };
        } else if (user.role.type !== ENUM_ROLE_TYPE.ORGANIZATION && facilityDetails.facilityId) {
            const facilityArray = Array.isArray(facilityDetails.facilityId) ? facilityDetails.facilityId : [facilityDetails.facilityId];

            filter["facilityId"] = {
                $in: facilityArray.map((id) => new Types.ObjectId(id)),
            };
        }
        if (scheduleStatus && scheduleStatus.length > 0) {
            filter["scheduleStatus"] = { $in: scheduleStatus };
        }
        if (startDate && endDate) {
            const startOfDay = new Date(new Date(startDate).setHours(0, 0, 0, 0));
            const endOfDay = new Date(new Date(endDate).setHours(23, 59, 59, 999));
            filter["date"] = {
                $gte: startOfDay,
                $lte: endOfDay,
            };
        }

        const hasBooking = classType.includes(ClassType.BOOKINGS);
        const hasNonBooking = classType.filter((type) => type !== ClassType.BOOKINGS);
        const orConditions: any[] = [];

        if (hasBooking && hasNonBooking.length > 0) {
            // If staffIds exist, filter by them
            if (staffIds && staffIds.length > 0) {
                orConditions.push({
                    classType: { $in: hasNonBooking },
                    trainerId: { $in: staffIds.map((id) => new Types.ObjectId(id)) },
                });
            } else {
                orConditions.push({
                    classType: { $in: hasNonBooking },
                });
            }

            orConditions.push({
                classType: ClassType.BOOKINGS,
            });
        } else if (hasBooking && hasNonBooking.length === 0) {
            orConditions.push({
                classType: ClassType.BOOKINGS,
            });
        } else if (!hasBooking && hasNonBooking.length > 0) {
            if (staffIds && staffIds.length > 0) {
                orConditions.push({
                    classType: { $in: hasNonBooking },
                    trainerId: { $in: staffIds.map((id) => new Types.ObjectId(id)) },
                });
            } else {
                orConditions.push({
                    classType: { $in: hasNonBooking },
                });
            }
        }

        if (orConditions.length > 0) {
            filter["$or"] = orConditions;
        }

        let schedulingDetails: any = await this.SchedulingModel.aggregate([
            { $match: filter },
            {
                $lookup: {
                    from: "schedulings",
                    let: { clientId: "$clientId", currentId: "$_id" },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [{ $eq: ["$clientId", "$$clientId"] }, { $lt: ["$_id", "$$currentId"] }],
                                },
                            },
                        },
                        { $limit: 1 },
                    ],
                    as: "visitHistory",
                },
            },
            {
                $addFields: {
                    firstVisit: { $eq: [{ $size: "$visitHistory" }, 0] },
                },
            },
            {
                $lookup: {
                    from: "services",
                    localField: "serviceCategoryId",
                    foreignField: "_id",
                    as: "services",
                },
            },
            {
                $addFields: {
                    serviceCategoryName: { $arrayElemAt: ["$services.name", 0] },
                    subtypeName: {
                        $first: {
                            $map: {
                                input: {
                                    $filter: {
                                        input: { $arrayElemAt: ["$services.appointmentType", 0] },
                                        as: "item",
                                        cond: { $eq: ["$$item._id", "$subTypeId"] },
                                    },
                                },
                                as: "match",
                                in: "$$match.name",
                            },
                        },
                    },
                },
            },
            {
                $lookup: {
                    from: "clients",
                    localField: "clientId",
                    foreignField: "userId",
                    as: "clientInfo",
                },
            },
            {
                $addFields: {
                    clientDob: { $arrayElemAt: ["$clientInfo.dob", 0] },
                    clientIdentity: { $arrayElemAt: ["$clientInfo.clientId", 0] },
                },
            },
            {
                $lookup: {
                    from: "facilities",
                    localField: "facilityId",
                    foreignField: "_id",
                    as: "facilityInfo",
                },
            },
            {
                $addFields: {
                    facilityName: { $arrayElemAt: ["$facilityInfo.facilityName", 0] },
                    cityId: { $arrayElemAt: ["$facilityInfo.address.city", 0] },
                },
            },
            {
                $lookup: {
                    from: "cities",
                    localField: "cityId",
                    foreignField: "_id",
                    as: "cityInfo",
                },
            },
            {
                $addFields: {
                    facilityLocation: { $arrayElemAt: ["$cityInfo.name", 0] },
                },
            },
            {
                $lookup: {
                    from: "users",
                    let: { clientId: "$clientId", trainerId: "$trainerId" },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $or: [{ $eq: ["$_id", "$$clientId"] }, { $eq: ["$_id", "$$trainerId"] }],
                                },
                            },
                        },
                        {
                            $project: {
                                _id: 1,
                                firstName: 1,
                                lastName: 1,
                            },
                        },
                    ],
                    as: "userNames",
                },
            },
            {
                $addFields: {
                    clientName: {
                        $let: {
                            vars: {
                                user: {
                                    $first: {
                                        $filter: {
                                            input: "$userNames",
                                            as: "u",
                                            cond: { $eq: ["$$u._id", "$clientId"] },
                                        },
                                    },
                                },
                            },
                            in: { $concat: ["$$user.firstName", " ", "$$user.lastName"] },
                        },
                    },
                    trainerName: {
                        $let: {
                            vars: {
                                user: {
                                    $first: {
                                        $filter: {
                                            input: "$userNames",
                                            as: "u",
                                            cond: { $eq: ["$$u._id", "$trainerId"] },
                                        },
                                    },
                                },
                            },
                            in: { $concat: ["$$user.firstName", " ", "$$user.lastName"] },
                        },
                    },
                },
            },
            {
                $project: {
                    date: 1,
                    from: 1,
                    to: 1,
                    serviceCategoryName: 1,
                    subtypeName: 1,
                    trainerName: 1,
                    facilityName: 1,
                    facilityLocation: 1,
                    notes: 1,
                    clientName: 1,
                    clientIdentity: 1,
                    scheduleStatus: 1,
                    clientDob: 1,
                    classType: 1,
                    firstVisit: 1,
                },
            },
        ]).exec();

        let data =
            `Date,Start Time,End Time,Service Type,Description,Staff Name,Facility Name,Facility Location,Notes,Client Name,ClientId,Status,First Visit,Birthday\n` +
            schedulingDetails
                ?.map((item: any) => {
                    const date = item?.date ? `${moment(item?.date).tz(userTimezone).format("DD/MM/YYYY")}` : item?.date;
                    const startTime = item?.from;
                    const endTime = item?.to;
                    const classType = item?.classType;
                    const description = `${item?.serviceCategoryName + "/" + item?.subtypeName}`;
                    const staffName = item?.trainerName;
                    const facilityName = item?.facilityName;
                    const facilityLocation = item?.facilityLocation;
                    const clientName = item?.clientName;
                    const clientId = item?.clientIdentity;
                    const status = item?.scheduleStatus;
                    const firstVisit = item?.firstVisit;
                    const birthday = item?.clientDob ? `${moment(item?.clientDob).tz(userTimezone).format("DD/MM/YYYY")}` : item?.clientDob;
                    const notes = item?.notes;

                    const row = [
                        date,
                        startTime,
                        endTime,
                        classType,
                        description,
                        staffName,
                        facilityName,
                        facilityLocation,
                        notes,
                        clientName,
                        clientId,
                        status,
                        firstVisit,
                        birthday,
                    ];
                    return row.join(",");
                })
                .join("\n");
        data += `\nGenerated By,${user.firstName?.toUpperCase() || ""} ${user.lastName?.toUpperCase() || ""}` + `\nDate,${moment().tz(userTimezone).format("DD/MM/YYYY HH:mm:ss")}`;
        return {
            data,
        };
    }

    async generateZOutReportPDF(data: any, facility: any, latestReconciliation: any, activePaymentMethods: any) {
        const templatePath = path.join(process.cwd(), "templates", "zout-report.hbs");
        const templateHtml = fs.readFileSync(templatePath, "utf-8");

        const methodLookup = new Map();
        activePaymentMethods.forEach((doc) => {
            if (doc.name) methodLookup.set(doc.name.toLowerCase(), doc.name);
            if (doc.shortId) methodLookup.set(doc.shortId.toLowerCase(), doc.name);
        });

        const compiledTemplate = Handlebars.compile(templateHtml);

        const cashSales =
            facility?.paymentMethodSummary
                ?.filter((pm) => {
                    const raw = (pm?.paymentMethod || "").toLowerCase();
                    return methodLookup.get(raw) === "Cash";
                })
                .reduce((sum, pm) => sum + (pm?.totalAmount || 0), 0) || 0;

        const { startingAmount = 0, pettyAmount = 0, drawerAmount = 0, leaveAmount = 0, overUnder = 0, depositAmount = 0, onlineOverUnder = 0 } = latestReconciliation || {};

        const cashSectionData = {
            facilityName: facility?.facilityName,
            startingAmount: startingAmount.toFixed(2),
            cashSales: cashSales.toFixed(2),
            pettyAmount: pettyAmount.toFixed(2),
            totalCash: (startingAmount + cashSales - pettyAmount).toFixed(2),
            drawerAmount: drawerAmount.toFixed(2),
            leaveAmount: leaveAmount.toFixed(2),
            overUnder: overUnder.toFixed(2),
            depositAmount: depositAmount.toFixed(2),
            overUnderPositive: overUnder >= 0,
        };

        const mergedPayments: Record<string, { amount: number }> = {};

        (facility?.paymentMethodSummary || []).forEach((pm) => {
            const rawMethod = (pm?.paymentMethod || "").toLowerCase();
            const normalizedName = methodLookup.get(rawMethod) || pm?.paymentMethod;

            if (!normalizedName || normalizedName.toLowerCase() === "cash") return;

            const amount = Number(pm?.totalAmount || 0);

            if (!mergedPayments[normalizedName]) {
                mergedPayments[normalizedName] = { amount: 0 };
            }
            mergedPayments[normalizedName].amount += amount;
        });

        let otherPaymentsTotalAmount = 0;
        let otherPaymentsTotalCollected = 0;
        let otherPaymentsTotalOverUnder = 0;

        const otherPaymentsData = Object.entries(mergedPayments).map(([methodName, data]) => {
            const reconciliation =
                latestReconciliation?.otherPayments.find((e) => {
                    const recMethod = (e?.method || "").toLowerCase();
                    const recNormalized = methodLookup.get(recMethod) || e?.method;
                    return recNormalized === methodName;
                }) || {};

            const amount = Number(data.amount || 0);
            const collected = Number(reconciliation?.collected || 0);
            const recOverUnder = Number(reconciliation?.overUnder || 0);

            otherPaymentsTotalAmount += amount;
            otherPaymentsTotalCollected += collected;
            otherPaymentsTotalOverUnder += recOverUnder;
            return {
                label: methodName,
                amount: amount.toFixed(2),
                collected: collected.toFixed(2),
                overUnder: recOverUnder.toFixed(2),
                overUnderPositive: recOverUnder >= 0,
            };
        });

        const existingLabels = otherPaymentsData.map((item) => item.label);

        activePaymentMethods.forEach((pm) => {
            const rawMethod = (pm?.paymentMethod || pm?.name || "").toLowerCase();
            const normalizedName = methodLookup.get(rawMethod) || pm?.paymentMethod || pm?.name;

            if (!normalizedName || normalizedName.toLowerCase() === "cash" || normalizedName.toLowerCase() === "split payment" || existingLabels.includes(normalizedName)) {
                return;
            }

            otherPaymentsData.push({
                label: normalizedName,
                amount: "0.00",
                collected: "0.00",
                overUnder: "0.00",
                overUnderPositive: true,
            });
        });

        const otherPaymentsTotals = {
            amount: otherPaymentsTotalAmount.toFixed(2),
            collected: otherPaymentsTotalCollected.toFixed(2),
            overUnder: otherPaymentsTotalOverUnder.toFixed(2),
            overUnderPositive: otherPaymentsTotalOverUnder >= 0,
        };

        const totalPayments = (facility?.paymentMethodSummary || [])?.reduce((sum, p) => sum + (p?.totalAmount || 0), 0) || 0;

        const totalsData = {
            allOverUnder: (overUnder + onlineOverUnder).toFixed(2),
            allPayments: totalPayments.toFixed(2),
            allOverUnderPositive: overUnder + onlineOverUnder >= 0,
        };

        let totalAmount = 0;
        let totalSalesAmount = 0;
        const invoiceRows: any[] = [];
        const voucherAddedOrders = new Set<string>();

        const mergedPaymentRows: Record<string, { sales: number; returns: number }> = {};

        (facility?.paymentMethodSummary || []).forEach((payment) => {
            const rawMethod = (payment?.paymentMethod || "").toLowerCase();
            const normalizedName = methodLookup.get(rawMethod) || payment?.paymentMethod;

            if (!normalizedName) return;

            const paymentTotal = Number(payment?.totalAmount || 0);
            totalSalesAmount += paymentTotal;

            if (!mergedPaymentRows[normalizedName]) {
                mergedPaymentRows[normalizedName] = { sales: 0, returns: 0 };
            }

            mergedPaymentRows[normalizedName].sales += paymentTotal;
        });

        // 🔑 Convert to final array for template
        const paymentRows = Object.entries(mergedPaymentRows).map(([method, data]) => ({
            source: "POS",
            method,
            returns: data.returns.toFixed(2),
            sales: data.sales.toFixed(2),
        }));

        (facility?.invoices || []).forEach((invoice) => {
            const isInclusiveGst = invoice.isInclusiveofGst;
            const orderId = invoice.orderId;

            const itemName = invoice?.itemType === "service" || invoice?.itemType === "return" ? invoice?.packageName : invoice?.productName;

            const quantity = Number(invoice?.quantity || 0);
            const price = isInclusiveGst ? Number(invoice?.price || 0) : Number(invoice?.unitPrice || 0);
            const unitPrice = invoice?.itemType === "service" || invoice?.itemType === "return" ? price : Number(invoice?.salePrice || 0);

            const subTotal = unitPrice * quantity;
            const discountExcludeCart = Number(invoice?.discountExcludeCart || 0);
            const discountIncludeCart = Number(invoice?.discountIncludeCart || 0);
            const voucherDiscount = Number(invoice?.voucherDiscount || 0);
            const returnDiscount = Number(invoice?.returnDiscountAmount || 0);

            const totalDiscount = discountExcludeCart + discountIncludeCart;

            const gstAmount = Number(invoice?.gstAmount || 0);
            const amountBeforeGst = subTotal - totalDiscount - (isInclusiveGst ? gstAmount : 0);
            const itemTotal = isInclusiveGst ? subTotal - totalDiscount : subTotal + gstAmount - totalDiscount;

            if (invoice?.itemType === "return") {
                invoiceRows.push({
                    orderId: orderId,
                    item: `${itemName} - (Return)`,
                    qty: quantity,
                    unitPrice: invoice?.totalPrice?.toFixed(2),
                    discount: 0,
                    amountBeforeGst: invoice?.basePrice?.toFixed(2),
                    gst: invoice?.gstAmount?.toFixed(2),
                    total: invoice?.totalPrice?.toFixed(2),
                    isVoucher: true,
                });
                totalAmount -= invoice?.totalPrice;
            } else {
                invoiceRows.push({
                    orderId: orderId,
                    item: itemName,
                    qty: quantity,
                    unitPrice: unitPrice.toFixed(2),
                    discount: totalDiscount.toFixed(2),
                    amountBeforeGst: amountBeforeGst.toFixed(2),
                    gst: gstAmount.toFixed(2),
                    total: itemTotal.toFixed(2),
                });
                totalAmount += itemTotal;
            }

            if (voucherDiscount > 0 && !voucherAddedOrders.has(orderId)) {
                invoiceRows.push({
                    isVoucher: true,
                    orderId: orderId,
                    item: "Voucher Applied",
                    qty: 1,
                    unitPrice: voucherDiscount.toFixed(2),
                    discount: "0.00",
                    amountBeforeGst: voucherDiscount.toFixed(2),
                    gst: "0.00",
                    total: voucherDiscount.toFixed(2),
                });
                voucherAddedOrders.add(orderId);
                totalAmount -= voucherDiscount;
            }
        });

        const imagePath = path.resolve(__dirname, "../../../image/logo_svg.png");
        const imageFile = fs.readFileSync(imagePath);
        const base64Image = `data:image/png;base64,${imageFile.toString("base64")}`;

        const templateData = {
            ...data,
            cashSectionData,
            otherPaymentsData,
            totalsData,
            otherPaymentsTotals,
            startDate: data?.startDate,
            startTime: data?.startTime,
            endDate: data?.endDate,
            endTime: data?.endTime,
            currentDate: data?.currentDate,
            currentTime: data?.currentTime,
            day: data?.day?.toUpperCase(),
            generatedBy: `${data?.name || `${data.firstName || ""} ${data.lastName || ""}`.trim()} (${data?.role})`,
            salesData: {
                paymentRows,
                invoiceRows,
                invoiceTotalAmount: Math.floor(totalAmount).toFixed(2),
                facilitySalesAmount: totalSalesAmount.toFixed(2),
                facilityName: facility?.facilityName,
            },
            image: base64Image,
        };

        const html = compiledTemplate(templateData);

        const browser = await puppeteer.launch({
            args: ["--no-sandbox", "--disable-setuid-sandbox"],
        });

        const page = await browser.newPage();
        await page.setContent(html, { waitUntil: "networkidle0" });

        const pdfBuffer = Buffer.from(await page.pdf({ format: "A4" }));
        await browser.close();

        return pdfBuffer;
    }
    private getPaymentStatusLabel(status: PaymentStatus): string {
        switch (status) {
            case PaymentStatus.REFUNDED:
                return "Refunded";
            case PaymentStatus.COMPLETED:
                return "Completed";
            case PaymentStatus.PENDING:
                return "Pending";
            case PaymentStatus.FAILED:
                return "Failed";
            case PaymentStatus.CANCELED:
                return "Canceled";
            default:
                return "";
        }
    }

    async salesByEmp(user: IUserDocument, body: ExportSalesByEmpDto, userTimezone: string) {
        const facilityDetails = await this.getOrganizationandFacility(user);
        const { facilityIds, startDate, endDate, staffIds, revenueCategories } = body;
        const isIncludingAll = revenueCategories.includes("all");
        const roleDocs = await this.roleSchema.find({}).lean();
        const userRoleId = roleDocs.find((r) => r.type === ENUM_ROLE_TYPE.USER)?._id?.toString();
        const roleMap = new Map(roleDocs.map((r) => [r._id.toString(), r.name]));
        const filter: any = {
            organizationId: new Types.ObjectId(facilityDetails.organizationId),
            paymentStatus: { $ne: PaymentStatus.CANCELED },
        };
        if (facilityIds?.length) {
            filter.facilityId = { $in: facilityIds.map((_id) => new Types.ObjectId(_id)) };
        } else if (user.role.type !== ENUM_ROLE_TYPE.ORGANIZATION && facilityDetails.facilityId) {
            const facilityArray = Array.isArray(facilityDetails.facilityId) ? facilityDetails.facilityId : [facilityDetails.facilityId];
            filter.facilityId = { $in: facilityArray.map((id) => new Types.ObjectId(id)) };
        }

        if (startDate && endDate) {
            filter.invoiceDate = {
                $gte: new Date(new Date(startDate).setHours(0, 0, 0, 0)),
                $lte: new Date(new Date(endDate).setHours(23, 59, 59, 999)),
            };
        }

        if (staffIds?.length) {
            filter.createdBy = { $in: staffIds.map((_id) => new Types.ObjectId(_id)) };
        }

        const orgData = await this.OrganizationsModel.findOne({ userId: facilityDetails.organizationId }, "isInclusiveofGst").lean();
        const isInclusiveofGst = Boolean(orgData?.isInclusiveofGst);

        const formatDate = (date: Date) => (date ? moment(date).tz(userTimezone).format("DD/MM/YYYY HH:mm") : "");

        const formatPaymentMethod = (voucher: number, status: PaymentStatus, details: any[]) => {
            let method = voucher > 0 ? `Voucher-${voucher}` : "";
            if (status === PaymentStatus.COMPLETED && details?.length) {
                const methods = details.map((p) => `${p.paymentMethodName || "UNKNOWN"}-${p.amount}`).join(" | ");
                method = method ? `${method} | ${methods}` : methods;
            }
            return method;
        };

        const formatTransactions = (details: any[]) =>
            (details || [])
                .map((p) => p?.transactionId)
                .filter(Boolean)
                .join(", ");

        const formatRow = (
            orderId: string,
            clientName: string,
            type: string,
            itemName: string,
            price: number,
            qty: number,
            subtotal: number,
            discount: number,
            afterDiscount: number,
            gst: number,
            total: number,
            invoiceDate: Date,
            status: PaymentStatus,
            paymentMethod: string,
            transactions = "",
        ) =>
            [
                "",
                orderId || "",
                clientName || "",
                type || "",
                itemName || "",
                (price || 0).toFixed(2),
                qty || 1,
                (subtotal || 0).toFixed(2),
                (discount || 0).toFixed(2),
                (afterDiscount || 0).toFixed(2),
                (gst || 0).toFixed(2),
                (total || 0).toFixed(2),
                formatDate(invoiceDate),
                this.getPaymentStatusLabel(status),
                `"${paymentMethod}"`,
                `"${transactions}"`,
            ].join(",");

        const salesData = await this.InvoiceModel.aggregate([
            { $match: filter },
            {
                $lookup: {
                    from: "users",
                    let: { creatorId: "$createdBy" },
                    pipeline: [
                        { $match: { $expr: { $eq: ["$_id", "$$creatorId"] }, role: { $ne: new Types.ObjectId(userRoleId) } } },
                        { $project: { name: 1, firstName: 1, lastName: 1, email: 1, role: 1 } },
                    ],
                    as: "creator",
                },
            },
            { $unwind: "$creator" },
            {
                $lookup: {
                    from: "facilities",
                    localField: "facilityId",
                    foreignField: "_id",
                    as: "facilityDetails",
                },
            },
            { $unwind: "$facilityDetails" },
            {
                $project: {
                    allItems: {
                        $concatArrays: [
                            { $ifNull: ["$purchaseItems", []] },
                            { $ifNull: ["$productItem", []] },
                            { $ifNull: ["$customPackageItems", []] },
                            { $ifNull: ["$returnItems", []] }
                        ]
                    },
                    organizationId: 1,
                    invoiceDate: 1,
                    orderId: 1,
                    clientDetails: 1,
                    paymentStatus: 1,
                    paymentDetails: {
                        $map: {
                            input: "$paymentDetails",
                            as: "detail",
                            in: {
                                $mergeObjects: [
                                    "$$detail",
                                    {
                                        paymentMethodName: {
                                            $let: {
                                                vars: {
                                                    matched: {
                                                        $arrayElemAt: [
                                                            {
                                                                $filter: {
                                                                    input: "$facilityDetails.paymentMethods",
                                                                    as: "pm",
                                                                    cond: {
                                                                        $eq: [
                                                                            { $toLower: { $trim: { input: "$$pm.shortId" } } },
                                                                            { $toLower: { $trim: { input: "$$detail.paymentMethod" } } },
                                                                        ],
                                                                    },
                                                                },
                                                            },
                                                            0,
                                                        ],
                                                    },
                                                },
                                                in: "$$matched.name",
                                            },
                                        },
                                    },
                                ],
                            },
                        },
                    },
                    creatorId: "$creator._id",
                    name: { $ifNull: ["$creator.name", { $concat: ["$creator.firstName", " ", "$creator.lastName"] }] },
                    email: "$creator.email",
                    role: "$creator.role",
                    voucherDiscount: 1,
                },
            },
            { $unwind: "$allItems" },
            {
                $addFields: {
                    itemType: {
                        $cond: [
                            { $ifNull: ["$allItems.invoiceId", false] },
                            "return",
                            {
                                $cond: [{ $ifNull: ["$allItems.productId", false] }, "product", "service"],
                            },
                        ],
                    },
                    price: {
                        $toDouble: {
                            $cond: [
                                { $ifNull: ["$allItems.invoiceId", false] },
                                {
                                    $cond: [{ $eq: [isInclusiveofGst, true] }, { $ifNull: ["$allItems.totalPrice", 0] }, { $ifNull: ["$allItems.basePrice", 0] }],
                                },
                                { $ifNull: ["$allItems.price", { $cond: [{ $ifNull: ["$allItems.productId", false] }, "$allItems.salePrice", "$allItems.unitPrice"] }] },
                            ],
                        },
                    },
                    quantity: { $toInt: "$allItems.quantity" },
                    discount: {
                        $add: [{ $toDouble: "$allItems.discountExcludeCart" }, { $toDouble: "$allItems.discountIncludeCart" }],
                    },
                    gstAmount: { $toDouble: "$allItems.gstAmount" },
                    packageId: "$allItems.packageId",
                    productId: "$allItems.productId",
                    packageName: "$allItems.packageName",
                    productName: "$allItems.productName",
                    voucherDiscount: { $toDouble: "$voucherDiscount" },
                    finalPrice: {
                        $cond: [{ $ifNull: ["$allItems.invoiceId", false] }, { $ifNull: ["$allItems.totalPrice", 0] }, 0],
                    },
                },
            },
            {
                $addFields: {
                    subtotal: { $multiply: ["$price", "$quantity"] },
                    amountAfterDiscount: {
                        $subtract: [{ $multiply: ["$price", "$quantity"] }, "$discount"],
                    },
                },
            },
            {
                $addFields: {
                    totalAmountAfterGst: {
                        $cond: {
                            if: { $eq: [isInclusiveofGst, false] },
                            then: { $add: ["$amountAfterDiscount", "$gstAmount"] },
                            else: "$amountAfterDiscount",
                        },
                    },
                },
            },
            {
                $lookup: {
                    from: "pricings",
                    localField: "packageId",
                    foreignField: "_id",
                    as: "pricing",
                },
            },
            { $unwind: { path: "$pricing", preserveNullAndEmptyArrays: true } },
            {
                $lookup: {
                    from: "products",
                    localField: "productId",
                    foreignField: "_id",
                    as: "product",
                },
            },
            { $unwind: { path: "$product", preserveNullAndEmptyArrays: true } },
            {
                $addFields: {
                    revenueCategoryId: {
                        $switch: {
                            branches: [
                                { case: { $eq: ["$itemType", "service"] }, then: "$pricing.revenueCategory" },
                                { case: { $eq: ["$itemType", "product"] }, then: "$product.revenueCategory" },
                            ],
                            default: null,
                        },
                    },
                },
            },
            ...(isIncludingAll ? [] : [{ $match: { revenueCategoryId: { $in: revenueCategories.map((id) => new Types.ObjectId(id)) } } }]),
            {
                $group: {
                    _id: "$creatorId",
                    name: { $first: "$name" },
                    email: { $first: "$email" },
                    role: { $first: "$role" },
                    totalSales: { $sum: "$totalAmountAfterGst" },
                    invoices: { $push: "$$ROOT" },
                },
            },
            { $sort: { totalSales: -1 } },
        ]).exec();

        let columns = isInclusiveofGst
            ? "Name (Role),Order ID,Customer Name,Type,Item Name,Price (Incl. Tax) (INR),Quantity,Subtotal (Incl. Tax) (INR),Discount (INR),Amount After Discount (Incl. Tax),Tax (INR),Amount (Incl. Tax) (INR),Invoice Date,Payment Status,Payment Method,TransactionId/Notes\n"
            : "Name (Role),Order ID,Customer Name,Type,Item Name,Price (Excl. Tax) (INR),Quantity,Subtotal (Excl. Tax) (INR),Discount (INR),Amount After Discount (Excl. Tax),Tax (INR),Amount (Incl. Tax) (INR),Invoice Date,Payment Status,Payment Method,TransactionId/Notes\n";

        for (const staff of salesData) {
            const roleName = roleMap.get(staff.role.toString()) || "";
            columns += `"${staff.name} (${roleName})",,,,,,\n`;

            let subtotalSum = 0;
            let totalAmount = 0;

            staff.invoices.sort((a, b) => a.orderId - b.orderId);
            const invoicesByOrder: Record<string, any[]> = {};
            for (const item of staff.invoices) {
                if (!invoicesByOrder[item.orderId]) invoicesByOrder[item.orderId] = [];
                invoicesByOrder[item.orderId].push(item);
            }

            for (const [orderId, invoiceItems] of Object.entries(invoicesByOrder)) {
                let voucher = invoiceItems[0]?.voucherDiscount ?? 0;
                let paymentStatus = invoiceItems[0]?.paymentStatus;
                let paymentDetails = invoiceItems[0]?.paymentDetails ?? [];

                for (const item of invoiceItems) {
                    item.itemType === "return" ? (subtotalSum -= item.finalPrice - item.gstAmount) : (subtotalSum += item.subtotal);
                    item.itemType === "return" ? (totalAmount -= item.finalPrice) : (totalAmount += item.totalAmountAfterGst);

                    const method = formatPaymentMethod(voucher, item.paymentStatus, item.paymentDetails);
                    const row = formatRow(
                        item.orderId,
                        item.clientDetails?.name || "",
                        item?.itemType === "return" ? "Return" : "Purchase",
                        item.itemType === "service" || item.itemType === "return" ? item.packageName || "" : item.productName || "",
                        item.price,
                        item.quantity,
                        item?.itemType === "return" ? -Number(item.subtotal) : item.subtotal,
                        item.discount,
                        item?.itemType === "return" ? -Number(item.price) : item.amountAfterDiscount,
                        item.gstAmount,
                        item?.itemType === "return" ? -Number(item.finalPrice) : item.totalAmountAfterGst,
                        item.invoiceDate,
                        item.paymentStatus,
                        method,
                        formatTransactions(item.paymentDetails),
                    );
                    columns += row + "\n";
                }

                if (voucher > 0) {
                    subtotalSum -= voucher;
                    totalAmount -= voucher;
                    const method = formatPaymentMethod(voucher, paymentStatus, paymentDetails);
                    columns +=
                        formatRow(
                            orderId,
                            invoiceItems[0]?.clientDetails?.name || "",
                            "Voucher Applied",
                            "Voucher Applied",
                            voucher,
                            1,
                            -voucher,
                            0,
                            -voucher,
                            0,
                            -voucher,
                            invoiceItems[0]?.invoiceDate,
                            paymentStatus,
                            method,
                        ) + "\n";
                }
            }

            columns += `,,,,,,,"- - - - - - - - - - - -",,,,"- - - - - - - - - - - -"\n`;
            columns += `"Grand Total",,,,,,,"INR ${subtotalSum.toFixed(2)}",,,,"INR ${totalAmount.toFixed(2)}"\n\n`;
        }

        columns += `\nGenerated By,${(user?.name || `${user.firstName} ${user.lastName}`)?.toUpperCase()}\nDate,${moment().tz(userTimezone).format("DD/MM/YYYY HH:mm:ss")}`;

        return { data: columns };
    }

    async salesByCategory(user: IUserDocument, body: ExportSalesReportDto, userTimezone: string) {
        const facilityDetails = await this.getOrganizationandFacility(user);
        const { facilityIds, startDate, endDate } = body;

        const filter: any = {
            organizationId: new Types.ObjectId(facilityDetails.organizationId),
            paymentStatus: { $in: [PaymentStatus.COMPLETED, PaymentStatus.PENDING] },
        };

        if (Array.isArray(facilityIds) && facilityIds.length > 0) {
            filter.facilityId = { $in: facilityIds.map((id) => new Types.ObjectId(id)) };
        } else if (user.role.type !== ENUM_ROLE_TYPE.ORGANIZATION && facilityDetails.facilityId) {
            const facilityArray = Array.isArray(facilityDetails.facilityId) ? facilityDetails.facilityId : [facilityDetails.facilityId];
            filter.facilityId = { $in: facilityArray.map((id) => new Types.ObjectId(id)) };
        }
        if (startDate && endDate) {
            filter.invoiceDate = {
                $gte: new Date(new Date(startDate).setHours(0, 0, 0, 0)),
                $lte: new Date(new Date(endDate).setHours(23, 59, 59, 999)),
            };
        }

        const orgData = await this.OrganizationsModel.findOne({ userId: facilityDetails.organizationId }, "isInclusiveofGst").lean();
        const isInclusiveofGst = Boolean(orgData?.isInclusiveofGst);
        let pipeline: any[] = [
            { $match: filter },
            {
                $project: {
                    allItems: {
                        $concatArrays: [
                            { $ifNull: ["$purchaseItems", []] },
                            { $ifNull: ["$productItem", []] },
                            {
                                $ifNull: ["$customPackageItems", []]
                            },
                            { $ifNull: ["$returnItems", []] }
                        ]
                    },
                    organizationId: 1,
                    voucherDiscount: 1
                }
            },
            { $unwind: "$allItems" },
            {
                $addFields: {
                    itemType: {
                        $cond: [
                            {
                                $ifNull: [
                                    "$allItems.invoiceId",
                                    false
                                ]
                            },
                            "return",
                            {
                                $cond: [
                                    {
                                        $ifNull: [
                                            "$allItems.productId",
                                            false
                                        ]
                                    },
                                    "product",
                                    "service"
                                ]
                            }
                        ]
                    },
                    price: {
                        $toDouble: {
                            $cond: [
                                {
                                    $ifNull: [
                                        "$allItems.invoiceId",
                                        false
                                    ]
                                },
                                {
                                    $cond: [
                                        { $eq: [isInclusiveofGst, true] },
                                        {
                                            $ifNull: [
                                                "$allItems.totalPrice",
                                                0
                                            ]
                                        },
                                        {
                                            $ifNull: [
                                                "$allItems.basePrice",
                                                0
                                            ]
                                        }
                                    ]
                                },
                                {
                                    $ifNull: [
                                        "$allItems.price",
                                        {
                                            $cond: [
                                                {
                                                    $ifNull: [
                                                        "$allItems.productId",
                                                        false
                                                    ]
                                                },
                                                "$allItems.salePrice",
                                                "$allItems.unitPrice"
                                            ]
                                        }
                                    ]
                                }
                            ]
                        }
                    },
                    quantity: { $toInt: "$allItems.quantity" },
                    discountExcludeCart: {
                        $toDouble: {
                            $ifNull: [
                                "$allItems.discountExcludeCart",
                                0
                            ]
                        }
                    },
                    discountIncludeCart: {
                        $toDouble: {
                            $ifNull: [
                                "$allItems.discountIncludeCart",
                                0
                            ]
                        }
                    },
                    gstAmount: {
                        $toDouble: "$allItems.gstAmount"
                    },
                    finalAmountUnit: {
                        $toDouble: {
                            $ifNull: ["$allItems.totalPrice", 0]
                        }
                    },
                    packageId: "$allItems.packageId",
                    productId: "$allItems.productId"
                }
            },
            {
                $addFields: {
                    subtotal: {
                        $multiply: ["$price", "$quantity"]
                    },
                    totalDiscount: {
                        $add: [
                            "$discountExcludeCart",
                            "$discountIncludeCart"
                        ]
                    },
                    totalAmountAfterDiscount: {
                        $subtract: [
                            { $multiply: ["$price", "$quantity"] },
                            {
                                $add: [
                                    "$discountExcludeCart",
                                    "$discountIncludeCart"
                                ]
                            }
                        ]
                    }
                }
            },
            {
                $addFields: {
                    totalAmountAfterGst: {
                        $cond: {
                            if: { $eq: [isInclusiveofGst, false] },
                            then: {
                                $add: [
                                    "$totalAmountAfterDiscount",
                                    "$gstAmount"
                                ]
                            },
                            else: "$totalAmountAfterDiscount"
                        }
                    }
                }
            },
            {
                $lookup: {
                    from: "pricings",
                    localField: "packageId",
                    foreignField: "_id",
                    as: "pricing"
                }
            },
            {
                $unwind: {
                    path: "$pricing",
                    preserveNullAndEmptyArrays: true
                }
            },
            {
                $lookup: {
                    from: "services",
                    localField:
                        "pricing.services.serviceCategory",
                    foreignField: "_id",
                    as: "serviceCategory"
                }
            },
            {
                $unwind: {
                    path: "$serviceCategory",
                    preserveNullAndEmptyArrays: false
                }
            },
            {
                $group: {
                    _id: "$pricing.services.serviceCategory",
                    serviceCategoryId: {
                        $first: "$serviceCategory._id"
                    },
                    serviceCategoryName: {
                        $first: "$serviceCategory.name"
                    },
                    totalSubtotal: {
                        $sum: {
                            $cond: [
                                { $ne: ["$itemType", "return"] },
                                "$subtotal",
                                0
                            ]
                        }
                    },
                    totalDiscount: {
                        $sum: {
                            $cond: [
                                { $ne: ["$itemType", "return"] },
                                "$totalDiscount",
                                0
                            ]
                        }
                    },
                    totalAmountAfterDiscount: {
                        $sum: {
                            $cond: [
                                { $ne: ["$itemType", "return"] },
                                "$totalAmountAfterDiscount",
                                0
                            ]
                        }
                    },
                    totalGstAmount: {
                        $sum: {
                            $cond: [
                                { $ne: ["$itemType", "return"] },
                                "$gstAmount",
                                0
                            ]
                        }
                    },
                    totalAmountAfterGst_nonReturn: {
                        $sum: {
                            $cond: [
                                { $ne: ["$itemType", "return"] },
                                "$totalAmountAfterGst",
                                0
                            ]
                        }
                    },
                    totalReturnAmountWithGst: {
                        $sum: {
                            $cond: [
                                { $eq: ["$itemType", "return"] },
                                "$totalAmountAfterGst",
                                0
                            ]
                        }
                    }
                }
            },
            {
                $addFields: {
                    serviceCategoryName: {
                        $ifNull: ["$serviceCategoryName", "Other"]
                    },
                    finalAmountInclTax: {
                        $subtract: [
                            "$totalAmountAfterGst_nonReturn",
                            "$totalReturnAmountWithGst"
                        ]
                    }
                }
            },
            {
                $group: {
                    _id: null,
                    categories: { $push: "$$ROOT" },
                    grandTotal: {
                        $sum: {
                            $cond: [
                                { $gt: ["$finalAmountInclTax", 0] },
                                "$finalAmountInclTax",
                                0
                            ]
                        }
                    }
                }
            },
            { $unwind: "$categories" },
            {
                $addFields: {
                    "categories.revenuePercent": {
                        $cond: [
                            {
                                $and: [
                                    {
                                        $gt: [
                                            "$categories.finalAmountInclTax",
                                            0
                                        ]
                                    },
                                    {
                                        $ne: [
                                            "$categories.serviceCategoryName",
                                            "Other"
                                        ]
                                    }
                                ]
                            },
                            {
                                $round: [
                                    {
                                        $multiply: [
                                            {
                                                $divide: [
                                                    "$categories.finalAmountInclTax",
                                                    "$grandTotal"
                                                ]
                                            },
                                            100
                                        ]
                                    },
                                    2
                                ]
                            },
                            0
                        ]
                    }
                }
            },
            { $replaceRoot: { newRoot: "$categories" } },
            {
                $project: {
                    serviceCategoryName: 1,
                    totalSubtotal: {
                        $round: ["$totalSubtotal", 2]
                    },
                    totalDiscount: {
                        $round: ["$totalDiscount", 2]
                    },
                    totalAmountAfterDiscount: {
                        $round: ["$totalAmountAfterDiscount", 2]
                    },
                    totalGstAmount: {
                        $round: ["$totalGstAmount", 2]
                    },
                    totalAmountAfterGst_nonReturn: {
                        $round: [
                            "$totalAmountAfterGst_nonReturn",
                            2
                        ]
                    },
                    totalReturnAmountWithGst: {
                        $round: ["$totalReturnAmountWithGst", 2]
                    },
                    finalAmountInclTax: {
                        $round: ["$finalAmountInclTax", 2]
                    },
                    revenuePercent: 1
                }
            },
            { $sort: { serviceCategoryName: 1 } }
        ];
        const invoiceDetails = await this.InvoiceModel.aggregate(pipeline).exec();

        const columns = isInclusiveofGst
            ? `Category,Subtotal (Incl. Tax) (INR),Discount (INR),Tax (INR),Total Amount After Tax (INR),Return Amount (INR),Final Amount (Incl. Tax) (INR),% of Revenue\n`
            : `Category,Subtotal (Excl. Tax) (INR),Discount (INR),Tax (INR),Total Amount After Tax (INR),Return Amount (INR),Final Amount (Incl. Tax) (INR),% of Revenue\n`;
        let data =
            columns +
            (invoiceDetails || [])
                .map((item: any) =>
                    [
                        item?.serviceCategoryName || "",
                        item?.totalSubtotal ?? 0,
                        item?.totalDiscount ? - item.totalDiscount : 0,
                        item?.totalGstAmount ?? 0,
                        item?.totalAmountAfterGst_nonReturn ?? 0,
                        item?.totalReturnAmountWithGst ? -item.totalReturnAmountWithGst : 0,
                        item?.finalAmountInclTax ?? 0,
                        item?.revenuePercent ?? 0,
                    ].join(","),
                )
                .join("\n");

        data +=
            `\nGenerated By,${user?.name?.toUpperCase() || `${user.firstName?.toUpperCase() || ""} ${user.lastName?.toUpperCase() || ""}`}\n` +
            `Date,${moment(new Date()).tz(userTimezone).format("DD/MM/YYYY HH:mm:ss")}`;
        return { data };
    }

    async exportSalesList(user: IUserDocument, body: ExportSalesReportDto, userTimezone: string) {
        const facilityDetails = await this.getOrganizationandFacility(user);
        const { facilityIds, startDate, endDate, category } = body;

        const filter: any = {
            organizationId: new Types.ObjectId(facilityDetails.organizationId),
        };

        if (Array.isArray(facilityIds) && facilityIds.length > 0) {
            filter["facilityId"] = { $in: facilityIds.map((_id) => new Types.ObjectId(_id)) };
        } else if (user.role.type !== ENUM_ROLE_TYPE.ORGANIZATION && facilityDetails.facilityId) {
            const facilityArray = Array.isArray(facilityDetails.facilityId) ? facilityDetails.facilityId : [facilityDetails.facilityId];

            filter["facilityId"] = {
                $in: facilityArray.map((id) => new Types.ObjectId(id)),
            };
        }

        if (startDate && endDate) {
            const startOfDay = new Date(new Date(startDate).setHours(0, 0, 0, 0));
            const endOfDay = new Date(new Date(endDate).setHours(23, 59, 59, 999));
            filter["invoiceDate"] = {
                $gte: startOfDay,
                $lte: endOfDay,
            };
        }
        let invoiceDetails: any = await this.InvoiceModel.find(filter)
            .populate([
                { path: "createdBy", select: "_id name firstName lastName email" },
                { path: "cancelledBy", select: "firstName lastName name" },
                {
                    path: "facilityId",
                    select: "_id facilityName paymentMethods name",
                    populate: { path: "paymentMethods.paymentMethodId", model: "PaymentMethod", select: "_id name methodType" },
                },
            ])
            .sort({ orderId: 1 });
        let allSalesData = [];

        const organizationData = await this.OrganizationsModel.findOne({ userId: facilityDetails.organizationId }, "isInclusiveofGst").lean();
        const isInclusiveofGst = organizationData?.isInclusiveofGst || false;
        invoiceDetails.map((invoice) => {
            if (invoice && invoice.facilityId && invoice.facilityId.paymentMethods) {
                const updatedPaymentDetails = invoice.paymentDetails.map((detail) => {
                    const matchedMethod = invoice.facilityId.paymentMethods.find((pm) => pm.shortId.trim().toLowerCase() === detail.paymentMethod.trim().toLowerCase());
                    const cleanDetail = JSON.parse(JSON.stringify(detail));
                    return {
                        ...cleanDetail,
                        paymentMethodName: matchedMethod ? matchedMethod.name : "Unknown",
                    };
                });
                invoice.updatedPaymentDetails = updatedPaymentDetails;
            }

            if (!invoice) {
                throw new NotFoundException("Invoice not found or you do not have access to it");
            }

            const clientBillingUTCode = invoice.clientBillingDetails?.utCode;
            const billingUTCode = invoice.billingDetails?.utCode;
            const totalGSTValue = invoice?.totalGstValue;

            if (clientBillingUTCode === billingUTCode) {
                invoice.cgst = totalGSTValue / 2;
                invoice.sgst = totalGSTValue / 2;
                invoice.igst = 0;
            } else {
                invoice.igst = totalGSTValue;
                invoice.cgst = 0;
                invoice.sgst = 0;
            }
            const baseInvoiceData = {
                _id: invoice._id,
                createdBy: invoice.createdBy ? invoice.createdBy._id : null,
                createdByName: invoice.createdBy ? invoice.createdBy.name : null,
                invoiceDate: invoice.invoiceDate,
                invoiceNumber: invoice.invoiceNumber,
                orderId: invoice?.orderId || "",
                organizationId: invoice.organizationId,
                facilityId: invoice?.facilityId,
                facilityName: invoice?.billingDetails?.facilityName,
                billingDetails: invoice?.billingDetails,
                clientDetails: invoice?.clientDetails,
                clientBillingDetails: invoice?.clientBillingDetails,
                totalItems: invoice?.productItem?.length + invoice?.purchaseItems?.length,
                subTotal: invoice?.subTotal,
                discount: invoice?.discount || 0,
                cartDiscount: invoice?.cartDiscount || 0,
                cartDiscountType: invoice?.cartDiscountType || "",
                cartDiscountAmount: invoice?.cartDiscountAmount || 0,
                totalGstValue: invoice?.totalGstValue || 0,
                totalAmountAfterGst: invoice?.totalAmountAfterGst || 0,
                roundOff: invoice?.roundOff || 0,
                grandTotal: invoice?.grandTotal || 0,
                amountInWords: invoice?.amountInWords ? invoice?.amountInWords : "",
                amountPaid: invoice?.amountPaid,
                paymentStatus: invoice.paymentStatus,
                paymentDetails: invoice.paymentDetails,
                invoiceStatus: invoice.invoiceStatus,
                refundStatus: invoice.refundStatus,
                refundAmount: invoice.refundAmount,
                platform: invoice.platform,
                cgst: invoice.cgst,
                sgst: invoice.sgst,
                igst: invoice.igst,
                voucherDiscount: invoice.voucherDiscount || 0,
                updatedPaymentDetails: invoice.updatedPaymentDetails,
                paymentReason: invoice.paymentReason,
                cancelledBy: invoice?.cancelledBy?.name
                    ? invoice.cancelledBy?.name
                    : invoice.cancelledBy?.firstName
                        ? invoice.cancelledBy?.firstName + " " + invoice.cancelledBy?.lastName
                        : null,
            };
            if (!category || category.includes(ReportCategory.SERVICE)) {
                invoice?.purchaseItems?.forEach((item: any) => {
                    allSalesData.push({
                        ...baseInvoiceData,
                        itemType: "service",
                        itemDetail: item,
                    });
                });
            }
            if (!category || category.includes(ReportCategory.PRODUCT)) {
                invoice?.productItem?.forEach((item: any) => {
                    allSalesData.push({
                        ...baseInvoiceData,
                        itemType: "product",
                        itemDetail: item,
                    });
                });
            }
            if (!category || category.includes(ReportCategory.SERVICE)) {
                invoice?.customPackageItems?.forEach((item: any) => {
                    allSalesData.push({
                        ...baseInvoiceData,
                        itemType: "service",
                        itemDetail: item,
                    });
                });
            }

            invoice?.returnItems?.forEach((item: any) => {
                allSalesData.push({
                    ...baseInvoiceData,
                    itemType: "return",
                    itemDetail: item,
                });
            });
            if (invoice.voucherDiscount && invoice.voucherDiscount > 0) {
                allSalesData.push({
                    ...baseInvoiceData,
                    itemType: "voucher",
                    itemDetail: {
                        productName: "Voucher Applied",
                        packageName: "Voucher Applied",
                        quantity: 1,
                        price: invoice.voucherDiscount,
                        unitPrice: invoice.voucherDiscount,
                        discountExcludeCart: 0,
                        discountIncludeCart: 0,
                        tax: 0,
                        gstAmount: 0,
                        hsnOrSacCode: "",
                    },
                });
            }
        });

        const columns = isInclusiveofGst
            ? `Sale Date (${userTimezone}),Client Id,Client Name,Order ID,Item Type,Type,Item Name,Location,Item Price (Incl. Tax)  (INR),Quantity,Discount Amount (INR),Cart Discount Amount (INR), Amount After Discount (Incl. Tax) (INR),TAX(%),CGST (INR),SGST (INR),IGST (INR),Item Total (Incl. Tax) (INR), HSN/SAC Code,Invoice Total (INR),Amount Paid (INR),Payment Method,Payment Status,Cancelled By,Reason\n`
            : `Sale Date (${userTimezone}),Client Id,Client Name,Order ID,Item Type,Type,Item Name,Location,Item Price (Excl. Tax)  (INR),Quantity,Discount Amount (INR),Cart Discount Amount (INR), Amount After Discount (Excl. Tax) (INR),TAX(%),CGST (INR),SGST (INR),IGST (INR),Item Total (Incl. Tax) (INR), HSN/SAC Code,Invoice Total (INR),Amount Paid (INR),Payment Method,Payment Status,Cancelled By,Reason\n`;
        let data =
            columns +
            allSalesData
                .map((item: any) => {
                    const quantity = Number(item?.itemDetail?.quantity || 0);
                    let price: number;
                    if (item?.itemType === "return") {
                        price = isInclusiveofGst ? Number(item?.itemDetail?.totalPrice ?? 0) : Number(item?.itemDetail?.basePrice ?? 0);
                    } else {
                        price = Number(item?.itemDetail?.price ?? (item?.itemType === "service" ? item?.itemDetail?.unitPrice : item?.itemDetail?.salePrice) ?? 0);
                    }
                    const discountExcludeCart = Number(item?.itemDetail?.discountExcludeCart || 0);
                    const discountIncludeCart = Number(item?.itemDetail?.discountIncludeCart || 0);
                    const amountAfterDiscount = price * quantity - (discountExcludeCart + discountIncludeCart);
                    const tax = Number(item?.itemDetail?.tax || 0).toFixed(2);
                    const gstAmount = Number(item?.itemDetail?.gstAmount || 0);
                    const cgst = (item?.clientBillingDetails?.utCode === item?.billingDetails?.utCode ? gstAmount / 2 : 0).toFixed(2);
                    const sgst = (item?.clientBillingDetails?.utCode === item?.billingDetails?.utCode ? gstAmount / 2 : 0).toFixed(2);
                    const igst = (item?.clientBillingDetails?.utCode !== item?.billingDetails?.utCode ? gstAmount : 0).toFixed(2);
                    const itemTotal = (amountAfterDiscount + (isInclusiveofGst ? 0 : gstAmount)).toFixed(2);
                    const amountPaid = item?.amountPaid ? `${item?.amountPaid.toFixed(2)}` : ``;
                    const cancelledBy = item?.cancelledBy;
                    let paymentMethod = item.voucherDiscount ? `Voucher-${item.voucherDiscount}` : "";

                    if (item.paymentStatus === PaymentStatus.COMPLETED) {
                        const methods = item.updatedPaymentDetails?.map((payment) => `${payment.paymentMethodName || "UNKNOWN"}-${payment.amount}`).join(" | ");

                        paymentMethod = paymentMethod ? `${paymentMethod} | ${methods}` : methods;
                    }

                    const row = [
                        `${moment(item.invoiceDate).tz(userTimezone).format("DD/MM/YYYY")}-${moment(item.invoiceDate).tz(userTimezone).format("HH:mm")}`,
                        item?.clientDetails?.customerId,
                        item?.clientDetails?.name,
                        item.orderId,
                        item?.itemType === "return" ? "service" : item?.itemType,
                        item?.itemType === "return" ? "Return" : item?.itemType === "voucher" ? "Voucher Applied" : "Purchase",
                        item?.itemType === "service" || item.itemType === "return" ? item?.itemDetail?.packageName : item?.itemDetail?.productName,
                        item?.billingDetails?.facilityName,
                        `${price.toFixed(2)}`,
                        quantity,
                        `${discountExcludeCart.toFixed(2)}`,
                        `${discountIncludeCart.toFixed(2)}`,
                        `${(item?.itemType === "voucher" ? -Number(amountAfterDiscount) : amountAfterDiscount).toFixed(2)}`,
                        `${tax} (%)`,
                        `${cgst}`,
                        `${sgst}`,
                        `${igst}`,
                        `${item?.itemType === "voucher" || item?.itemType === "return" ? -Number(itemTotal) : itemTotal}`,
                        item?.itemDetail?.hsnOrSacCode,
                        `${item.grandTotal}`,
                        amountPaid,
                        paymentMethod,
                        this.getPaymentStatusLabel(item.paymentStatus),
                        cancelledBy,
                        item?.paymentReason,
                    ];
                    return row.join(",");
                })
                .join("\n");

        data +=
            `\nGenerated By,${user?.name?.toUpperCase() || `${user.firstName?.toUpperCase() || ""} ${user.lastName?.toUpperCase() || ""}`}\n` +
            `Date,${moment(new Date()).tz(userTimezone).format("DD/MM/YYYY HH:mm:ss")}`;

        return { data };
    }

    async getQrCodesForUser(
        userId: string,
        page = 1,
        limit = 10,
    ): Promise<{
        results: {
            qrCodeUrl: string | null;
            packageId: string;
            packageName: string;
            isExpired: boolean;
            purchaseId: string;
        }[];
        total: number;
    }> {
        const skip = (page - 1) * limit;
        const today = new Date();

        const purchaseFilter: any = {
            itemType: ENUM_PRODUCT_ITEM_TYPE.SERVICE,
            consumers: userId, // if stored as ObjectId: new Types.ObjectId(userId)
            isExpired: false,
            endDate: { $gt: today },
            $expr: {
                $lt: ["$sessionConsumed", "$totalSessions"],
            },
        };

        const distinctPackageIds: string[] = await this.PurchaseModel.distinct("packageId", purchaseFilter);

        const candidatePricings = await this.PricingModel.find({
            _id: { $in: distinctPackageIds },
            isActive: true,
        })
            .select("_id name expiryDate validTill endDate isActive")
            .lean();

        const isPricingExpired = (p: any, ref: Date) => {
            const dates = [p?.expiryDate, p?.validTill, p?.endDate].filter(Boolean).map((d: any) => new Date(d));
            if (dates.length === 0) return false;
            const minExpiry = new Date(Math.min(...dates.map((d) => d.getTime())));
            return !(minExpiry > ref);
        };

        const activeValidPricings = candidatePricings.filter((p) => !isPricingExpired(p, today));
        const activeValidPackageIdSet = new Set(activeValidPricings.map((p) => p._id.toString()));
        const packageNameMap = new Map(activeValidPricings.map((p) => [p._id.toString(), p.name as string]));

        const total = await this.PurchaseModel.countDocuments({
            ...purchaseFilter,
            packageId: { $in: Array.from(activeValidPackageIdSet) },
        });

        const purchases = await this.PurchaseModel.find(purchaseFilter).select("qrCodeUrl packageId isExpired").sort({ _id: -1 }).skip(skip).limit(limit).lean();

        const results = purchases
            .filter((p) => p.packageId && activeValidPackageIdSet.has(p.packageId.toString()))
            .map((p) => ({
                qrCodeUrl: p.qrCodeUrl ? p.qrCodeUrl.toString() : null,
                packageId: p.packageId?.toString(),
                packageName: packageNameMap.get(p.packageId?.toString()) || "N/A",
                isExpired: p.isExpired,
                purchaseId: p._id,
            }));

        return { results, total };
    }

    async fetchQrBookingData(user: any, purchaseId: string): Promise<any> {
        const session = await this.PurchaseModel.db.startSession();
        session.startTransaction();

        try {
            // Ensure purchaseId is converted to ObjectId
            let purchaseIdObj;
            try {
                purchaseIdObj = new Types.ObjectId(purchaseId);
            } catch (error) {
                throw new BadRequestException("Invalid purchase ID format");
            }

            // Fetch purchase details from the Purchase table using ObjectId
            const purchase = await this.PurchaseModel.findOne({ _id: purchaseIdObj, itemType: ENUM_PRODUCT_ITEM_TYPE.SERVICE });

            if (!purchase) {
                throw new NotFoundException("No purchase found for this purchase ID");
            }

            const today = new Date();

            // CHECK 1: Check if the package is expired (isExpired field or endDate check)
            if (purchase.isExpired || purchase.endDate < today) {
                throw new BadRequestException("Package expired");
            }

            // CHECK 2: Check if all sessions have been consumed
            if (purchase.sessionConsumed >= purchase.totalSessions) {
                throw new BadRequestException("All sessions used");
            }

            // CHECK 3: Check if the package is active
            if (!purchase.isActive) {
                throw new BadRequestException("Package is not active");
            }

            // CHECK 4: Ensure the current date is within the valid date range of the package (startDate <= today && endDate >= today)
            if (purchase.startDate > today || purchase.endDate < today) {
                throw new BadRequestException("The package is not within the valid date range");
            }

            // Fetch the package details from PricingModel using packageId
            const packageDetails = await this.PricingModel.findOne({ _id: purchase.packageId });

            if (!packageDetails) {
                throw new NotFoundException("No package found for this purchase");
            }

            // Fetch the facility details from FacilitiesModel using facilityId
            const facilityDetails = await this.FacilityModel.findOne({ _id: purchase.facilityId });

            if (!facilityDetails) {
                throw new NotFoundException("No facility found for this purchase");
            }

            // Fetch client details using purchase.userId
            const clientDetails = await this.UserModel.findOne({ _id: purchase.userId });

            if (!clientDetails) {
                throw new NotFoundException("No client found for this purchase");
            }

            // Extract service details from packageDetails.services
            const services = packageDetails.services;

            // Get the booking type from the services.type
            const bookingType = services?.type || "N/A";
            const serviceCategoryId = services?.serviceCategory?.toString() || "N/A";
            const subTypeId = services?.appointmentType && services.appointmentType.length > 0 ? services.appointmentType[0].toString() : "N/A";

            // Build the response dynamically from the purchase data
            const response = {
                message: "Client list Fetched successfully",
                data: {
                    _id: purchase._id.toString(),
                    scheduledBy: purchase.organizationId,
                    facilityId: purchase.facilityId,
                    organizationId: purchase.organizationId,
                    clientId: purchase.userId,
                    clientName: `${clientDetails.firstName} ${clientDetails.lastName}`,
                    clientFirstName: clientDetails.firstName,
                    clientLastName: clientDetails.lastName,
                    clientEmail: clientDetails.email,
                    clientPhone: clientDetails.mobile,
                    purchaseId: purchase._id.toString(),
                    packageId: purchase.packageId,
                    subTypeDuration: packageDetails.durationUnit || "N/A",
                    sessions: purchase.totalSessions || 1,
                    date: purchase.purchaseDate || new Date(),
                    facilityName: facilityDetails.facilityName || "N/A",
                    packageName: packageDetails.name || "N/A",
                    totalSession: purchase.totalSessions || 0,
                    sessionConsumed: purchase.sessionConsumed || 0,
                    packagePrice: packageDetails.price || 0,
                    // packageGstAmount: packageDetails.gstAmount || 0,
                    // packageFinalPrice: packageDetails.finalPrice || 0,
                    facilityContactName: facilityDetails.contactName || "N/A",
                    facilityEmail: facilityDetails.email || "N/A",
                    facilityMobile: facilityDetails.mobile || "N/A",
                    bookingType: bookingType,
                    serviceCategoryId: serviceCategoryId,
                    subTypeId: subTypeId,
                },
            };

            await session.commitTransaction();
            return response; // Return single data object for a single purchase
        } catch (error) {
            await session.abortTransaction();
            throw new BadRequestException(error.message);
        } finally {
            session.endSession();
        }
    }

    private splitGSTInclusivePrice(finalPrice, gstPercent) {
        const basePrice = +(finalPrice / (1 + gstPercent / 100));
        const gstAmount = +(finalPrice - basePrice).toFixed(2);
        return { basePrice, gstAmount, finalPrice };
    }

    private addGSTToBasePrice(basePrice, gstPercent) {
        const gstAmount = +(basePrice * (gstPercent / 100));
        const finalPrice = +(basePrice + gstAmount).toFixed(2);
        return { basePrice, gstAmount, finalPrice };
    }

    async getEligibleReturnPricingByUserId(userId: string, body: PaginationDto, organizationId: IDatabaseObjectId): Promise<any> {
        let { page = 1, pageSize = 10, search } = body;
        if (isNaN(page as any) || page < 1) page = 1;
        if (isNaN(pageSize as any) || pageSize < 1) pageSize = 10;
        const skip = (page - 1) * pageSize;

        const isInclusiveofGst = await this.organizationService.checkIsInclusiveGst(organizationId);

        const query: any = {
            itemType: ENUM_PRODUCT_ITEM_TYPE.SERVICE,
            userId: new Types.ObjectId(userId),
            organizationId: new Types.ObjectId(organizationId),
            isExpired: false,
            endDate: { $gt: new Date() },
            isActive: { $ne: false },
            bundledPricingId: { $exists: false },
            isExchanged: { $ne: true },
            exchangedInvoiceId: { $exists: false },
            sharePass: { $ne: true },
            paymentStatus: PaymentStatus.COMPLETED,
        };

        const pipeline: PipelineStage[] = [
            { $match: query },
            {
                $lookup: {
                    from: "invoices",
                    localField: "invoiceId",
                    foreignField: "_id",
                    as: "invoiceDetails",
                },
            },
            { $unwind: { path: "$invoiceDetails", preserveNullAndEmptyArrays: false } },
            {
                $addFields: {
                    matchedPurchaseItem: {
                        $arrayElemAt: [
                            {
                                $filter: {
                                    input: "$invoiceDetails.purchaseItems",
                                    as: "item",
                                    cond: { $eq: ["$$item.packageId", "$packageId"] },
                                },
                            },
                            0,
                        ],
                    },
                },
            },
            // Project fields we need + packageId for pricing lookup
            {
                $project: {
                    _id: 0,
                    purchaseId: "$_id",
                    invoiceId: 1,
                    sessionType: 1,
                    paymentStatus: 1,
                    price: {
                        $ifNull: ["$matchedPurchaseItem.price", "$matchedPurchaseItem.unitPrice"],
                    },
                    isInclusiveofGst: {
                        $ifNull: ["$invoiceDetails.isInclusiveofGst", false],
                    },
                    tax: "$matchedPurchaseItem.tax",
                    gstAmount: "$matchedPurchaseItem.gstAmount",

                    packageId: "$matchedPurchaseItem.packageId",
                    packageName: "$matchedPurchaseItem.packageName",

                    packageStartDate: "$startDate",
                    packageEndDate: "$endDate",
                    totalSessions: 1,
                    sessionConsumed: 1,
                    dayPassLimit: 1,

                    unitPrice: "$matchedPurchaseItem.unitPrice",
                    discountExcludeCart: {
                        $cond: [{ $gt: ["$matchedPurchaseItem.quantity", 0] }, { $divide: ["$matchedPurchaseItem.discountExcludeCart", "$matchedPurchaseItem.quantity"] }, 0],
                    },
                    discountIncludeCart: {
                        $cond: [{ $gt: ["$matchedPurchaseItem.quantity", 0] }, { $divide: ["$matchedPurchaseItem.discountIncludeCart", "$matchedPurchaseItem.quantity"] }, 0],
                    },
                    updatedAt: 1,
                },
            },
            // 🔗 MATCH PACKAGE -> PRICING
            {
                $lookup: {
                    from: "pricings",
                    localField: "packageId",
                    foreignField: "_id",
                    as: "pricing",
                },
            },
            { $unwind: { path: "$pricing", preserveNullAndEmptyArrays: true } },
            // keep only what we need from pricing to compute inclusive GST like activePricingList
            {
                $addFields: {
                    pricingPrice: { $ifNull: ["$pricing.price", 0] },
                    pricingTaxPct: { $ifNull: ["$pricing.tax", 0] },
                },
            },
        ];

        if (search && search.trim().length > 0) {
            pipeline.push({
                $match: { packageName: { $regex: search.trim(), $options: "i" } },
            });
        }

        pipeline.push(
            {
                $facet: {
                    total: [{ $count: "count" }],
                    data: [{ $sort: { updatedAt: -1 } }, { $skip: skip }, { $limit: pageSize }],
                },
            },
            {
                $project: {
                    total: { $ifNull: [{ $arrayElemAt: ["$total.count", 0] }, 0] },
                    data: 1,
                },
            },
        );

        const result = await this.PurchaseModel.aggregate(pipeline);
        const date = moment().utc(true).startOf("day").toDate();

        const round2 = (n: number) => Math.max(0, Math.round((Number(n) + Number.EPSILON) * 100) / 100);

        const data = await Promise.all(
            (result?.length && result[0].data ? result[0].data : []).map(async (item: any) => {
                const price = Number(item.price || 0);
                const taxPct = Number(item.tax || 0);
                const perUnitDiscount = round2(Number(item.discountExcludeCart || 0) + Number(item.discountIncludeCart || 0));
                const refundAmount = item.paymentStatus === PaymentStatus.COMPLETED ? round2((Number(price) || 0) - perUnitDiscount) : 0;
                let claculatePrice = item?.isInclusiveofGst ? this.splitGSTInclusivePrice(refundAmount, taxPct) : this.addGSTToBasePrice(refundAmount, taxPct);
                let basePriceToRefund = claculatePrice.basePrice;
                let gstAmountToRefund = claculatePrice.gstAmount;
                let gstInclusivePrice = claculatePrice.finalPrice;
                let availableSessions = 0;

                switch (item.sessionType) {
                    case SessionType.SINGLE:
                        availableSessions = 1 - (item.sessionConsumed || 0);
                        break;

                    case SessionType.ONCEADAY: {
                        const sessionsCounts = await this.SchedulingModel.aggregate([
                            {
                                $match: {
                                    purchaseId: item.purchaseId,
                                    clientId: new Types.ObjectId(userId),
                                    date: date,
                                    scheduleStatus: { $nin: [ScheduleStatusType.CANCELED] },
                                },
                            },
                            { $group: { _id: null, sessions: { $sum: "$sessions" } } },
                        ]);
                        const userSessionsForTheDay = sessionsCounts.length ? sessionsCounts[0]["sessions"] : 0;
                        availableSessions = 1 - userSessionsForTheDay;
                        break;
                    }

                    case SessionType.MULTIPLE:
                        availableSessions = Math.max(0, (item?.totalSessions || 0) - (item?.sessionConsumed || 0));
                        break;

                    case SessionType.DAY_PASS: {
                        const sessionsCount = await this.SchedulingModel.aggregate([
                            {
                                $match: {
                                    purchaseId: item.purchaseId,
                                    clientId: new Types.ObjectId(userId),
                                    scheduleStatus: { $nin: [ScheduleStatusType.CANCELED] },
                                },
                            },
                            { $group: { _id: "$date" } },
                        ]);
                        const bookedDates = sessionsCount.map((d: any) => d._id.toISOString().split("T")[0]);
                        const consumedDayPassLimit = bookedDates.length;
                        availableSessions = Math.max(0, (item.dayPassLimit || 0) - consumedDayPassLimit);
                        break;
                    }

                    case SessionType.UNLIMITED: {
                        availableSessions = 0;
                        const sessionPerDay = (item as any)?.sessionPerDay || 0;
                        if (sessionPerDay) {
                            const userSessionsForTheDay = await this.SchedulingModel.count({
                                purchaseId: item.purchaseId,
                                clientId: new Types.ObjectId(userId),
                                date: date,
                                scheduleStatus: { $nin: [ScheduleStatusType.CANCELED] },
                            });
                            availableSessions = Math.max(0, sessionPerDay - userSessionsForTheDay);
                        }
                        break;
                    }

                    default:
                        availableSessions = 0;
                }

                return {
                    ...item,
                    currentGstStatus: isInclusiveofGst,
                    discountedValue: perUnitDiscount,
                    refundAmount,
                    remainingSessions: availableSessions,
                    basePriceToRefund,
                    gstAmountToRefund,
                    gstInclusivePrice,
                };
            }),
        );

        return {
            data,
            count: result.length ? result[0].total : 0,
        };
    }

    async getEligibleReturnCustomPackageByUserId(userId: string, body: PaginationDto, organizationId: IDatabaseObjectId): Promise<any> {
        let { page = 1, pageSize = 10, search } = body;
        if (isNaN(page) || page < 1) page = 1;
        if (isNaN(pageSize) || pageSize < 1) pageSize = 10;
        const skip = (page - 1) * pageSize;

        const query: any = {
            userId: new Types.ObjectId(userId),
            organizationId: new Types.ObjectId(organizationId),
            paymentStatus: PaymentStatus.COMPLETED,
        };

        const pipeline: PipelineStage[] = [
            { $match: query },
            {
                $unwind: {
                    path: "$customPackageItems",
                    preserveNullAndEmptyArrays: false,
                },
            },
            {
                $match: {
                    $or: [
                        {
                            "customPackageItems.isReturned": false,
                        },
                        {
                            "customPackageItems.isReturned": {
                                $exists: false,
                            },
                        },
                    ],
                },
            },
            {
                $project: {
                    _id: 0,
                    packageId: "$customPackageItems.customPackageId",
                    purchaseId: "$customPackageItems._id",
                    invoiceId: "$_id",
                    packageName: "$customPackageItems.packageName",
                    paymentStatus: 1,
                    packageStartDate: null,
                    packageEndDate: null,
                    unitPrice: "$customPackageItems.unitPrice",
                    quantity: "$customPackageItems.quantity",
                    discountExcludeCart: {
                        $cond: [{ $gt: ["$customPackageItems.quantity", 0] }, { $divide: ["$customPackageItems.discountExcludeCart", "$customPackageItems.quantity"] }, 0],
                    },
                    discountIncludeCart: {
                        $cond: [{ $gt: ["$customPackageItems.quantity", 0] }, { $divide: ["$customPackageItems.discountIncludeCart", "$customPackageItems.quantity"] }, 0],
                    },
                    updatedAt: 1,
                },
            },
        ];

        if (search && search.trim().length > 0) {
            pipeline.push({
                $match: {
                    packageName: { $regex: search.trim(), $options: "i" },
                },
            });
        }

        pipeline.push(
            {
                $facet: {
                    total: [{ $count: "count" }],
                    data: [{ $sort: { updatedAt: -1 } }, { $skip: skip }, { $limit: pageSize }],
                },
            },
            {
                $project: {
                    total: { $ifNull: [{ $arrayElemAt: ["$total.count", 0] }, 0] },
                    data: 1,
                },
            },
        );

        const result = await this.InvoiceModel.aggregate(pipeline);
        const data = await Promise.all(
            (result?.length && result[0].data ? result[0].data : []).map(async (item) => {
                const refundAmount = item.paymentStatus === PaymentStatus.COMPLETED ? item.unitPrice - (item.discountExcludeCart + item.discountIncludeCart) : 0;

                return {
                    ...item,
                    refundAmount: refundAmount,
                };
            }),
        );
        return {
            data: data,
            count: result.length ? result[0].total : 0,
        };
    }

    async updatePurchaseSession(body: any, user: any): Promise<any> {
        const { purchaseId, addedSessions, startDate, endDate, notes } = body;

        const purchase = await this.PurchaseModel.findById(purchaseId);
        if (!purchase) throw new NotFoundException("Purchase not found");

        const pricingData = await this.PricingModel.findById(purchase.packageId).lean();
        if (!pricingData) throw new NotFoundException("Package not found");

        const newStartDate = moment(startDate).utc(true).startOf("day").toDate();
        let newEndDate: Date;

        const previousValues: any = {
            startDate: purchase.startDate,
            endDate: purchase.endDate,
            totalSessions: purchase.sessionType === SessionType.DAY_PASS ? purchase.dayPassLimit : purchase.totalSessions,
        };
        const updatedFields: any = { startDate: newStartDate, totalSessions: purchase.sessionType === SessionType.DAY_PASS ? purchase.dayPassLimit : purchase.totalSessions };

        if (endDate) {
            newEndDate = moment(endDate).utc(true).endOf("day").toDate();
            if (newStartDate > newEndDate) throw new BadRequestException("Start date cannot be after end date");
            updatedFields.endDate = newEndDate;
        } else {
            const { durationUnit, expiredInDays: expireIn } = pricingData;
            if (!durationUnit || !expireIn) throw new BadRequestException("Missing duration configuration in package");

            newEndDate = moment(newStartDate)
                .add(expireIn, durationUnit.toLowerCase() as moment.unitOfTime.DurationConstructor)
                .toDate();
            updatedFields.endDate = newEndDate;
        }

        let minDate: Date | null = null;
        let maxDate: Date | null = null;

        if ([ClassType.COURSES, ClassType.CLASSES].includes(pricingData?.services?.type)) {
            const [range] = await this.EnrollmentModel.aggregate([
                {
                    $match: {
                        purchaseId: new Types.ObjectId(purchaseId),
                        userId: new Types.ObjectId(purchase.userId),
                    },
                },
                {
                    $lookup: {
                        from: "schedulings",
                        localField: "schedulingId",
                        foreignField: "_id",
                        as: "scheduleInfo",
                    },
                },
                { $unwind: "$scheduleInfo" },
                {
                    $group: {
                        _id: null,
                        minDate: { $min: "$scheduleInfo.date" },
                        maxDate: { $max: "$scheduleInfo.date" },
                    },
                },
            ]);

            if (range) {
                minDate = range.minDate;
                maxDate = range.maxDate;
            }
        } else {
            const [range] = await this.SchedulingModel.aggregate([
                {
                    $match: {
                        purchaseId: new Types.ObjectId(purchaseId),
                        clientId: new Types.ObjectId(purchase.userId),
                        scheduleStatus: { $nin: [ScheduleStatusType.CANCELED] },
                    },
                },
                {
                    $group: {
                        _id: null,
                        minDate: { $min: "$date" },
                        maxDate: { $max: "$date" },
                    },
                },
            ]);

            if (range) {
                minDate = range.minDate;
                maxDate = range.maxDate;
            }
        }

        if (minDate && moment(newStartDate).isAfter(maxDate)) throw new BadRequestException("Start date cannot be after the latest scheduled session");
        if (maxDate && moment(newEndDate).isBefore(minDate)) throw new BadRequestException("End date cannot be before the earliest scheduled session");

        purchase.startDate = newStartDate;
        purchase.endDate = newEndDate;

        if (addedSessions !== null && addedSessions !== undefined) {
            const sessionType = purchase.sessionType;
            if ([SessionType.SINGLE, SessionType.UNLIMITED].includes(sessionType)) {
                throw new BadRequestException(`${sessionType} does not support session modification`);
            }
            const consumed = purchase.sessionConsumed;
            const totalSessions = (sessionType === SessionType.DAY_PASS ? purchase.dayPassLimit : purchase.totalSessions) + addedSessions;

            if (totalSessions < 1) {
                throw new BadRequestException(`Total sessions must be at least 1 for ${sessionType}`);
            }

            switch (sessionType) {
                case SessionType.MULTIPLE:
                    if (totalSessions < consumed) {
                        throw new BadRequestException("Total sessions cannot be less than consumed sessions");
                    }
                    updatedFields.totalSessions = totalSessions;
                    purchase.totalSessions = totalSessions;
                    break;

                case SessionType.DAY_PASS:
                    const sessionsCount = await this.SchedulingModel.aggregate([
                        {
                            $match: {
                                purchaseId: new Types.ObjectId(purchaseId),
                                clientId: new Types.ObjectId(purchase.userId),
                                scheduleStatus: { $nin: [ScheduleStatusType.CANCELED] },
                            },
                        },
                        { $group: { _id: "$date" } },
                    ]);

                    const consumedDayPass = sessionsCount.length;
                    if (totalSessions < consumedDayPass) {
                        throw new BadRequestException(`Total sessions cannot be less than consumed sessions for ${sessionType}`);
                    }

                    updatedFields.totalSessions = totalSessions;
                    purchase.totalSessions = Number.POSITIVE_INFINITY;
                    purchase.dayPassLimit = totalSessions;
                    break;
            }
        }

        await purchase.save();

        const normalizeValue = (val: any) => (val === Number.POSITIVE_INFINITY ? "unlimited" : val);

        const logsData = await this.PricingSessionLogsModel.create({
            userId: purchase.userId,
            organizationId: purchase.organizationId,
            facilityId: purchase.facilityId,
            purchaseId: purchase._id,
            packageId: purchase.packageId,
            modifiedBy: user._id,
            sessionType: purchase.sessionType,
            startDate: purchase.startDate,
            endDate: purchase.endDate,
            totalSessions: purchase.totalSessions,
            sessionConsumed: purchase.sessionConsumed,
            notes: notes || "",
            previousValues: {
                ...previousValues,
                totalSessions: normalizeValue(previousValues.totalSessions),
            },
            updatedValues: {
                ...updatedFields,
                totalSessions: normalizeValue(updatedFields.totalSessions),
            },
        });

        return logsData;
    }

    async getSessionLogsByPurchaseId(purchaseId: string, user: any, pagination: PaginationDto): Promise<{ totalCount: number; logs: any[] }> {
        const { page = 1, pageSize = 10 } = pagination;
        const skip = (page - 1) * pageSize;

        const query: any = {
            purchaseId: new Types.ObjectId(purchaseId),
        };

        if (user.role === ENUM_ROLE_TYPE.ORGANIZATION) {
            query.organizationId = new Types.ObjectId(user._id);
        } else if ([ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.WEB_MASTER].includes(user.role)) {
            const staffDetails = await this.StaffDetailsModel.findOne({ userId: user._id }, { organizationId: 1, facilityId: 1 }).lean();
            if (!staffDetails) throw new BadRequestException("Staff details not found");
            query.organizationId = new Types.ObjectId(staffDetails.organizationId);
            if (staffDetails.facilityId) {
                query.facilityId = { $in: (Array.isArray(staffDetails.facilityId) ? staffDetails.facilityId : [staffDetails.facilityId]).map((id) => new Types.ObjectId(id)) };
            }
        } else if (user.role === ENUM_ROLE_TYPE.USER) {
            query.userId = new Types.ObjectId(user._id);
        } else {
            throw new BadRequestException("Access Denied");
        }

        const pipeline: any[] = [
            { $match: query },
            {
                $lookup: {
                    from: "users",
                    localField: "modifiedBy",
                    foreignField: "_id",
                    as: "userDetails",
                },
            },
            { $unwind: { path: "$userDetails", preserveNullAndEmptyArrays: true } },
            {
                $project: {
                    _id: 1,
                    userId: 1,
                    originizationId: 1,
                    facilityId: 1,
                    purchaseId: 1,
                    packageId: 1,
                    sessionType: 1,
                    notes: 1,
                    modifiedBy: 1,
                    modifiedByName: "$userDetails.name",
                    previousValues: 1,
                    updatedValues: 1,
                    createdAt: 1,
                    updatedAt: 1,
                },
            },
            { $sort: { createdAt: -1 } },
            {
                $facet: {
                    logs: [{ $skip: skip }, { $limit: pageSize }],
                    total: [{ $count: "count" }],
                },
            },
            {
                $project: {
                    logs: 1,
                    totalCount: { $ifNull: [{ $arrayElemAt: ["$total.count", 0] }, 0] },
                },
            },
        ];

        const result = await this.PricingSessionLogsModel.aggregate(pipeline);
        return result[0] || { totalCount: 0, logs: [] };
    }

    async sharePackage(organizationId: IDatabaseObjectId, body: SharePackageDto): Promise<any> {
        const { purchaseId, shareTo } = body;
        const purchase = await this.PurchaseModel.findOne({
            _id: purchaseId,
            userId: body.shareFrom,
            organizationId,
            isActive: true,
            isExpired: false,
            endDate: { $gte: new Date() },
        });

        if (!purchase) {
            throw new NotFoundException("Purchase not found or is not active");
        }
        const pricing = await this.PricingModel.findOne({ _id: purchase.packageId });

        if (pricing?.services?.type === ClassType.COURSES) {
            throw new BadRequestException("Cannot share course this package");
        }

        const removedFromClient = purchase.consumers.filter((item) => !shareTo.includes(item.toString()));
        purchase.consumers = shareTo;
        purchase.removedConsumers = [removedFromClient, purchase.removedConsumers || []].flat();
        await purchase.save();
        return true;
    }

    async familyShareClientList(filter: Record<string, any>, _limit: number, _offset: number, _order: IPaginationOrder): Promise<any> {
        const purchase = await this.PurchaseModel.findOne(filter, { consumers: 1 });
        if (!purchase) {
            throw new NotFoundException("Purchase not found or is not active");
        }
        const userFindPipe: PipelineStage[] = [
            // 1. Start with the specific user
            {
                $match: {
                    _id: new Types.ObjectId(filter.userId),
                },
            },

            // 2. Store the original user document for later use
            {
                $addFields: { self: "$$ROOT" },
            },

            // 3. If user is a parent (root), find all descendants
            {
                $graphLookup: {
                    from: "users",
                    startWith: "$_id",
                    connectFromField: "_id",
                    connectToField: "parent",
                    as: "descendants",
                },
            },

            // 4. If user is a child, find all ancestors
            {
                $graphLookup: {
                    from: "users",
                    startWith: "$parent",
                    connectFromField: "parent",
                    connectToField: "_id",
                    as: "ancestors",
                },
            },

            // 5. Find siblings (users with the same parent, excluding self)
            {
                $lookup: {
                    from: "users",
                    let: { parentId: "$parent", selfId: "$_id" },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [{ $eq: ["$parent", "$$parentId"] }, { $ne: ["$_id", "$$selfId"] }],
                                },
                            },
                        },
                    ],
                    as: "siblings",
                },
            },

            // 6. Build "family" array conditionally
            {
                $addFields: {
                    family: {
                        $cond: [
                            {
                                $or: [{ $eq: [{ $type: "$parent" }, "missing"] }, { $eq: ["$parent", null] }],
                            },
                            { $concatArrays: [["$self"], "$descendants"] }, // parent user
                            { $concatArrays: [["$self"], "$ancestors", "$siblings"] }, // child user
                        ],
                    },
                },
            },

            // 7. Flatten the family array into individual documents
            { $unwind: "$family" },

            // 8. Replace the root with each family member
            { $replaceRoot: { newRoot: "$family" } },

            // 9. Filter only active users
            { $match: { isActive: true } },

            // 10. Final formatting: pagination and sorting
            {
                $facet: {
                    total: [{ $count: "count" }],
                    data: [{ $sort: { name: 1 } }, { $skip: filter.skip || 0 }, { $limit: filter.limit || 10 }],
                },
            },
        ];

        const users = await this.UserModel.aggregate(userFindPipe);
        const data = (users[0]?.data || []).map((user) => {
            return {
                _id: user._id,
                firstName: user.firstName,
                lastName: user.lastName,
                email: user.email,
                mobile: user.mobile,
                countryCode: user.countryCode,
                isOwner: user._id.toString() === filter.userId.toString(),
                isDefault: user._id.toString() === filter.userId.toString(),
                isShared: !!purchase?.consumers?.find((item) => item.toString() === user._id.toString()),
                isActive: user.isActive,
                createdAt: user.createdAt,
                updatedAt: user.updatedAt,
            };
        });

        return {
            data: data,
            total: users[0]?.total[0]?.count || 0,
        };
    }

    async sendMailForExpiringMemberships() {
        const orgs = await this.OrganizationsModel.find(
            { "renewalReminder.enabled": true },
            { _id: 1, "renewalReminder.daysBefore": 1, "renewalReminder.daysAfter": 1, userId: 1 },
        ).lean();

        if (!orgs?.length) return;

        // Map of organization reminders
        const reminderMap = new Map<string, { daysBefore: number[]; daysAfter: number[] }>();
        for (const org of orgs) {
            reminderMap.set(org?.userId?.toString(), {
                daysBefore: org?.renewalReminder?.daysBefore || [],
                daysAfter: org?.renewalReminder?.daysAfter || [],
            });
        }

        // Fetch role IDs
        const roleIds = await this.roleSchema.find({});
        const getRoleId = (type) => new Types.ObjectId(roleIds?.find((data) => data?.type === type)?._id);

        const orgID = getRoleId(ENUM_ROLE_TYPE.ORGANIZATION);
        const frontDeskId = getRoleId(ENUM_ROLE_TYPE.FRONT_DESK_ADMIN);
        const webMasterId = getRoleId(ENUM_ROLE_TYPE.WEB_MASTER);
        const trainerId = getRoleId(ENUM_ROLE_TYPE.TRAINER);

        if (!orgID || !frontDeskId || !webMasterId || !trainerId) {
            throw new Error("Required role IDs not found");
        }

        const now = new Date();
        const todayStart = new Date(now.setHours(0, 0, 0, 0));
        const todayEnd = new Date(now.setHours(23, 59, 59, 999));

        const maxDaysBefore = 7;
        const maxDaysAfter = 3;

        const minDate = new Date(todayStart);
        minDate.setDate(todayStart.getDate() - maxDaysAfter);
        const maxDate = new Date(todayEnd);
        maxDate.setDate(todayEnd.getDate() + maxDaysBefore);

        const orgIds = orgs?.map((o) => new Types.ObjectId(o?.userId));
        if (!orgIds?.length) return;

        // Membership query
        const query = {
            organizationId: { $in: orgIds },
            membershipId: { $exists: true, $ne: null },
            isActive: true,
            isExchanged: { $ne: true },
            paymentStatus: { $in: [PaymentStatus.COMPLETED, PaymentStatus.PENDING] },
            endDate: { $gte: minDate, $lte: maxDate },
        };

        // Aggregation pipeline
        const pipeline = [
            { $match: query },
            { $lookup: { from: "facilities", localField: "facilityId", foreignField: "_id", as: "facility" } },
            { $unwind: "$facility" },
            { $addFields: { facilityName: "$facility.facilityName" } },
            {
                $lookup: {
                    from: "users",
                    localField: "userId",
                    foreignField: "_id",
                    pipeline: [{ $project: { salt: 0, createdAt: 0, updatedAt: 0, password: 0, countryCode: 0, __v: 0, newUser: 0 } }],
                    as: "clientDetails",
                },
            },
            { $unwind: "$clientDetails" },
            {
                $lookup: {
                    from: "users",
                    let: { orgId: "$organizationId" },
                    pipeline: [
                        { $match: { $expr: { $and: [{ $eq: ["$_id", "$$orgId"] }, { $eq: ["$role", orgID] }] } } },
                        { $project: { salt: 0, createdAt: 0, updatedAt: 0, password: 0, countryCode: 0, __v: 0, newUser: 0 } },
                    ],
                    as: "orgOwner",
                },
            },
            { $unwind: "$orgOwner" },
            {
                $lookup: {
                    from: "staffprofiledetails",
                    let: { orgId: "$organizationId", facilityId: "$facilityId" },
                    pipeline: [
                        { $match: { $expr: { $and: [{ $eq: ["$organizationId", "$$orgId"] }, { $in: ["$$facilityId", "$facilityId"] }] } } },
                        {
                            $lookup: {
                                from: "users",
                                localField: "userId",
                                foreignField: "_id",
                                pipeline: [
                                    { $match: { $expr: { $in: ["$role", [frontDeskId, webMasterId, trainerId]] } } },
                                    { $project: { firstName: 1, lastName: 1, mobile: 1, email: 1, isActive: 1, role: 1, name: 1 } },
                                ],
                                as: "staffUser",
                            },
                        },
                        { $unwind: "$staffUser" },
                        { $replaceRoot: { newRoot: "$staffUser" } },
                    ],
                    as: "staffMembers",
                },
            },
            {
                $lookup: {
                    from: "pricings",
                    localField: "packageId",
                    foreignField: "_id",
                    pipeline: [{ $project: { name: 1, "services.type": 1 } }],
                    as: "pricingDetails",
                },
            },
            { $unwind: { path: "$pricingDetails", preserveNullAndEmptyArrays: true } },
            {
                $project: {
                    itemType: 0,
                    invoiceId: 0,
                    purchasedBy: 0,
                    purchaseDate: 0,
                    paymentStatus: 0,
                    isExpired: 0,
                    sessionType: 0,
                    totalSessions: 0,
                    sessionConsumed: 0,
                    sessionPerDay: 0,
                    createdAt: 0,
                    updatedAt: 0,
                    facility: 0,
                },
            },
        ];

        const results = await this.PurchaseModel.aggregate(pipeline);

        // Helper: send mail + WhatsApp
        const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

        // Process memberships in batches
        const processInBatches = async (memberships: any[], batchSize: number, delayBetweenBatches: number) => {
            for (let i = 0; i < memberships.length; i += batchSize) {
                const batch = memberships.slice(i, i + batchSize);
                console.log(`Processing batch ${Math.floor(i / batchSize) + 1} of ${Math.ceil(memberships.length / batchSize)}`);

                const batchPromises = batch.map(async (membership, index) => {
                    if (index > 0) {
                        await delay(200);
                    }

                    try {
                        await this.processSingleMembership(membership, reminderMap);
                    } catch (err) {
                        console.error("Error processing membership:", membership?._id?.toString(), err);
                    }
                });

                await Promise.allSettled(batchPromises);

                if (i + batchSize < memberships.length) {
                    console.log(`Waiting ${delayBetweenBatches}ms before next batch...`);
                    await delay(delayBetweenBatches);
                }
            }
        };

        await processInBatches(results, 3, 2000); // 3 concurrent, 2sec between batches
    }

    async sendNotification(user: any, messageTemplateName: string, components: Record<string, string>, subject: string, templateName: string, userMailData: object): Promise<any> {
        if (!user) return;
        const formattedMobile = user.mobile
            ? (() => {
                const digits = user.mobile.replace(/\D/g, "").slice(-10);
                return /^\d{10}$/.test(digits) ? `91${digits}` : "";
            })()
            : "";

        const tasks: Promise<any>[] = [];

        if (formattedMobile) {
            tasks.push(
                this.msg91Service.sendTemplateMessage([formattedMobile], messageTemplateName, components).catch((err) => {
                    console.error(`WhatsApp failed for ${formattedMobile}:`, err);
                }),
            );
        }
        if (user?.email) {
            tasks.push(
                Promise.resolve(
                    this.mailService.sendMail({
                        to: user.email,
                        subject,
                        template: templateName,
                        context: userMailData,
                    }),
                ).catch((err) => console.error(`Mail failed for ${user.email}:`, err)),
            );
        }

        await Promise.allSettled(tasks);
    };

    private async processSingleMembership(membership: any, reminderMap: any): Promise<void> {
        try {
            const endDate = new Date(membership?.endDate);
            const now = new Date();
            const diff = (endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24);

            let isToday = false;
            let isAfter = false;
            let daysLeft = diff > 0 ? Math.ceil(diff) : Math.floor(diff);

            const todayStart = new Date(now.setHours(0, 0, 0, 0));
            const todayEnd = new Date(now.setHours(23, 59, 59, 999));

            if (membership?.endDate >= todayStart && membership?.endDate <= todayEnd) {
                isToday = true;
            } else if (daysLeft < 0) {
                isAfter = true;
                daysLeft = -daysLeft;
            }

            const orgId = membership?.orgOwner?._id?.toString();
            if (!orgId) return;

            const reminderRule = reminderMap.get(orgId);
            if (!reminderRule) return;

            const validDays = isAfter ? reminderRule?.daysAfter : reminderRule?.daysBefore;
            if (!isToday && !validDays?.includes(daysLeft)) return;

            const clientDetails = membership?.clientDetails;

            let formattedMobile = "";
            if (clientDetails?.mobile) {
                const digits = clientDetails.mobile.replace(/\D/g, "");
                const tenDigit = digits.slice(-10);
                formattedMobile = /^\d{10}$/.test(tenDigit) ? `91${tenDigit}` : "";
            }

            const userMailData = {
                userName: `${clientDetails?.firstName ?? ""} ${clientDetails?.lastName ?? ""}`.trim(),
                organizationName: membership?.orgOwner?.name ?? "",
                facilityName: membership?.facilityName ?? "",
                packageName: membership?.pricingDetails?.name ?? "",
                expiryDate: moment(endDate).tz("Asia/Kolkata").format("DD MMM YYYY"),
                daysLeft,
                renewalLink: `${process.env.ADMIN_FRONTEND_APP_URL}/pricing`,
                status: "",
            };

            let subject = "";
            let templateName = "";
            let messageTemplateName = "";
            let components: Record<string, any> = {};

            if (isToday) {
                subject = `Urgent: Your ${userMailData.packageName} Expires Today`;
                templateName = "notifyClient-expiring-today";
                messageTemplateName = "expiring_today_production";
                userMailData.status = "Expiring Today";
                components = {
                    header_1: { type: "text", value: "Package" },
                    body_1: { type: "text", value: userMailData.userName },
                    body_2: { type: "text", value: userMailData.packageName },
                    body_3: { type: "text", value: userMailData.organizationName },
                    body_4: { type: "text", value: userMailData.facilityName },
                };
            } else if (!isAfter) {
                subject = `Don't Let Your ${userMailData.packageName} Expire – Renew Today!`;
                templateName = "notifyClient-before-expiry";
                messageTemplateName = "before_expiry_production";
                userMailData.status = `${daysLeft} Days Left`;
                components = {
                    header_1: { type: "text", value: "Package" },
                    body_1: { type: "text", value: userMailData.userName },
                    body_2: { type: "text", value: userMailData.packageName },
                    body_3: { type: "text", value: userMailData.organizationName },
                    body_4: { type: "text", value: userMailData.facilityName },
                    body_5: { type: "text", value: userMailData.expiryDate },
                    body_6: { type: "text", value: userMailData.daysLeft.toString() },
                };
            } else {
                subject = `We'd Love to Have You Back at ${userMailData.organizationName}`;
                templateName = "notifyClient-after-expiry";
                messageTemplateName = "after_expiry_production";
                userMailData.status = `Expired ${daysLeft} Days Ago`;
                components = {
                    header_1: { type: "text", value: userMailData.organizationName },
                    body_1: { type: "text", value: userMailData.userName },
                    body_2: { type: "text", value: userMailData.packageName },
                    body_3: { type: "text", value: userMailData.organizationName },
                    body_4: { type: "text", value: userMailData.facilityName },
                };
            }

            await this.sendNotification(
                clientDetails,
                messageTemplateName,
                components,
                subject,
                templateName,
                userMailData,
            );

            let staffComponents: Record<string, any> = {
                header_1: { type: "text", value: userMailData.userName },
                body_1: { type: "text", value: userMailData.organizationName },
                body_2: { type: "text", value: userMailData.organizationName },
                body_3: { type: "text", value: userMailData.facilityName },
                body_4: { type: "text", value: userMailData.userName },
                body_5: { type: "text", value: userMailData.packageName },
                body_6: { type: "text", value: userMailData.expiryDate },
                body_7: { type: "text", value: userMailData.status },
            };

            await this.sendNotification(
                membership.orgOwner,
                "notify_staff_membership_expiry",
                staffComponents,
                `Membership Expiry Alert – ${userMailData.userName} – ${userMailData.packageName}`,
                "notifyStaff-expiry",
                userMailData,
            );

            if (Array.isArray(membership.staffMembers)) {
                for (const staff of membership.staffMembers) {
                    await this.sendNotification(
                        staff,
                        "notify_staff_membership_expiry",
                        staffComponents,
                        `Membership Expiry Alert – ${userMailData.userName} – ${userMailData.packageName}`,
                        "notifyStaff-expiry",
                        userMailData,
                    );
                }
            }
        } catch (err) {
            console.error("Error in processSingleMembership:", membership?._id?.toString(), err);
            throw err;
        }
    }

    async salesByRevenueCategory(user: IUserDocument, body: ExportSalesReportDto, userTimezone: string) {
        const facilityDetails = await this.getOrganizationandFacility(user);
        const { facilityIds, startDate, endDate } = body;

        const filter: any = {
            organizationId: new Types.ObjectId(facilityDetails.organizationId),
            paymentStatus: { $in: [PaymentStatus.COMPLETED, PaymentStatus.PENDING] },
        };

        if (Array.isArray(facilityIds) && facilityIds.length > 0) {
            filter.facilityId = { $in: facilityIds.map((_id) => new Types.ObjectId(_id)) };
        } else if (user.role.type !== ENUM_ROLE_TYPE.ORGANIZATION && facilityDetails.facilityId) {
            const facilityArray = Array.isArray(facilityDetails.facilityId) ? facilityDetails.facilityId : [facilityDetails.facilityId];
            filter.facilityId = { $in: facilityArray.map((id) => new Types.ObjectId(id)) };
        }
        if (startDate && endDate) {
            filter.invoiceDate = {
                $gte: new Date(new Date(startDate).setHours(0, 0, 0, 0)),
                $lte: new Date(new Date(endDate).setHours(23, 59, 59, 999)),
            };
        }

        const orgData = await this.OrganizationsModel.findOne({ userId: facilityDetails.organizationId }, "isInclusiveofGst").lean();
        const isInclusiveofGst = Boolean(orgData?.isInclusiveofGst);
        let pipeline: any = [
            { $match: filter },
            {
                $project: {
                    allItems: {
                        $concatArrays: [
                            {
                                $ifNull: ["$purchaseItems", []]
                            },
                            {
                                $ifNull: ["$productItem", []]
                            },
                            {
                                $ifNull: ["$customPackageItems", []]
                            },
                            {
                                $ifNull: ["$returnItems", []]
                            }
                        ]
                    },
                    organizationId: 1,
                    voucherDiscount: 1
                }
            },
            {
                $unwind: "$allItems"
            },
            {
                $addFields: {
                    itemType: {
                        $cond: [
                            {
                                $ifNull: [
                                    "$allItems.invoiceId",
                                    false
                                ]
                            },
                            "return",
                            {
                                $cond: [
                                    {
                                        $ifNull: [
                                            "$allItems.productId",
                                            false
                                        ]
                                    },
                                    "product",
                                    "service"
                                ]
                            }
                        ]
                    },
                    price: {
                        $toDouble: {
                            $cond: [
                                {
                                    $ifNull: [
                                        "$allItems.invoiceId",
                                        false
                                    ]
                                },
                                {
                                    $cond: [
                                        {
                                            $eq: [isInclusiveofGst, true]
                                        },
                                        {
                                            $ifNull: [
                                                "$allItems.totalPrice",
                                                0
                                            ]
                                        },
                                        {
                                            $ifNull: [
                                                "$allItems.basePrice",
                                                0
                                            ]
                                        }
                                    ]
                                },
                                {
                                    $ifNull: [
                                        "$allItems.price",
                                        {
                                            $cond: [
                                                {
                                                    $ifNull: [
                                                        "$allItems.productId",
                                                        false
                                                    ]
                                                },
                                                "$allItems.salePrice",
                                                "$allItems.unitPrice"
                                            ]
                                        }
                                    ]
                                }
                            ]
                        }
                    },
                    quantity: {
                        $toInt: "$allItems.quantity"
                    },
                    discountExcludeCart: {
                        $toDouble: {
                            $ifNull: [
                                "$allItems.discountExcludeCart",
                                0
                            ]
                        }
                    },
                    discountIncludeCart: {
                        $toDouble: {
                            $ifNull: [
                                "$allItems.discountIncludeCart",
                                0
                            ]
                        }
                    },
                    gstAmount: {
                        $toDouble: "$allItems.gstAmount"
                    },
                    finalAmountUnit: {
                        $toDouble: {
                            $ifNull: ["$allItems.totalPrice", 0]
                        }
                    },
                    packageId: "$allItems.packageId",
                    productId: "$allItems.productId"
                }
            },
            {
                $addFields: {
                    subtotal: {
                        $multiply: ["$price", "$quantity"]
                    },
                    totalDiscount: {
                        $add: [
                            "$discountExcludeCart",
                            "$discountIncludeCart"
                        ]
                    },
                    totalAmountAfterDiscount: {
                        $subtract: [
                            {
                                $multiply: ["$price", "$quantity"]
                            },
                            {
                                $add: [
                                    "$discountExcludeCart",
                                    "$discountIncludeCart"
                                ]
                            }
                        ]
                    }
                }
            },
            {
                $addFields: {
                    totalAmountAfterGst: {
                        $cond: [
                            {
                                $eq: ["$itemType", "return"]
                            },
                            "$finalAmountUnit",
                            {
                                $cond: [
                                    {
                                        $eq: [isInclusiveofGst, false]
                                    },
                                    {
                                        $add: [
                                            "$totalAmountAfterDiscount",
                                            "$gstAmount"
                                        ]
                                    },
                                    "$totalAmountAfterDiscount"
                                ]
                            }
                        ]
                    }
                }
            },
            {
                $lookup: {
                    from: "pricings",
                    localField: "packageId",
                    foreignField: "_id",
                    as: "pricing"
                }
            },
            {
                $unwind: {
                    path: "$pricing",
                    preserveNullAndEmptyArrays: true
                }
            },
            {
                $lookup: {
                    from: "products",
                    localField: "productId",
                    foreignField: "_id",
                    as: "product"
                }
            },
            {
                $unwind: {
                    path: "$product",
                    preserveNullAndEmptyArrays: true
                }
            },
            {
                $addFields: {
                    revenueCategoryId: {
                        $switch: {
                            branches: [
                                {
                                    case: {
                                        $eq: ["$itemType", "service"]
                                    },
                                    then: "$pricing.revenueCategory"
                                },
                                {
                                    case: {
                                        $eq: ["$itemType", "product"]
                                    },
                                    then: "$product.revenueCategory"
                                }
                            ],
                            default: null
                        }
                    }
                }
            },
            {
                $lookup: {
                    from: "organizations",
                    let: {
                        orgId: "$organizationId",
                        rcId: "$revenueCategoryId"
                    },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $eq: ["$userId", "$$orgId"]
                                }
                            }
                        },
                        {
                            $unwind: "$revenueCategory"
                        },
                        {
                            $match: {
                                $expr: {
                                    $eq: [
                                        "$revenueCategory._id",
                                        "$$rcId"
                                    ]
                                }
                            }
                        },
                        {
                            $project: {
                                _id: "$revenueCategory._id",
                                name: "$revenueCategory.name"
                            }
                        }
                    ],
                    as: "rc"
                }
            },
            {
                $unwind: {
                    path: "$rc",
                    preserveNullAndEmptyArrays: true
                }
            },
            {
                $addFields: {
                    revenueCategoryId: {
                        $ifNull: [
                            "$rc._id",
                            "000000000000000000000000"
                        ]
                    },
                    revenueCategoryName: {
                        $ifNull: ["$rc.name", "Other"]
                    }
                }
            },
            {
                $group: {
                    _id: "$revenueCategoryId",
                    revenueCategoryName: {
                        $first: "$revenueCategoryName"
                    },
                    // Subtotal / discount / amount-after-discount / gst sums should reflect only non-return items
                    totalSubtotal: {
                        $sum: {
                            $cond: [
                                {
                                    $eq: ["$itemType", "return"]
                                },
                                0,
                                "$subtotal"
                            ]
                        }
                    },
                    totalDiscount: {
                        $sum: {
                            $cond: [
                                {
                                    $eq: ["$itemType", "return"]
                                },
                                0,
                                "$totalDiscount"
                            ]
                        }
                    },
                    totalAmountAfterDiscount: {
                        $sum: {
                            $cond: [
                                {
                                    $eq: ["$itemType", "return"]
                                },
                                0,
                                "$totalAmountAfterDiscount"
                            ]
                        }
                    },
                    totalGstAmount: {
                        $sum: {
                            $cond: [
                                {
                                    $eq: ["$itemType", "return"]
                                },
                                0,
                                "$gstAmount"
                            ]
                        }
                    },
                    // Sum of totalAmountAfterGst for NON-RETURN items
                    totalAmountAfterGst_nonReturn: {
                        $sum: {
                            $cond: [
                                {
                                    $eq: ["$itemType", "return"]
                                },
                                0,
                                "$totalAmountAfterGst"
                            ]
                        }
                    },
                    // Sum of totalAmountAfterGst for RETURN items (this is the "Return Amount With GST" to display and subtract)
                    totalReturnAmountWithGst: {
                        $sum: {
                            $cond: [
                                {
                                    $eq: ["$itemType", "return"]
                                },
                                "$totalAmountAfterGst",
                                0
                            ]
                        }
                    }
                }
            },
            // Compute final amount per category by subtracting returns from non-return sum
            {
                $addFields: {
                    finalAmountInclTax: {
                        $subtract: [
                            "$totalAmountAfterGst_nonReturn",
                            "$totalReturnAmountWithGst"
                        ]
                    }
                }
            },
            // Group again to compute grandTotalNetRevenue but exclude categories with finalAmountInclTax <= 0
            {
                $group: {
                    _id: null,
                    categories: { $push: "$$ROOT" },
                    grandTotalNetRevenue: {
                        $sum: {
                            $cond: [
                                { $gt: ["$finalAmountInclTax", 0] },
                                "$finalAmountInclTax",
                                0
                            ]
                        }
                    }
                }
            },
            {
                $unwind: "$categories"
            },
            // Compute revenue percent ONLY if finalAmountInclTax > 0
            {
                $addFields: {
                    "categories.revenuePercent": {
                        $cond: [
                            {
                                $and: [
                                    {
                                        $gt: [
                                            "$categories.finalAmountInclTax",
                                            0
                                        ]
                                    },
                                    {
                                        $gt: ["$grandTotalNetRevenue", 0]
                                    }
                                ]
                            },
                            {
                                $round: [
                                    {
                                        $multiply: [
                                            {
                                                $divide: [
                                                    "$categories.finalAmountInclTax",
                                                    "$grandTotalNetRevenue"
                                                ]
                                            },
                                            100
                                        ]
                                    },
                                    2
                                ]
                            },
                            0
                        ]
                    }
                }
            },
            {
                $replaceRoot: { newRoot: "$categories" }
            },
            {
                $addFields: {
                    sortOrder: {
                        $cond: [
                            {
                                $eq: ["$revenueCategoryName", "Other"]
                            },
                            1,
                            0
                        ]
                    }
                }
            },
            {
                $project: {
                    revenueCategoryName: 1,
                    sortOrder: 1,
                    totalSubtotal: {
                        $round: ["$totalSubtotal", 2]
                    },
                    totalDiscount: {
                        $round: ["$totalDiscount", 2]
                    },

                    // New field: Amount After Discount (Excl. Tax) = subtotal - discount
                    amountAfterDiscountExclTax: {
                        $round: [
                            {
                                $subtract: [
                                    "$totalSubtotal",
                                    "$totalDiscount"
                                ]
                            },
                            2
                        ]
                    },

                    amountAfterDiscountInclTax: {
                        $round: [
                            "$totalAmountAfterGst_nonReturn",
                            2
                        ]
                    },
                    totalGstAmount: {
                        $round: ["$totalGstAmount", 2]
                    },
                    totalReturnAmountWithGst: {
                        $round: ["$totalReturnAmountWithGst", 2]
                    },
                    finalAmountInclTax: {
                        $round: ["$finalAmountInclTax", 2]
                    },
                    revenuePercent: 1
                }
            },
            {
                $sort: {
                    sortOrder: 1,
                    revenueCategoryName: 1
                }
            }
        ]
        const invoiceDetails = await this.InvoiceModel.aggregate(pipeline).exec();
        // ---- CSV ----
        const columns = isInclusiveofGst
            ? `Revenue Category,Subtotal (Incl. Tax) (INR),Discount (INR),Tax (INR),Total Amount After Tax (INR),Return Amount (INR),Final Amount (Incl. Tax) (INR),% of Revenue\n`
            : `Revenue Category,Subtotal (Excl. Tax) (INR),Discount (INR),Tax (INR),Total Amount After Tax (INR),Return Amount (INR),Final Amount (Incl. Tax) (INR),% of Revenue\n`;
        let data =
            columns +
            (invoiceDetails || [])
                .map((item: any) =>
                    [
                        item?.revenueCategoryName || "",
                        item?.totalSubtotal ?? 0,
                        item?.totalDiscount ? - item.totalDiscount : 0,
                        //isInclusiveofGst?item?.amountAfterDiscountInclTax:item?.amountAfterDiscountExclTax ,
                        item?.totalGstAmount ?? 0,
                        item?.amountAfterDiscountInclTax ?? 0,
                        item?.totalReturnAmountWithGst ? -item.totalReturnAmountWithGst : 0,
                        item?.finalAmountInclTax ?? 0,
                        item?.revenuePercent ?? 0,
                    ].join(","),
                )
                .join("\n");

        data +=
            `\nGenerated By,${user?.name?.toUpperCase() || `${user.firstName?.toUpperCase() || ""} ${user.lastName?.toUpperCase() || ""}`}\n` +
            `Date,${moment(new Date()).tz(userTimezone).format("DD/MM/YYYY HH:mm:ss")}`;

        return { data };
    }

    async exportMultipleZOutReports(user: any, body: ExportMultipleZOutReportDto, userTimezone: string, res: Response) {
        const { zOutIds, facilityIds, responseType } = body;

        if (zOutIds.length === 1) {
            // Single Z-Out - generate and return PDF directly
            const singleBody: ExportZOutReportDto = {
                zOutId: zOutIds[0],
                facilityIds,
                responseType: "pdf",
            };

            return await this.zOutReportByEmp(user, singleBody, userTimezone);
        }

        // Multiple Z-Outs - fetch all data in single aggregation
        const zOutReportsData = await this.fetchMultipleZOutReportsData(user, zOutIds, facilityIds, userTimezone);

        const pdfBuffers = await Promise.all(
            zOutReportsData.map(async (reportData) => {
                return this.generateZOutReportPDF(reportData.data, reportData.facility, reportData.reconciliation, reportData.activePaymentMethods);
            }),
        );

        if (responseType === "pdf") {
            // For multiple PDFs, we'll return a ZIP even if responseType is 'pdf'
            // since we can't return multiple files in single response
            return this.createZipArchive(zOutIds, pdfBuffers);
        }

        // Return as ZIP
        return this.createZipArchive(zOutIds, pdfBuffers);
    }

    private async fetchMultipleZOutReportsData(user: any, zOutIds: string[], facilityIds: Types.ObjectId[], userTimezone: string) {
        const facilityDetails = await this.getOrganizationandFacility(user);

        // Convert to ObjectIds
        const objectIds = zOutIds.map((id) => new Types.ObjectId(id));
        const facilityObjectIds = facilityIds.map((id) => new Types.ObjectId(id));

        // Fetch all reconciliations in single query
        const reconciliations = await this.ReconciliationModel.find({
            _id: { $in: objectIds },
            facilityId: { $in: facilityObjectIds },
            organizationId: new Types.ObjectId(facilityDetails.organizationId),
        }).sort({ createdAt: -1 });

        if (reconciliations.length === 0) {
            throw new Error("No reconciliation documents found.");
        }

        // Get facility details
        const facilityName = await this.FacilityModel.findOne({ _id: { $in: facilityObjectIds } }, { facilityName: 1, paymentMethods: 1 });

        const activePaymentMethods = facilityName?.paymentMethods?.filter((method: any) => method.isActive === true) || [];

        // For each reconciliation, get the previous one and prepare data
        const reportDataPromises = reconciliations.map(async (reconciliation) => {
            const secondLastReconciliation = await this.ReconciliationModel.findOne({
                facilityId: reconciliation.facilityId,
                organizationId: new Types.ObjectId(facilityDetails.organizationId),
                createdAt: { $lt: reconciliation.createdAt },
            }).sort({ createdAt: -1 });

            let gteDate: Date;
            if (secondLastReconciliation) {
                gteDate = secondLastReconciliation.createdAt;
            } else {
                const firstInvoice = await this.InvoiceModel.findOne(
                    {
                        facilityId: reconciliation.facilityId,
                        organizationId: new Types.ObjectId(facilityDetails.organizationId),
                    },
                    { createdAt: 1 },
                )
                    .sort({ createdAt: 1 })
                    .lean();
                gteDate = firstInvoice?.["createdAt"] || new Date(0);
            }

            const lteDate = reconciliation.createdAt;
            const currentDate = new Date();

            // Prepare metadata (similar to your existing data preparation)
            const data = {
                day: currentDate.toLocaleString("en-US", { weekday: "short" }).toLowerCase(),
                startDate: moment(gteDate).tz(userTimezone).format("DD/MM/YYYY"),
                endDate: moment(lteDate).tz(userTimezone).format("DD/MM/YYYY"),
                currentDate: moment(currentDate).tz(userTimezone).format("DD/MM/YYYY"),
                role: user.role ? user.role.type : "",
                name: user?.name,
                firstName: user?.firstName,
                lastName: user?.lastName,
                startTime: moment(gteDate).tz(userTimezone).format("HH:mm"),
                currentTime: moment(currentDate).tz(userTimezone).format("HH:mm"),
                endTime: moment(lteDate).tz(userTimezone).format("HH:mm"),
                facilityName: facilityName?.facilityName,
            };

            // Fetch sales data for this reconciliation period
            const salesData = await this.fetchSalesDataForPeriod(user.organizationId, facilityObjectIds, gteDate, lteDate);

            return {
                data,
                facility: salesData.length > 0 ? salesData[0] : {},
                reconciliation,
                activePaymentMethods,
            };
        });

        return Promise.all(reportDataPromises);
    }

    private async fetchSalesDataForPeriod(organizationId: string, facilityIds: Types.ObjectId[], gteDate: Date, lteDate: Date) {
        const filter: any = {
            organizationId: new Types.ObjectId(organizationId),
            paymentStatus: PaymentStatus.COMPLETED,
            updatedAt: {
                $gte: gteDate,
                $lte: lteDate,
            },
            facilityId: { $in: facilityIds },
        };

        // Use your existing aggregation pipeline here
        let pipeline: any = [
            { $match: filter },
            {
                $addFields: {
                    purchaseItemsSafe: { $ifNull: ["$purchaseItems", []] },
                    productItemSafe: { $ifNull: ["$productItem", []] },
                    customPackageItemsSafe: { $ifNull: ["$customPackageItems", []] },
                    returnItemsSafe: { $ifNull: ["$returnItems", []] },
                },
            },
            {
                $addFields: {
                    serviceItems: {
                        $map: {
                            input: "$purchaseItemsSafe",
                            as: "item",
                            in: {
                                $mergeObjects: [
                                    "$$item",
                                    {
                                        itemType: "service",
                                        isInclusiveofGst: { $ifNull: ["$isInclusiveofGst", false] },
                                        orderId: "$orderId",
                                        voucherDiscount: { $ifNull: ["$voucherDiscount", 0] },
                                    },
                                ],
                            },
                        },
                    },
                    productItems: {
                        $map: {
                            input: "$productItemSafe",
                            as: "item",
                            in: {
                                $mergeObjects: [
                                    "$$item",
                                    {
                                        itemType: "product",
                                        isInclusiveofGst: { $ifNull: ["$isInclusiveofGst", false] },
                                        orderId: "$orderId",
                                        voucherDiscount: { $ifNull: ["$voucherDiscount", 0] },
                                    },
                                ],
                            },
                        },
                    },
                    customPackageItems: {
                        $map: {
                            input: "$customPackageItemsSafe",
                            as: "item",
                            in: {
                                $mergeObjects: [
                                    "$$item",
                                    {
                                        itemType: "service",
                                        isInclusiveofGst: { $ifNull: ["$isInclusiveofGst", false] },
                                        orderId: "$orderId",
                                        voucherDiscount: { $ifNull: ["$voucherDiscount", 0] },
                                    },
                                ],
                            },
                        },
                    },
                    returnPackageItems: {
                        $map: {
                            input: "$returnItemsSafe",
                            as: "item",
                            in: {
                                $mergeObjects: [
                                    "$$item",
                                    {
                                        itemType: "return",
                                        isInclusiveofGst: { $ifNull: ["$isInclusiveofGst", false] },
                                        orderId: "$orderId",
                                        voucherDiscount: { $ifNull: ["$voucherDiscount", 0] },
                                    },
                                ],
                            },
                        },
                    },
                    completedPayments: {
                        $let: {
                            vars: {
                                paymentsArray: {
                                    $cond: [{ $isArray: "$paymentDetails" }, "$paymentDetails", { $ifNull: [["$paymentDetails"], []] }],
                                },
                            },
                            in: {
                                $filter: {
                                    input: "$$paymentsArray",
                                    as: "payment",
                                    cond: { $eq: ["$$payment.paymentStatus", PaymentStatus.COMPLETED] },
                                },
                            },
                        },
                    },
                },
            },
            {
                $facet: {
                    payments: [
                        { $unwind: "$completedPayments" },
                        {
                            $group: {
                                _id: {
                                    facilityId: "$facilityId",
                                    paymentMethod: "$completedPayments.paymentMethod",
                                },
                                totalPaid: { $sum: "$completedPayments.amount" },
                                totalSales: { $sum: "$grandTotal" },
                            },
                        },
                        {
                            $group: {
                                _id: "$_id.facilityId",
                                totalSales: { $sum: "$totalSales" },
                                paymentMethodSummary: {
                                    $push: {
                                        paymentMethod: "$_id.paymentMethod",
                                        totalAmount: "$totalPaid",
                                    },
                                },
                            },
                        },
                    ],
                    invoices: [
                        {
                            $project: {
                                facilityId: 1,
                                allItems: { $concatArrays: ["$serviceItems", "$productItems", "$customPackageItems", "$returnPackageItems"] },
                            },
                        },
                        {
                            $group: {
                                _id: "$facilityId",
                                invoices: { $push: "$allItems" },
                            },
                        },
                        {
                            $addFields: {
                                flattenedItems: {
                                    $reduce: {
                                        input: "$invoices",
                                        initialValue: [],
                                        in: { $concatArrays: ["$$value", "$$this"] },
                                    },
                                },
                            },
                        },
                    ],
                },
            },
            {
                $project: {
                    data: {
                        $map: {
                            input: "$payments",
                            as: "payment",
                            in: {
                                _id: "$$payment._id",
                                totalSales: "$$payment.totalSales",
                                paymentMethodSummary: "$$payment.paymentMethodSummary",
                                invoices: {
                                    $let: {
                                        vars: {
                                            inv: {
                                                $arrayElemAt: [
                                                    {
                                                        $filter: {
                                                            input: "$invoices",
                                                            as: "i",
                                                            cond: { $eq: ["$$i._id", "$$payment._id"] },
                                                        },
                                                    },
                                                    0,
                                                ],
                                            },
                                        },
                                        in: "$$inv.flattenedItems",
                                    },
                                },
                            },
                        },
                    },
                },
            },
            { $unwind: "$data" },
            {
                $replaceRoot: {
                    newRoot: "$data",
                },
            },
            {
                $lookup: {
                    from: "facilities",
                    localField: "_id",
                    foreignField: "_id",
                    as: "facility",
                },
            },
            {
                $project: {
                    _id: 1,
                    facilityName: { $arrayElemAt: ["$facility.facilityName", 0] },
                    totalSales: 1,
                    invoiceCount: { $size: "$invoices" },
                    invoices: 1,
                    paymentMethodSummary: 1,
                },
            },
        ];

        return await this.InvoiceModel.aggregate(pipeline).exec();
    }

    private async createZipArchive(zOutIds: string[], pdfBuffers: Buffer[]): Promise<StreamableFile> {
        const archive = archiver("zip", { zlib: { level: 9 } });
        const stream = new PassThrough();

        archive.pipe(stream);

        pdfBuffers.forEach((buffer, index) => {
            archive.append(buffer, { name: `Z-Out_Report_${zOutIds[index]}.pdf` });
        });

        await archive.finalize();

        return new StreamableFile(stream, {
            type: "application/zip",
            disposition: `attachment; filename="Z-Out_Reports_${moment().format("YYYY-MM-DD")}.zip"`,
        });
    }
}
