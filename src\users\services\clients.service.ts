import { BadRequestException, Injectable, NotFoundException } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { User, UserDocument } from "../schemas/user.schema";
import { ClientSession, Model, PipelineStage, Types } from "mongoose";
import { ClientDocument, Clients } from "../schemas/clients.schema";
import { TransactionService } from "src/utils/services/transaction.service";
import { ClientsDto } from "../dto/clients.dto";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { Facility } from "src/facility/schemas/facility.schema";
import { StaffProfileDetails } from "src/staff/schemas/staff.schema";
import { ClientsListDto } from "../dto/clients-list.dto";
import { UsersPipe } from "../pipes/users.pipe";
import { GeneralService } from "src/utils/services/general.service";
import { MailService } from "src/mail/services/mail.service";
import { UpdateMeasurementsDto } from "../dto/update-measurements.dto";
import { UpdatePoliciesDto } from "../dto/update-policies.dto";
import { UpdateClientsMobileDto } from "../dto/update-clients-mobile.dto";
import { updateStaffStatusDto } from "src/staff/dto/update-staff-status.dto";
import { SharePassDto, ShareVoucherDto } from "src/users/dto/share-pass.dto";
import { Purchase, PurchaseDocument } from "src/users/schemas/purchased-packages.schema";
import { PayRate } from "src/staff/schemas/pay-rate.schema";
import { ClientsListDtoV1 } from "../dto/clientsv1-list.dto";
import { IUserDocument } from "../interfaces/user.interface";
import { SharePassListDto } from "src/users/dto/share-pass-list.dto";
import { format } from "date-fns";
import { RoleService } from "src/role/services/role.service";
import { ClientLead } from "src/zoho-webhook/schema/zoho-webhook.schema";
import { SessionType } from "src/utils/enums/session-type.enum";
import { CreateMinorRequestDto } from "../dto/miner.request.dto";
import { SharePassMultipleDto } from "../dto/share-pass-multiple.dto";
import { ScheduleStatusType } from "src/scheduling/enums/schedule-status.enum";
import { SchedulingService } from "src/scheduling/services/scheduling.service";
import { InstantCheckinDto } from "src/scheduling/dto/instant-checkin.dto";
import { Organizations } from "src/organization/schemas/organization.schema";
import { UploadService } from "src/utils/services/upload.service";
import { UploadMultimediaDto } from "../dto/upload-document.dto";
import { DocumentLocker } from "../schemas/document.locker.schema";
import { CreateDocumentDto } from "../dto/create-document.dto";
import { IDatabaseObjectId } from "src/common/database/interfaces/database.objectid.interface";
import { RoleEntity } from "src/role/repository/entities/role.entity";
import { ENUM_PRODUCT_ITEM_TYPE } from "src/promotions/enums/item-type.enum";
import { VoucherService } from "src/organization/services/voucher.service";
import { O } from "node_modules/@faker-js/faker/dist/airline-CLphikKp.cjs";
import { PricingDocument } from "src/organization/schemas/pricing.schema";
import { type } from "os";

@Injectable()
export class ClientsService {
    constructor(
        @InjectModel(User.name) private UserModel: Model<User>,
        @InjectModel(Clients.name) private ClientModel: Model<Clients>,
        @InjectModel(Purchase.name) private PurchaseModel: Model<Purchase>,
        @InjectModel(Facility.name) private FacilityModel: Model<Facility>,
        @InjectModel(StaffProfileDetails.name) private StaffDetailsModel: Model<StaffProfileDetails>,
        @InjectModel(PayRate.name) private PayRateModel: Model<PayRate>,
        @InjectModel(ClientLead.name) private readonly leadModel: Model<ClientLead>,
        @InjectModel(Organizations.name) private OrganizationModel: Model<Organizations>,
        @InjectModel(DocumentLocker.name) private documentLockerModel: Model<DocumentLocker>,
        @InjectModel(RoleEntity.name) private roleSchema: Model<RoleEntity>,

        private roleService: RoleService,
        private transactionService: TransactionService,
        private usersPipe: UsersPipe,
        private generalService: GeneralService,
        private mailService: MailService,
        private schedulingService: SchedulingService,
        private uploadService: UploadService,
        private voucherService: VoucherService,
    ) {}

    async validFacility(facilityId: string, user: IUserDocument): Promise<Boolean> {
        let {
            role: { type: role },
        } = user;
        if (role === ENUM_ROLE_TYPE.WEB_MASTER) {
            let output = await this.checkStaffFacility(facilityId, user);
            return output;
        } else if (role === ENUM_ROLE_TYPE.ORGANIZATION) {
            let output = await this.checkOrgFacility(facilityId, user);
            return output;
        } else if (role === ENUM_ROLE_TYPE.FRONT_DESK_ADMIN || role === ENUM_ROLE_TYPE.TRAINER) {
            let output = await this.checkStaffFacility(facilityId, user);
            return output;
        } else {
            return false;
        }
    }

    async checkStaffFacility(facilityId: string, user: Object): Promise<boolean> {
        let staffId = user["_id"];
        let staffFacility = await this.StaffDetailsModel.findOne({ userId: staffId, facilityId: { $in: [facilityId] } });
        if (staffFacility) return true;
        return false;
    }

    async checkOrgFacility(facilityId: string, user: Object): Promise<Boolean> {
        let userId = user["_id"];
        let checkFacility = await this.FacilityModel.findOne({ _id: facilityId, organizationId: userId });
        if (checkFacility) return true;
        return false;
    }

    async registerClient(createClientDto: ClientsDto, delegateUserId: any, organizationId: string): Promise<any> {
        const session = await this.transactionService.startTransaction();
        try {
            let checkValidFacility = await this.FacilityModel.findOne({ _id: createClientDto.facilityId }, { organizationId: 1, facilityName: 1 });
            if (!checkValidFacility) throw new BadRequestException("Invalid facility");

            let userData;
            let password = "";
            const queryConditions = [];
            if (createClientDto.mobile) {
                queryConditions.push({ mobile: createClientDto.mobile, organizationId });
            }
            if (createClientDto.email) {
                queryConditions.push({ email: createClientDto.email, organizationId });
            }

            userData = await this.UserModel.findOne(queryConditions.length > 0 ? { $or: queryConditions } : {});

            if (userData) {
                throw new BadRequestException("User with this Mobile No/Email already Exist");
            }

            if (!userData) {
                let output = await this.createNewClient({ ...createClientDto, organizationId }, session);
                userData = output.userData;
                password = output.password;
            }
            const clientId = this.generalService.generateClientId();
            let clientDetails = {
                createdBy: delegateUserId["_id"] ?? userData["_id"],
                userId: userData["_id"],
                facilityId: createClientDto.facilityId,
                organizationId: checkValidFacility["organizationId"],
                clientId: clientId,
                dob: createClientDto.dob,
                gender: createClientDto.gender,
                activityLevel: createClientDto?.activityLevel,
                userType: createClientDto.userType,
                subUserType: createClientDto.subUserType,
                address: createClientDto.address,
                businessAddress: createClientDto.businessAddress,
                isBusiness: createClientDto.isBusiness,
                emergencyContactPerson: createClientDto.emergencyContactPerson,
                emergencyContactPhone: createClientDto.emergencyContactPhone,
                policies: createClientDto.policies,
                photo: createClientDto.photo,
                source: createClientDto?.isConvertingToClient ? "zoho" : undefined,
                sourceId: createClientDto?.clientLeadId ? createClientDto.clientLeadId : undefined,
            };

            let clientData = new this.ClientModel(clientDetails);
            await clientData.save({ session });

            if (createClientDto?.minor?.length) {
                let minorData = createClientDto.minor;
                for (let i = 0; i < minorData.length; i++) {
                    let minorUser = new this.UserModel({
                        name: minorData[i].firstName + " " + minorData[i].lastName,
                        firstName: minorData[i].firstName,
                        lastName: minorData[i].lastName,
                        mobile: userData.mobile,
                        email: userData.email,
                        organizationId,
                        role: ENUM_ROLE_TYPE.USER,
                        newUser: true,
                        isActive: true,
                        parent: userData["_id"],
                    });

                    let minorClientId = this.generalService.generateClientId();
                    let minorClientDetails = new this.ClientModel({
                        userId: minorUser["_id"],
                        facilityId: createClientDto.facilityId,
                        organizationId: checkValidFacility["organizationId"],
                        clientId: minorClientId,
                        dob: minorData[i].dob,
                        gender: minorData[i].gender,
                        activityLevel: userData.activityLevel,
                        address: null,
                        businessAddress: null,
                        isBusiness: false,
                        emergencyContactPerson: userData.firstName,
                        emergencyContactPhone: userData.mobile,
                        policies: [],
                        photo: "",
                        parent: userData["_id"],
                    });

                    const res = await minorUser.save({ session });
                    await minorClientDetails.save({ session });
                }
            }

            // if(createClientDto.email) {
            //     this.mailService.sendMail({
            //         to: createClientDto.email.toString(),
            //         subject: `Welcome to  - Set Your New Password`,
            //         template: "client-onboarding",
            //         context: {
            //             clientName: createClientDto.firstName,
            //             username: createClientDto.email,
            //             password: password,
            //         },
            //     });
            // }
            await this.transactionService.commitTransaction(session);

            if (createClientDto.isConvertingToClient && createClientDto.clientLeadId) {
                // here we  copyy the
                const clientLeadData = await this.leadModel.findOneAndUpdate({ _id: createClientDto.clientLeadId }, { $set: { isConvertedToClient: true } });
                const payload = {
                    userId: clientDetails?.userId?.toString(),
                    file: clientLeadData?.pdfUrl,
                    documentName: "WAVIER",
                    facilityId: createClientDto.facilityId,
                };
                const orgId = checkValidFacility["organizationId"];
                const organizationId = new Types.ObjectId(orgId);
                await this.createDocument(payload, delegateUserId, organizationId);
            }
            return clientData;
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }
    }

    async createNewClient(createClientDto: any, session: ClientSession) {
        let randomPassword = await this.generalService.generatePassword();
        let { salt, password } = await this.generalService.encryptPassword(randomPassword);
        const role = await this.roleService.findOneByType(ENUM_ROLE_TYPE.USER);
        if (!role) throw new BadRequestException("Invalid role");
        let userDetails = {
            name: [createClientDto.firstName, createClientDto.lastName].join(" ").trim(),
            firstName: createClientDto.firstName,
            lastName: createClientDto.lastName,
            mobile: createClientDto.mobile,
            email: createClientDto.email,
            role: role._id,
            newUser: true,
            isActive: true,
            password: password,
            salt: salt,
            countryCode: createClientDto.countryCode,
            organizationId: createClientDto.organizationId,
        };
        let userData = new this.UserModel(userDetails);
        await userData.save({ session });
        return { userData, password };
    }

    async registerMinor(user: IUserDocument, createClientDto: CreateMinorRequestDto, organizationId: string): Promise<any> {
        const session = await this.transactionService.startTransaction();
        try {
            let checkValidClient = await this.ClientModel.findOne({ _id: createClientDto.clientId });
            if (!checkValidClient) throw new BadRequestException("Invalid client");

            let parentUser = await this.UserModel.findOne({ _id: checkValidClient.userId });
            if (!parentUser) throw new BadRequestException("Invalid parent details");

            const userRole = await this.roleService.findOneByType(ENUM_ROLE_TYPE.USER);

            let minorData = createClientDto.minor;
            for (let i = 0; i < minorData.length; i++) {
                let minorUser = new this.UserModel({
                    name: minorData[i].firstName + " " + minorData[i].lastName,
                    firstName: minorData[i].firstName,
                    lastName: minorData[i].lastName,
                    mobile: parentUser.mobile,
                    email: parentUser.email,
                    role: userRole?._id,
                    organizationId,
                    newUser: true,
                    isActive: true,
                    parent: parentUser._id,
                    countryCode: parentUser.countryCode ? parentUser.countryCode : "+91",
                });

                let minorClientId = this.generalService.generateClientId();
                let minorClientDetails = new this.ClientModel({
                    createdBy: user._id,
                    userId: minorUser["_id"],
                    facilityId: checkValidClient.facilityId,
                    organizationId: checkValidClient.organizationId,
                    clientId: minorClientId,
                    dob: minorData[i].dob,
                    relation: minorData[i].relation,
                    gender: minorData[i].gender,
                    activityLevel: "",
                    address: checkValidClient.address,
                    businessAddress: null,
                    isBusiness: false,
                    emergencyContactPerson: `${parentUser.firstName} ${parentUser.lastName}`,
                    emergencyContactPhone: parentUser.mobile,
                    policies: minorData[i]?.policies || [],
                    photo: minorData[i].photo,
                });

                const res = await minorUser.save({ session });
                await minorClientDetails.save({ session });
                await this.transactionService.commitTransaction(session);

                if (minorData[i].minorId && createClientDto.wavierSourceId) {
                    // here we  copyy the
                    const clientLeadData = await this.leadModel.findOne({ _id: createClientDto.wavierSourceId });

                    const payload = {
                        userId: minorUser["_id"].toString(),
                        file: clientLeadData?.pdfUrl,
                        documentName: "ZOHO-WAVIER",
                        facilityId: checkValidClient.facilityId.toString(),
                    };
                    const orgId = checkValidClient.organizationId;
                    const organizationId = new Types.ObjectId(orgId.toString());
                    await this.createDocument(payload, user, organizationId);
                    await this.leadModel.updateOne(
                        { _id: createClientDto.wavierSourceId, "minors._id": minorData[i].minorId },
                        {
                            $set: {
                                "minors.$[elem].isMinorAdded": true,
                            },
                        },
                        {
                            arrayFilters: [{ "elem._id": new Types.ObjectId(minorData[i].minorId) }],
                        },
                    );
                }
            }
            return true;
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }
    }

    async clientList(clientListDto: ClientsListDto, facilityList: Array<string>): Promise<any> {
        let search = "";
        if (clientListDto.search) {
            search = clientListDto.search.trim();
        }
        let pipeline = this.usersPipe.getClientsPipe(clientListDto, facilityList, search);
        let data = await this.ClientModel.aggregate(pipeline);
        const userIds = data[0]?.list?.map((user) => new Types.ObjectId(user?.userId));
        const now = new Date();
        const activePricings = await this.PurchaseModel.find(
            {
                consumers: { $in: userIds },
                bundledPricingId: { $exists: false },
                isExchanged: { $ne: true },
                isExpired: false,
                exchangedInvoiceId: { $exists: false },
                isActive: { $ne: false },
                sharePass: { $ne: true },
                endDate: { $gt: now },
            },
            { userId: 1 },
        ).exec();
        const activeUserIdSet = new Set(activePricings.map((p) => p.userId.toString()));
        data[0].list = data[0].list.map((client) => {
            client.hasActivePricing = activeUserIdSet.has(client.userId.toString());
            return client;
        });

        return {
            count: data[0]?.total[0]?.total ? data[0]?.total[0]?.total : 0,
            list: data[0]?.list,
        };
    }

    async clientListV1(clientListDto: ClientsListDtoV1, facilityList: Array<string>): Promise<any> {
        let { search, clientId } = clientListDto;
        let searchQuery = [];
        if (search?.length > 0) {
            searchQuery.push({ name: { $regex: search, $options: "i" } }, { mobile: { $regex: search, $options: "i" } }, { email: { $regex: search, $options: "i" } });
        }
        let pipeline: any = this.usersPipe.getClientsPipeV1(clientListDto, facilityList, searchQuery);
        let data = await this.ClientModel.aggregate(pipeline);
        return {
            count: data[0]?.total[0]?.total ? data[0]?.total[0]?.total : 0,
            list: data[0]?.list,
        };
    }

    async clientListForTrainer(clientListDto: ClientsListDto, facilityList: Array<string>, user: UserDocument): Promise<any> {
        let search = "";
        if (clientListDto.search) {
            search = clientListDto.search.trim().split(" ").join("|");
        }

        const pipeline = this.usersPipe.getClientsPipeForTrainer(clientListDto, facilityList, search, user._id);
        const userIdsFromPayrate = await this.PayRateModel.aggregate(pipeline);

        const total = userIdsFromPayrate[0]?.total || 0;
        const users = userIdsFromPayrate[0]?.users || [];
        const userIds = users.map((user) => user._id);
        const clientList = await this.ClientModel.find({ userId: { $in: userIds } }).populate([
            {
                path: "facilityId",
                select: "facilityName",
            },
            {
                path: "createdBy",
                select: "name firstName lastName",
            },
        ]);
        const now = new Date();
        const activePricings = await this.PurchaseModel.find(
            {
                consumers: { $in: userIds },
                bundledPricingId: { $exists: false },
                isExchanged: { $ne: true },
                isExpired: false,
                exchangedInvoiceId: { $exists: false },
                isActive: { $ne: false },
                sharePass: { $ne: true },
                endDate: { $gt: now },
            },
            { userId: 1 },
        ).exec();
        const activeUserIdSet = new Set(activePricings.map((p) => p.userId.toString()));

        const formattedClientList = clientList.map((client: any) => {
            const user = users.find((user) => user._id.toString() === client.userId.toString());
            return {
                _id: client._id,
                facilityId: (client.facilityId as any)?._id || null,
                facilityName: (client.facilityId as any)?.facilityName || null,
                clientId: client.clientId,
                createdBy: client.createdBy
                    ? {
                          _id: client.createdBy._id,
                          name: client.createdBy.name,
                          firstName: client.createdBy.firstName,
                          lastName: client.createdBy.lastName,
                      }
                    : null,
                membershipId: client.membershipId,
                userId: user._id,
                name: user.name,
                firstName: user.firstName,
                lastName: user.lastName,
                isActive: user.isActive,
                mobile: user.mobile,
                countryCode: user.countryCode,
                email: user.email,
                createdAt: user.createdAt,
                cityName: (client.facilityId as any)?.facilityName || "",
                photo: client?.photo || "",
                hasActivePricing: activeUserIdSet.has(user._id.toString()),
            };
        });

        return {
            count: total,
            list: formattedClientList,
        };
    }

    async facilityList(user: Record<string, any>, locationId): Promise<any> {
        let {
            role: { type },
        } = user;
        let userId = user["_id"];
        let query = { organizationId: userId };
        if (locationId?.length > 0) {
            query["_id"] = { $in: locationId };
        }
        if (type === ENUM_ROLE_TYPE.FRONT_DESK_ADMIN || type === ENUM_ROLE_TYPE.WEB_MASTER || type === ENUM_ROLE_TYPE.TRAINER) {
            let output = await this.StaffDetailsModel.findOne({ userId: userId }, { facilityId: 1 });
            const facilityIds = output?.facilityId?.map((id) => id) || [];
            return facilityIds;
        } else if (type === ENUM_ROLE_TYPE.ORGANIZATION) {
            let output = await this.FacilityModel.find(query, { _id: 1 });
            const facilityIds = output.map((doc) => doc._id);
            return facilityIds;
        } else {
            throw new BadRequestException("Access denied");
        }
    }

    async facilityListV1(clientListDto: ClientsListDtoV1, user: IUserDocument): Promise<any> {
        const { organizationId, facilityIds } = clientListDto;
        if (facilityIds?.length > 0) return facilityIds.map((facilityId) => new Types.ObjectId(facilityId)) || [];
        let userId = user["_id"];
        let role = user["role"];

        if ([ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.TRAINER].includes(role.type)) {
            const result = await this.StaffDetailsModel.findOne({ userId, organizationId }, { facilityId: 1 });
            return result?.facilityId?.map((id) => new Types.ObjectId(id)) || [];
        }

        if (role.type === ENUM_ROLE_TYPE.ORGANIZATION) {
            if (userId.toString() !== organizationId.toString()) {
                throw new BadRequestException("Access denied");
            }
            const facilities = await this.FacilityModel.find({ organizationId: userId }, { _id: 1 });
            return facilities?.map((doc) => new Types.ObjectId(doc._id)) || [];
        }

        throw new BadRequestException("Access denied");
    }

    async updateClient(updateClientDto: ClientsDto, clientId: string): Promise<any> {
        const session = await this.transactionService.startTransaction();
        try {
            // Build clientDetails with only provided fields
            let clientDetails = {};

            if (updateClientDto.facilityId) clientDetails["facilityId"] = updateClientDto.facilityId;
            if (updateClientDto.dob) clientDetails["dob"] = updateClientDto.dob;
            if (updateClientDto.gender) clientDetails["gender"] = updateClientDto.gender;
            if (updateClientDto.activityLevel) clientDetails["activityLevel"] = updateClientDto.activityLevel;
            if (updateClientDto.userType) clientDetails["userType"] = updateClientDto.userType;
            if (updateClientDto.subUserType) clientDetails["subUserType"] = updateClientDto.subUserType;
            if (updateClientDto.address) clientDetails["address"] = updateClientDto.address;
            if (updateClientDto.businessAddress) clientDetails["businessAddress"] = updateClientDto.businessAddress;
            if (updateClientDto.emergencyContactPerson) clientDetails["emergencyContactPerson"] = updateClientDto.emergencyContactPerson;
            if (updateClientDto.emergencyContactPhone) clientDetails["emergencyContactPhone"] = updateClientDto.emergencyContactPhone;
            if (updateClientDto.policies) clientDetails["policies"] = updateClientDto.policies;
            if (updateClientDto.photo) clientDetails["photo"] = updateClientDto.photo;

            if (updateClientDto.notes) clientDetails["notes"] = updateClientDto.notes;

            let updateClient = await this.ClientModel.findOneAndUpdate({ _id: clientId }, { $set: clientDetails }, { new: true, session });
            if (!updateClient) throw new NotFoundException("Client not found");

            const user: any = await this.UserModel.findById(updateClient.userId).populate({
                path: "parent",
                select: "mobile countryCode email",
            });

            // Build userDetails with only provided fields
            let userDetails: any = {};

            if (updateClientDto.firstName || updateClientDto.lastName) {
                userDetails["name"] = `${updateClientDto.firstName || user.firstName} ${updateClientDto.lastName || user.lastName}`.trim();
                if (updateClientDto.firstName) userDetails["firstName"] = updateClientDto.firstName;
                if (updateClientDto.lastName) userDetails["lastName"] = updateClientDto.lastName;
            }
            if (updateClientDto.mobile) userDetails["mobile"] = user?.parent?.mobile ?? updateClientDto.mobile;
            if (updateClientDto.email) userDetails["email"] = user?.parent?.email ?? updateClientDto.email;
            if (updateClientDto.countryCode) userDetails["countryCode"] = updateClientDto.countryCode;

            if (Object.keys(userDetails).length > 0) {
                let updateUser = await this.UserModel.findOneAndUpdate({ _id: updateClient["userId"] }, { $set: userDetails }, { new: true, session });
                if (!updateUser) throw new BadRequestException("Client not found");

                // Only update children profiles if relevant fields are being updated
                if (userDetails.countryCode || userDetails.mobile || userDetails.email) {
                    const updateFields = {};
                    if (userDetails.countryCode) updateFields["countryCode"] = userDetails.countryCode;
                    if (userDetails.mobile) updateFields["mobile"] = userDetails.mobile;
                    if (userDetails.email) updateFields["email"] = userDetails.email;

                    await this.UserModel.updateMany({ parent: updateClient["userId"] }, { $set: updateFields }, { session });
                }
            }

            await this.transactionService.commitTransaction(session);
            return {};
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }
    }

    async deleteClient(clientId: string): Promise<any> {
        const session = await this.transactionService.startTransaction();
        try {
            // Check if client exists
            const clientExists = await this.UserModel.findById(clientId).session(session);
            if (!clientExists) throw new BadRequestException("Client not found");

            // Check if client has purchases
            const hasPurchases = await this.PurchaseModel.findOne({ consumers: clientId }).session(session);
            if (hasPurchases) {
                throw new BadRequestException("Client has purchased a package and cannot be deleted.");
            }

            // Delete client from User and ClientModel
            await this.UserModel.deleteOne({ _id: clientId }).session(session);
            await this.ClientModel.deleteOne({ userId: clientId }).session(session);

            await this.transactionService.commitTransaction(session);
            return { message: "Client deleted successfully." };
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }
    }

    async sharePass(sharePassDto: SharePassDto): Promise<any> {
        const session = await this.transactionService.startTransaction();
        try {
            // Validate users
            const [shareFromUser, shareToUser] = await Promise.all([
                this.UserModel.findOne({ _id: sharePassDto.shareFrom }),
                this.UserModel.findOne({ _id: sharePassDto.shareTo }),
            ]);
            if (!shareFromUser || !shareToUser) throw new BadRequestException("Client not found");

            // Validate clients within the same organization
            const [shareFromClient, shareToClient] = await Promise.all([
                this.ClientModel.findOne({ userId: sharePassDto.shareFrom, organizationId: sharePassDto.organizationId }),
                this.ClientModel.findOne({ userId: sharePassDto.shareTo, organizationId: sharePassDto.organizationId }),
            ]);
            if (!shareFromClient || !shareToClient) throw new BadRequestException("Both clients are not from the same organization.");

            // Fetch sender's purchase data
            const senderPurchaseData = await this.PurchaseModel.findOne({
                _id: sharePassDto.purchaseId,
                $or: [
                    {
                        userId: sharePassDto.shareFrom,
                    },
                    {
                        sponsorUser: sharePassDto.shareFrom,
                    },
                ],
            }).session(session);
            if (!senderPurchaseData) throw new BadRequestException("Purchase data not found");

            if (![SessionType.MULTIPLE, SessionType.DAY_PASS].includes(senderPurchaseData.sessionType))
                throw new BadRequestException("Session type should be multiple or day-pass.");

            if (sharePassDto.noOfSessions < 1) throw new BadRequestException("No of sessions must be greater than 0");
            if (senderPurchaseData.totalSessions - senderPurchaseData.sessionConsumed < sharePassDto.noOfSessions) {
                // Ensure sufficient sessions are available
                throw new BadRequestException("Not enough sessions available to share");
            }

            const packageStart = senderPurchaseData?.startDate ? new Date(senderPurchaseData.startDate).getTime() : null;
            const packageEnd = senderPurchaseData?.endDate ? new Date(senderPurchaseData.endDate).getTime() : null;
            const currentTime = new Date().getTime();
            if (!packageStart || !packageEnd) throw new BadRequestException("Invalid package dates.");

            if (!(packageStart <= currentTime && packageEnd >= currentTime)) {
                throw new BadRequestException(
                    `This package is not eligible for booking. Expiry date is ${format(new Date(packageEnd), "dd/MM/yyyy")} at ${format(new Date(packageEnd), "HH:mm")}.`,
                );
            }

            // Decrease session count for sender
            await this.PurchaseModel.updateOne(
                { _id: sharePassDto.purchaseId },
                { $inc: { sessionConsumed: sharePassDto.noOfSessions, sessionShared: sharePassDto.noOfSessions } },
                { session },
            );

            // Create new purchase record for the recipient
            const receiverPurchaseData = new this.PurchaseModel({
                userId: sharePassDto.shareTo,
                consumers: [sharePassDto.shareTo],
                packageId: senderPurchaseData.packageId,
                itemType: senderPurchaseData.itemType,
                bundledPricingId: senderPurchaseData.bundledPricingId,
                organizationId: senderPurchaseData.organizationId,
                facilityId: senderPurchaseData.facilityId,
                purchasedBy: sharePassDto.shareTo,
                purchaseDate: new Date(),
                paymentStatus: senderPurchaseData.paymentStatus,
                isExpired: senderPurchaseData.isExpired,
                sessionType: senderPurchaseData.sessionType,
                totalSessions: sharePassDto.noOfSessions,
                sessionConsumed: 0,
                sessionShared: 0,
                sharePass: true,
                sharedBy: sharePassDto.shareFrom,
                isActive: senderPurchaseData.isActive,
                startDate: senderPurchaseData.startDate,
                endDate: senderPurchaseData.endDate,
                invoiceId: senderPurchaseData.invoiceId,
                membershipId: senderPurchaseData?.membershipId || null,
                ...([SessionType.MULTIPLE, SessionType.DAY_PASS].includes(senderPurchaseData.sessionType) && {
                    sessionPerDay: senderPurchaseData.sessionPerDay,
                }),
                ...(senderPurchaseData.sessionType === SessionType.DAY_PASS && {
                    dayPassLimit: sharePassDto.noOfSessions,
                }),
            });
            await receiverPurchaseData.save({ session });
            // const mailData = await this.formatMailData(senderPurchaseData, receiverPurchaseData);
            // let isNew = true;
            // await this.sendConfirmationEmail(mailData, isNew);

            await this.transactionService.commitTransaction(session);

            return { message: "Shared pass successfully", sharedTo: sharePassDto.shareTo, totalSessions: sharePassDto.noOfSessions };
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }
    }

    async sharedPassList(sharePassListDto: SharePassListDto): Promise<any> {
        try {
            const pageSize = sharePassListDto.pageSize ?? 10;
            const page = sharePassListDto.page ?? 1;
            const skip = pageSize * (page - 1);
            const sharedPasses = await this.PurchaseModel.aggregate([
                { $match: { sharedBy: Types.ObjectId.createFromHexString(sharePassListDto.userId), sharePass: true } },
                {
                    $facet: {
                        list: [
                            { $sort: { updatedAt: -1 } },
                            { $skip: skip },
                            { $limit: pageSize },
                            {
                                $lookup: {
                                    from: "users",
                                    localField: "userId",
                                    foreignField: "_id",
                                    as: "recipientUser",
                                },
                            },
                            { $unwind: "$recipientUser" },
                            {
                                $lookup: {
                                    from: "clients",
                                    localField: "userId",
                                    foreignField: "userId",
                                    as: "recipientClient",
                                },
                            },
                            { $unwind: "$recipientClient" },
                            {
                                $lookup: {
                                    from: "pricings",
                                    localField: "packageId",
                                    foreignField: "_id",
                                    as: "packageDetails",
                                },
                            },
                            { $unwind: "$packageDetails" },
                            {
                                $lookup: {
                                    from: "facilities",
                                    localField: "facilityId",
                                    foreignField: "_id",
                                    as: "facilityDetails",
                                },
                            },
                            { $unwind: "$facilityDetails" },
                            {
                                $lookup: {
                                    from: "cities",
                                    localField: "facilityDetails.address.city",
                                    foreignField: "_id",
                                    as: "cityDetails",
                                },
                            },
                            {
                                $project: {
                                    _id: 0,
                                    clientId: "$recipientClient.clientId",
                                    userId: "$recipientUser._id",
                                    sharedTo: { $concat: ["$recipientUser.firstName", " ", "$recipientUser.lastName"] },
                                    packageName: "$packageDetails.name",
                                    transferredSessions: "$totalSessions",
                                    cityName: {
                                        $arrayElemAt: ["$cityDetails.name", 0],
                                    },
                                    facilityName: "$facilityDetails.facilityName",
                                    date: {
                                        $dateToString: { format: "%d-%m-%Y", date: "$purchaseDate" },
                                    },
                                    time: {
                                        $dateToString: { format: "%H:%M:%S", date: "$purchaseDate" },
                                    },
                                },
                            },
                        ],
                        total: [
                            {
                                $count: "total",
                            },
                        ],
                    },
                },
            ]);

            return { list: sharedPasses[0]?.list, count: sharedPasses[0]?.total[0]?.total ? sharedPasses[0]?.total[0]?.total : 0 };
        } catch (error) {
            throw new Error("Error fetching shared pass details: " + error.message);
        }
    }

    async formatMailData(senderPurchaseData, receiverPurchaseData) {
        const [senderClient, receiverClient]: [any, any] = await Promise.all([
            await this.ClientModel.findOne({ userId: senderPurchaseData.userId }, { userId: 1, clientId: 1, facilityId: 1, packageId: 1 }).populate([
                { path: "facilityId", select: "_id facilityName address profilePicture billingDetails" },
                { path: "organizationId", select: "_id name email" },
                { path: "packageId", select: "_id name price services expiredInDays durationUnit" },
                { path: "userId", select: "_id name firstName lastName email mobile" },
            ]),
            await this.ClientModel.findOne({ userId: receiverPurchaseData.userId }, { userId: 1, clientId: 1 }).populate([
                { path: "userId", select: "_id name firstName lastName email mobile" },
            ]),
        ]);

        const data = {
            facilityId: senderClient.facilityId._id,
            organizationId: senderClient.organizationId._id,
            organizationEmail: senderClient.organizationId.email,
            packageId: senderClient.packageId._id,
            sessionsShared: receiverClient.totalSessions,
            facilityName: senderClient.facilityId.facilityName,
            packageName: senderClient.packageId.name,

            senderClientId: senderClient._id,
            senderClientName: `${senderClient.userId.firstName} ${senderClient.userId.lastName}`,
            senderClientFirstName: senderClient.userId.firstName,
            senderClienttLastName: senderClient.userId.lastName,
            senderClientEmail: senderClient.userId.email,
            senderClientPhone: senderClient.userId.mobile,
            senderCustomerId: senderClient.clientId,

            receiverClientId: receiverClient._id,
            receiverClientName: `${receiverClient.userId.firstName} ${receiverClient.userId.lastName}`,
            receiverClientFirstName: receiverClient.userId.firstName,
            receiverClientLastName: receiverClient.userId.lastName,
            receiverClientEmail: receiverClient.userId.email,
            receiverClientPhone: receiverClient.userId.mobile,
            receiverCustomerId: receiverClient.clientId,
        };
        return data;
    }

    private async sendConfirmationEmail(dataForMail: any, isNew: boolean) {
        if (dataForMail?.senderClientEmail) {
            await this.mailService.sendMail({
                to: dataForMail["senderClientEmail"].toString(),
                subject: `Share pass transferred`,
                template: "share-pass",
                context: {
                    facilityName: dataForMail.facilityName || "Default Facility Name", // Add a fallback if undefined
                    ...dataForMail, // Spread other properties
                },
            });
        }
        if (dataForMail?.receiverClientEmail) {
            await this.mailService.sendMail({
                to: dataForMail["receiverClientEmail"].toString(),
                subject: `Share pass transferred`,
                template: "share-pass",
                context: {
                    facilityName: dataForMail.facilityName || "Default Facility Name", // Add a fallback if undefined
                    ...dataForMail, // Spread other properties
                },
            });
        }
        if (dataForMail?.organizationEmail) {
            await this.mailService.sendMail({
                to: dataForMail["organizationEmail"].toString(),
                subject: `Share pass transferred`,
                template: "share-pass",
                context: {
                    facilityName: dataForMail.facilityName || "Default Facility Name", // Add a fallback if undefined
                    ...dataForMail, // Spread other properties
                },
            });
        }
    }

    async updateClientMobile(updateClientDto: UpdateClientsMobileDto, clientId: string): Promise<any> {
        const session = await this.transactionService.startTransaction();
        try {
            let clientDetails = {
                organizationId: updateClientDto.organizationId,
                dob: updateClientDto.dob,
                gender: updateClientDto.gender,
                activityLevel: updateClientDto?.activityLevel,
                address: updateClientDto.address,
                emergencyContactPerson: updateClientDto.emergencyContactPerson,
                emergencyContactPhone: updateClientDto.emergencyContactPhone,
                policies: updateClientDto.policies,
                photo: updateClientDto.photo,
            };

            let updateClient = await this.ClientModel.findOneAndUpdate(
                {
                    userId: clientId,
                    organizationId: updateClientDto.organizationId,
                },
                {
                    $set: clientDetails,
                },
                {
                    new: true,
                    session,
                },
            );
            if (!updateClient) throw new BadRequestException("Client not found");

            let userDetails = {
                name: updateClientDto.firstName + " " + updateClientDto.lastName,
                firstName: updateClientDto.firstName,
                lastName: updateClientDto.lastName,
                mobile: updateClientDto.mobile,
                email: updateClientDto.email,
            };

            let updateUser = await this.UserModel.findOneAndUpdate(
                {
                    _id: clientId,
                },
                {
                    $set: userDetails,
                },
                {
                    new: true,
                    session,
                },
            );
            if (!updateUser) throw new BadRequestException("Client not found");
            await this.transactionService.commitTransaction(session);
            return {};
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }
    }

    async clientDetails(clientId: string): Promise<any> {
        const pipeline = this.usersPipe.clientDetails([clientId]) as PipelineStage[];
        let data = await this.ClientModel.aggregate(pipeline);
        if (data.length == 0) throw new BadRequestException("Client not found");
        const client = data[0];
        const parentId = client.parent;
        if (parentId) {
            const parentClient = (await this.ClientModel.findOne({ userId: parentId })).toObject();
            const parentUser = (await this.UserModel.findOne({ _id: parentId })).toObject();
            client.address = parentClient?.address
                ? {
                      ...(parentClient?.address || {}),
                      firstName: parentUser.firstName,
                      lastName: parentUser.lastName,
                  }
                : null;
            client.businessAddress = parentClient?.businessAddress
                ? {
                      ...(parentClient?.businessAddress || {}),
                      firstName: parentUser.firstName,
                      lastName: parentUser.lastName,
                  }
                : null;
        } else {
            client.address = client.address
                ? {
                      ...client.address,
                      firstName: client.firstName,
                      lastName: client.lastName,
                  }
                : null;
            client.businessAddress = client.businessAddress
                ? {
                      ...client.businessAddress,
                      firstName: client.firstName,
                      lastName: client.lastName,
                  }
                : null;
        }
        const organizationDetail = await this.OrganizationModel.findOne({ userId: client.organizationId });
        const orgPolicies: any = organizationDetail?.clientOnboarding?.policies?.items || [];
        const clientPolicies = client?.policies || [];
        const missingRequiredPolicies = [];
        const onlyShownOrgPolicies = orgPolicies.filter((op) => op.isShown); // only get the policy whose policy shown are true
        for (const policy of onlyShownOrgPolicies) {
            const hasPolicy = clientPolicies.some((cp) => cp.policyId?.toString() === policy._id?.toString());
            if (!hasPolicy) {
                missingRequiredPolicies.push({
                    id: policy._id,
                    name: policy.name,
                    message: `${policy.name} is  incomplete`,
                });
            }
        }

        client.missingRequiredPolicies = missingRequiredPolicies;

        return client;
    }

    async clientDetailsForApp(clientId: string): Promise<any> {
        let pipeline = this.usersPipe.clientDetailsForApp(clientId);
        let data = await this.ClientModel.aggregate(pipeline);
        if (data.length == 0) throw new BadRequestException("Client not found");
        return data[0];
    }

    async updateAssessment(updateAssessmentDto: UpdateMeasurementsDto, clientId: string): Promise<any> {
        let updateClient = await this.ClientModel.findOneAndUpdate(
            {
                _id: clientId,
            },
            {
                $set: updateAssessmentDto,
            },
            {
                new: true,
            },
        );
        if (!updateClient) throw new BadRequestException("Client not found");

        return updateClient;
    }

    async updatePolicy(updatePoliciesDto: UpdatePoliciesDto, clientId: string): Promise<any> {
        let updatePolicy = await this.ClientModel.findOneAndUpdate(
            {
                _id: clientId,
            },
            {
                $set: updatePoliciesDto,
            },
            {
                new: true,
            },
        );

        if (!updatePolicy) throw new BadRequestException("Client not found");
        return updatePolicy;
    }

    async adminUpdateClientStatus(UpdateStaffDto: updateStaffStatusDto, userId: string): Promise<any> {
        const session = await this.transactionService.startTransaction();

        try {
            const { isActive } = UpdateStaffDto;
            const existingUser = await this.UserModel.findById(new Types.ObjectId(userId)).session(session);
            if (!existingUser) {
                throw new BadRequestException("User not found");
            }

            const updateUser = await this.UserModel.findOneAndUpdate({ _id: new Types.ObjectId(userId) }, { $set: { isActive } }, { new: true, upsert: true, session });

            await session.commitTransaction();
            return {
                message: "Client Status Updated Successfully",
                data: updateUser,
            };
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw new BadRequestException(error.message || "An error occurred while updating the Client status");
        } finally {
            session.endSession();
        }
    }

    async setDefaultAddress(clientId: string, addressId: string) {
        const client = await this.ClientModel.findOne({
            _id: new Types.ObjectId(clientId),
        });

        if (!client) {
            throw new NotFoundException("Client not found");
        }

        // Check if address exists
        const addressExists = (client.address && client.address._id.toString() === addressId) || (client.businessAddress && client.businessAddress._id.toString() === addressId);

        if (!addressExists) {
            throw new NotFoundException("Address not found");
        }

        // Update primary flags
        if (client.address && client.address._id.toString() === addressId) {
            client.address.isDefault = true;
            if (client.businessAddress) {
                client.businessAddress.isDefault = false;
            }
        } else if (client.businessAddress && client.businessAddress._id.toString() === addressId) {
            client.businessAddress.isDefault = true;
            if (client.address) {
                client.address.isDefault = false;
            }
        }

        await client.save();
        return client;
    }

    async getMinor(clientId: string) {
        const client = await this.ClientModel.findOne({
            _id: new Types.ObjectId(clientId),
        });

        if (!client) {
            throw new NotFoundException("Client not found");
        }
        const minorUsers = await this.UserModel.find({
            parent: client.userId as string,
        });
        const minorClient = await this.ClientModel.find({
            userId: { $in: minorUsers.map((client) => client._id) },
        });

        const pipeline = this.usersPipe.clientDetails(minorClient.map((client) => client._id.toString())) as PipelineStage[];
        const data = await this.ClientModel.aggregate(pipeline);

        // Get all unique parent IDs
        const parentIds = [...new Set(data.filter((client) => client.parent).map((client) => client.parent))];

        // Fetch all parent data in bulk
        const [parentClients, parentUsers] = await Promise.all([
            this.ClientModel.find({ userId: { $in: parentIds } }).lean(),
            this.UserModel.find({ _id: { $in: parentIds } }).lean(),
        ]);

        // Create lookup maps for faster access
        const parentClientMap = new Map(parentClients.map((pc) => [pc.userId.toString(), pc]));
        const parentUserMap = new Map(parentUsers.map((pu) => [pu._id.toString(), pu]));

        // Process all clients in one pass
        data.forEach((client) => {
            const parentId = client.parent;
            if (parentId) {
                const parentClient = parentClientMap.get(parentId.toString());
                const parentUser = parentUserMap.get(parentId.toString());

                client.address = parentClient?.address
                    ? {
                          ...parentClient.address,
                          firstName: parentUser.firstName,
                          lastName: parentUser.lastName,
                      }
                    : null;

                client.businessAddress = parentClient?.businessAddress
                    ? {
                          ...parentClient.businessAddress,
                          firstName: parentUser.firstName,
                          lastName: parentUser.lastName,
                      }
                    : null;
            } else {
                client.address = client.address
                    ? {
                          ...client.address,
                          firstName: client.firstName,
                          lastName: client.lastName,
                      }
                    : null;

                client.businessAddress = client.businessAddress
                    ? {
                          ...client.businessAddress,
                          firstName: client.firstName,
                          lastName: client.lastName,
                      }
                    : null;
            }
        });
        return data;
    }

    async sharePassToMultiple(user: IUserDocument, dto: SharePassMultipleDto): Promise<any> {
        const session = await this.transactionService.startTransaction();
        try {
            const { shareFrom, organizationId, shares } = dto;
            let res = [];
            let invoiceId = null;
            let packageId = null;

            const shareFromUser = await this.UserModel.findById(shareFrom);
            if (!shareFromUser) throw new BadRequestException("Sender user not found");

            const shareFromClient = await this.ClientModel.findOne({ userId: shareFrom, organizationId });
            if (!shareFromClient) throw new BadRequestException("Sender client not found in organization");

            if (!shares || shares.length === 0) {
                throw new BadRequestException("No share instructions provided");
            }

            let allPurchaseId = [...new Set(shares.map((share) => share.purchaseId))];
            if (allPurchaseId.length === 0) throw new BadRequestException("No purchase IDs provided for sharing");

            const allShareToUserIds = [...new Set(shares.map((s) => s.shareTo))];
            const allPurchaseIds = [...new Set(shares.map((s) => s.purchaseId))];

            const [receiverUsers, receiverClients, senderPurchases] = await Promise.all([
                this.UserModel.find({ _id: { $in: allShareToUserIds } }),
                this.ClientModel.find({ userId: { $in: allShareToUserIds }, organizationId }),
                this.PurchaseModel.find({ _id: { $in: allPurchaseIds } }).session(session),
            ]);
            invoiceId = senderPurchases[0].invoiceId;
            packageId = senderPurchases[0].packageId;
            // Index for fast lookup
            const receiverUserMap = new Map(receiverUsers.map((u) => [u._id.toString(), u]));
            const receiverClientMap = new Map(receiverClients.map((c) => [c.userId.toString(), c]));
            const purchaseDataMap = new Map(senderPurchases.map((p) => [p._id.toString(), p]));

            const availablePasses = await this.getPricingForSharePass(shareFrom, allPurchaseId, organizationId);

            const passMap = new Map<string, any>();
            availablePasses.forEach((p) => passMap.set(p._id?.toString() || "", p));

            const enrollPurchaseId = new Set<string>();

            for (const share of shares) {
                const { shareTo, purchaseId, noOfSessions } = share;

                if (!noOfSessions) {
                    enrollPurchaseId.add(purchaseId);
                    continue;
                }

                const receiverUser = receiverUserMap.get(shareTo);
                if (!receiverUser) throw new BadRequestException(`Receiver user not found: ${shareTo}`);

                const receiverClient = receiverClientMap.get(shareTo);
                if (!receiverClient) throw new BadRequestException(`Receiver client not in same organization: ${shareTo}`);

                const senderPurchaseData = purchaseDataMap.get(purchaseId);
                if (!senderPurchaseData) throw new BadRequestException(`Invalid purchase ID: ${purchaseId}`);

                const matchedPass = passMap.get(purchaseId);
                if (!matchedPass) throw new BadRequestException(`Purchase is not eligible for sharing`);

                const packageStart = senderPurchaseData?.startDate ? new Date(senderPurchaseData.startDate).getTime() : null;
                const packageEnd = senderPurchaseData?.endDate ? new Date(senderPurchaseData.endDate).getTime() : null;
                const currentTime = new Date().getTime();
                if (!packageStart || !packageEnd) throw new BadRequestException("Invalid package dates.");

                if (!(packageStart <= currentTime && packageEnd >= currentTime)) {
                    throw new BadRequestException(
                        `This package is not eligible for booking. Expiry date is ${format(new Date(packageEnd), "dd/MM/yyyy")} at ${format(new Date(packageEnd), "HH:mm")}.`,
                    );
                }

                if (matchedPass.remainingSessions < noOfSessions || matchedPass.remainingSessions < 1) {
                    throw new BadRequestException(`Not enough sessions left in purchase ${purchaseId}`);
                }

                const newPurchase = new this.PurchaseModel({
                    userId: shareTo,
                    packageId: senderPurchaseData.packageId,
                    itemType: senderPurchaseData.itemType,
                    bundledPricingId: senderPurchaseData.bundledPricingId,
                    organizationId: senderPurchaseData.organizationId,
                    facilityId: senderPurchaseData.facilityId,
                    purchasedBy: shareTo,
                    purchaseDate: new Date(),
                    paymentStatus: senderPurchaseData.paymentStatus,
                    isExpired: senderPurchaseData.isExpired,
                    sessionType: senderPurchaseData.sessionType,
                    totalSessions: noOfSessions,
                    sessionConsumed: 0,
                    sessionShared: 0,
                    sharePass: true,
                    sharedBy: shareFrom,
                    isActive: senderPurchaseData.isActive,
                    startDate: senderPurchaseData.startDate,
                    endDate: senderPurchaseData.endDate,
                    invoiceId: senderPurchaseData.invoiceId,
                    membershipId: senderPurchaseData?.membershipId || null,
                    ...(senderPurchaseData.sessionType === SessionType.DAY_PASS && {
                        dayPassLimit: noOfSessions,
                    }),
                });
                let purchaseDoc = await newPurchase.save({ session });
                let purchase = purchaseDoc.toObject();
                purchase["name"] = receiverUser?.firstName + " " + receiverUser?.lastName;
                res.push(purchase);
                enrollPurchaseId.add(purchaseDoc._id.toString());

                matchedPass.remainingSessions -= noOfSessions;

                await this.PurchaseModel.updateOne(
                    { _id: purchaseId },
                    {
                        $inc: {
                            sessionConsumed: noOfSessions,
                            sessionShared: noOfSessions,
                        },
                    },
                    { session },
                );
            }
            let ownerPurchaseData = await this.PurchaseModel.findOne({
                invoiceId: invoiceId,
                //packageId: packageId,
                userId: shareFrom,
                sessionType: { $in: [SessionType.MULTIPLE, SessionType.DAY_PASS, SessionType.SINGLE] },
                $expr: {
                    $gt: [
                        {
                            $cond: {
                                if: { $eq: ["$sessionType", SessionType.DAY_PASS] },
                                then: { $subtract: ["$dayPassLimit", "$sessionConsumed"] },
                                else: { $subtract: ["$totalSessions", "$sessionConsumed"] },
                            },
                        },
                        0,
                    ],
                },
            })
                .session(session)
                .lean();
            if (ownerPurchaseData) {
                ownerPurchaseData["name"] = shareFromUser?.firstName + " " + shareFromUser?.lastName;
                ownerPurchaseData["isOwner"] = true;
                res.push(ownerPurchaseData);
            }

            // checkin
            const schedulingData: InstantCheckinDto = {
                purchaseIds: [...enrollPurchaseId],
                organizationId: organizationId,
                facilityId: shareFromClient.facilityId as string,
            };
            const schedulings = await this.schedulingService.instantBookedAndCheckin(user, schedulingData, session);

            await this.transactionService.commitTransaction(session);
            return {
                message: "All passes shared successfully",
                data: schedulings,
            };
        } catch (err) {
            await this.transactionService.abortTransaction(session);
            throw err;
        } finally {
            session.endSession();
        }
    }

    async getPricingForSharePass(userId: string, allPurchaseId: Array<string>, organizationId: string) {
        const now = new Date();
        const query: any = {
            _id: { $in: allPurchaseId.map((id) => new Types.ObjectId(id)) },
            userId: new Types.ObjectId(userId),
            organizationId: new Types.ObjectId(organizationId),
            isExpired: false,
            startDate: { $lte: now },
            endDate: { $gte: now },
            sessionType: { $in: [SessionType.SINGLE, SessionType.MULTIPLE, SessionType.DAY_PASS] },
        };

        const agg = [
            { $match: query },
            {
                $set: {
                    sessionConsumed: { $ifNull: ["$sessionConsumed", 0] },
                },
            },
            {
                $lookup: {
                    from: "pricings",
                    localField: "packageId",
                    foreignField: "_id",
                    as: "pricingDetails",
                },
            },
            { $unwind: "$pricingDetails" },
            {
                $match: {
                    "pricingDetails.services.type": { $ne: "courses" },
                },
            },
            {
                $facet: {
                    multiple: [
                        { $match: { sessionType: { $ne: SessionType.DAY_PASS } } },
                        {
                            $addFields: {
                                remainingSessions: {
                                    $cond: {
                                        if: { $eq: ["$pricingDetails.services.sessionCount", Infinity] },
                                        then: "unlimited",
                                        else: {
                                            $subtract: [
                                                {
                                                    $cond: {
                                                        if: { $eq: ["$sharePass", true] },
                                                        then: "$totalSessions",
                                                        else: "$pricingDetails.services.sessionCount",
                                                    },
                                                },
                                                "$sessionConsumed",
                                            ],
                                        },
                                    },
                                },
                            },
                        },
                        {
                            $match: {
                                $or: [{ remainingSessions: { $gt: 0 } }, { remainingSessions: "unlimited" }],
                            },
                        },
                    ],
                    dayPass: [
                        { $match: { sessionType: SessionType.DAY_PASS } },
                        {
                            $lookup: {
                                from: "schedulings",
                                let: {
                                    purchaseId: "$_id",
                                    clientId: "$userId",
                                },
                                pipeline: [
                                    {
                                        $match: {
                                            $expr: {
                                                $and: [
                                                    { $eq: ["$purchaseId", "$$purchaseId"] },
                                                    { $eq: ["$clientId", "$$clientId"] },
                                                    { $ne: ["$scheduleStatus", ScheduleStatusType.CANCELED] },
                                                ],
                                            },
                                        },
                                    },
                                    {
                                        $group: {
                                            _id: "$date",
                                        },
                                    },
                                ],
                                as: "bookedDates",
                            },
                        },
                        {
                            $addFields: {
                                consumedDayPassLimit: { $size: "$bookedDates" },
                            },
                        },
                        {
                            $match: {
                                $expr: {
                                    $gt: ["$dayPassLimit", "$consumedDayPassLimit"],
                                },
                            },
                        },
                        {
                            $addFields: {
                                remainingSessions: {
                                    $subtract: [{ $subtract: ["$dayPassLimit", "$consumedDayPassLimit"] }, "$sessionShared"],
                                },
                            },
                        },
                    ],
                },
            },
            {
                $project: {
                    packages: {
                        $concatArrays: ["$multiple", "$dayPass"],
                    },
                },
            },
            { $unwind: "$packages" },
            {
                $replaceRoot: { newRoot: "$packages" },
            },
            {
                $project: {
                    packageId: "$pricingDetails._id",
                    packageName: "$pricingDetails.name",
                    sessionType: 1,
                    startDate: 1,
                    endDate: 1,
                    sessionCount: {
                        $cond: {
                            if: { $eq: ["$pricingDetails.services.sessionCount", Infinity] },
                            then: "unlimited",
                            else: "$pricingDetails.services.sessionCount",
                        },
                    },
                    remainingSessions: 1,
                    sessionConsumed: 1,
                    dayPassLimit: 1,
                    consumedDayPassLimit: 1,
                },
            },
        ];

        const packageList = await this.PurchaseModel.aggregate(agg);

        return packageList;
    }

    async uploadFile(
        file: Express.Multer.File,
        //uploadFileDto: UploadMultimediaDto,
        user: IUserDocument, // staff/trainer who uploads
    ): Promise<any> {
        // console.log("uploadFileDto", uploadFileDto);
        // let { documentName} = uploadFileDto
        const allowedImageTypes = ["image/png", "image/jpeg", "image/jpg", "image/webp"];
        const allowedPdfTypes = ["application/pdf", "application/vnd.pds", "application/octet-stream"];
        let uploadPath = "";
        let fileType = "";

        if (allowedImageTypes.includes(file.mimetype)) {
            uploadPath = `document-locker`;
            fileType = "image";
        } else if (allowedPdfTypes.includes(file.mimetype)) {
            uploadPath = `document-locker`;
            fileType = "pdf";
        } else {
            throw new BadRequestException("Invalid file type. Only Images (png, jpg, jpeg, webp) and PDFs are allowed.");
        }

        const uploadedFile = await this.uploadService.upload(file.buffer, uploadPath, file.originalname);

        if (!uploadedFile?.Location) {
            throw new BadRequestException("Unable to upload file");
        }
        return {
            message: "File uploaded successfully",
            document: uploadedFile?.Location,
        };
    }

    async createDocument(dto: CreateDocumentDto, user: IUserDocument, organizationId: IDatabaseObjectId): Promise<any> {
        const { userId, file, documentName, facilityId } = dto;
        const clientRole = await this.roleSchema.findOne({ type: ENUM_ROLE_TYPE.USER });
        if (!clientRole || !clientRole._id) throw new BadRequestException("Client role not found");

        let checkUser = await this.UserModel.findOne({ _id: userId, role: clientRole._id });
        if (!checkUser) throw new BadRequestException("User not found");

        let checkFacility = await this.ClientModel.findOne({ userId: userId, facilityId: facilityId });
        if (!checkFacility) throw new BadRequestException("Client not found in this facility");
        if (checkFacility?.organizationId.toString() !== organizationId.toString()) throw new BadRequestException("This User is Not in your organization");

        const savedDoc = await this.documentLockerModel.create({
            userId: userId,
            facilityId: facilityId,
            organizationId: organizationId,
            fileUrl: file,
            documentName: documentName,
            uploadedBy: user._id,
            uploadedAt: new Date(),
        });
        return savedDoc;
    }

    async deleteDocument(documentId: string, organizationId: IDatabaseObjectId): Promise<any> {
        const document = await this.documentLockerModel.findById(documentId);
        if (!document) throw new NotFoundException("Document not found");

        const user = await this.UserModel.findById(document.userId);
        if (!user || user.organizationId.toString() !== organizationId.toString()) {
            throw new BadRequestException("You cannot delete document of another organization");
        }

        await this.documentLockerModel.deleteOne({ _id: documentId });
        return { message: "Document deleted successfully" };
    }

    async listDocuments(userId: string, organizationId: IDatabaseObjectId): Promise<any> {
        return await this.documentLockerModel.aggregate([
            {
                $match: {
                    userId: new Types.ObjectId(userId),
                    organizationId: new Types.ObjectId(organizationId),
                },
            },
            {
                $lookup: {
                    from: "users",
                    localField: "uploadedBy",
                    foreignField: "_id",
                    as: "uploadedByUser",
                },
            },
            { $unwind: "$uploadedByUser" },
            {
                $lookup: {
                    from: "roles",
                    localField: "uploadedByUser.role",
                    foreignField: "_id",
                    as: "uploadedByRole",
                },
            },
            { $unwind: "$uploadedByRole" },
            {
                $project: {
                    _id: 1,
                    uploadedBy: "$uploadedByUser.firstName" + " " + "$uploadedByUser.lastName" ? "$uploadedByUser.name" : "",
                    uploadedByRole: "$uploadedByRole.name",
                    documentName: 1,
                    fileUrl: 1,
                    uploadedAt: 1,
                },
            },
        ]);
    }
}
