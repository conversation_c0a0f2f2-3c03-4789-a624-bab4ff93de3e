import { Schema, Prop, SchemaFactory } from '@nestjs/mongoose';
import { Document, SchemaTypes, Types } from 'mongoose';

export type VoucherRedemptionLogDocument = VoucherRedemption & Document;

@Schema({ timestamps: true })
export class VoucherRedemption {

    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: 'PurchasedPackages', index: true })
    purchaseId: Types.ObjectId;

    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: 'User', index: true })
    userId: Types.ObjectId;

    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: 'User', index: true })
    organizationId: Types.ObjectId;

    @Prop({ type: Number, required: true })
    amountRedeemed: number;

    @Prop({ type: Number, required: true })
    remainingBalance: number;

    @Prop({ type: Number, required: true })
    previousBalance: number;

    @Prop({ type: SchemaTypes.ObjectId, required: false, ref: 'invoices' })
    invoiceId?: Types.ObjectId;

    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: 'User' })
    createdBy: Types.ObjectId;
}

export const VoucherRedemptionSchema = SchemaFactory.createForClass(VoucherRedemption);

// Create compound indexes for efficient querying
VoucherRedemptionSchema.index({ purchaseId: 1, redemptionDate: -1 });
VoucherRedemptionSchema.index({ userId: 1, redemptionDate: -1 });
VoucherRedemptionSchema.index({ organizationId: 1, redemptionDate: -1 });
