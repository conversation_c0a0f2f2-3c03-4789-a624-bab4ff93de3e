import { MailerModule } from "@nestjs-modules/mailer";
import { HandlebarsAdapter } from "@nestjs-modules/mailer/dist/adapters/handlebars.adapter";
import { Module } from "@nestjs/common";
import { join } from "path";
import { ConfigModule, ConfigService } from "@nestjs/config";
import { MailService } from "./services/mail.service";
import * as Handlebars from "handlebars";
import * as AWS from 'aws-sdk';
require('dotenv').config();


// ✅ Register Handlebars Helpers Globally Before MailerModule Initialization
Handlebars.registerHelper("ifEquals", function (arg1, arg2, options) {
    return arg1 === arg2 ? options.fn(this) : options.inverse(this);
});

Handlebars.registerHelper("eq", function (a, b) {
    return a === b;
});

@Module({
    imports: [
        ...process.env.NODE_ENV == "production" ? [MailerModule.forRootAsync({
            imports: [ConfigModule],
            useFactory: async (configService: ConfigService) => ({
                transport: {
                    SES: new AWS.SES({
                        apiVersion: '2010-12-01',
                        region: configService.getOrThrow<string>("MAIL_AWS_SES_REGION"),
                        accessKeyId: configService.getOrThrow<string>("MAIL_AWS_ACCESS_KEY_ID"),
                        secretAccessKey: configService.getOrThrow<string>("MAIL_AWS_SECRET_ACCESS_KEY"),
                        maxRetries: 3,
                    }),
                },
                defaults: {
                    from: configService.getOrThrow<string>("MAIL_AWS_FROM_ADDRESS"),
                },
                template: {
                    dir: join(process.cwd(), "templates"),
                    adapter: new HandlebarsAdapter(),
                    options: { strict: true },
                },
            }),
            inject: [ConfigService],
        }),
        ] : [
            MailerModule.forRootAsync({
                imports: [ConfigModule],
                useFactory: async (configService: ConfigService) => ({
                    transport: {
                        host: configService.getOrThrow<string>("MAIL_HOST"),
                        auth: {
                            user: configService.getOrThrow<string>("MAIL_USERNAME"),
                            pass: configService.getOrThrow<string>("MAIL_PASSWORD"),
                        },
                        pool: true,
                        maxConnections: 10,
                        maxMessages: 50,
                        rateLimit: 10,
                        connectionTimeout: 30000,
                        greetingTimeout: 30000,
                        socketTimeout: 30000,
                        retries: 3,
                        port: parseInt(configService.getOrThrow("MAIL_PORT")),
                        secure: parseInt(configService.getOrThrow("MAIL_PORT")) === 465,
                        tls: {
                            rejectUnauthorized: false,
                        },
                    },
                    defaults: {
                        from: configService.getOrThrow<string>("MAIL_FROM_ADDRESS"),
                    },
                    template: {
                        dir: join(process.cwd(), "templates"),
                        adapter: new HandlebarsAdapter({
                            ifEquals: (arg1, arg2, options) => {
                                return arg1 === arg2 ? options.fn(this) : options.inverse(this);
                            },
                        }),
                        options: { strict: true },
                    },
                }),
                inject: [ConfigService],
            }),
        ]],
    providers: [MailService],
    exports: [MailService],
})
export class MailModule { }
