import { <PERSON>p, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';

// Define error interface for type safety and export it
export interface UploadError {
  lineNumber: number;
  message: string;
  field?: string;
  sku?: string;
  value?: string;
  rowData?: Record<string, any>;
  timestamp: Date;
}

@Schema({ timestamps: true })
export class UploadBatch {
  @Prop({ type: Types.ObjectId, required: true })
  organizationId: Types.ObjectId;

  @Prop({ required: true })
  filePath: string;

  @Prop({ default: 0 })
  totalRows: number;

  @Prop({ default: 0 })
  processedRows: number;

  @Prop({ default: 0 })
  successCount: number;

  @Prop({ default: 0 })
  failedCount: number;

  // ⬇️ ONLY these four
  @Prop({
    type: String,
    enum: ['queued', 'processing', 'completed', 'failed'],
    default: 'queued',
  })
  status: 'queued' | 'processing' | 'completed' | 'failed';

  @Prop()
  errorFilePath?: string;

  @Prop({ default: false })
  isUpdate: boolean;

  @Prop()
  initiatedByUserId?: string;

  @Prop()
  message?: string;

  @Prop({ type: Number, default: 0 })
  processedCount: number;

  // Store errors directly in the batch document
  @Prop({
    type: [{
      lineNumber: { type: Number, required: true },
      message: { type: String, required: true },
      field: { type: String },
      sku: { type: String },
      value: { type: String },
      rowData: { type: Object },
      timestamp: { type: Date, default: Date.now }
    }],
    default: []
  })
  errors: UploadError[];
}

export type UploadBatchDocument = HydratedDocument<UploadBatch>;
export const UploadBatchSchema = SchemaFactory.createForClass(UploadBatch);
