import {
  BadRequestException,
  Body,
  Controller,
  Post,
  UseInterceptors,
  Get,
  Param,
  Patch,
  Delete,
  UploadedFile,
  ParseFilePipe,
  FileTypeValidator,
  HttpCode,
  Headers,
  Res,
  Query,
  Inject,
  MaxFileSizeValidator,
  StreamableFile,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { ENUM_ROLE_TYPE } from 'src/role/enums/role.enum';
import { GetUser } from 'src/auth/decorators/get-user.decorator';
import { CreateProductDto } from '../dto/createProduct.dto';
import { ProductService } from '../services/product.service';
import { UpdateProductStatusDto } from '../dto/productupdateStatus.dto';
import { FileInterceptor } from '@nestjs/platform-express';
import { ExportProductsDto } from '../dto/productExport.dto';
import { ExportService } from 'src/utils/services/export.service';
import { AuthJwtAccessProtected } from 'src/auth/decorators/auth.jwt.decorator';
import { PolicyAbilityRoleProtected } from 'src/policy/decorators/policy.decorator';
import { BrandProductListingDto } from '../dto/brand-product-listing.dto';
import * as multer from 'multer';
import { Response } from 'express';

@ApiTags('product')
@ApiBearerAuth()
@Controller('product')
export class ProductController {
  constructor(
    private productService: ProductService,
    private readonly exportService: ExportService,
  ) { }

  @Post('/create')
  @ApiOperation({ summary: 'Create a new Product' })
  @PolicyAbilityRoleProtected(
    ENUM_ROLE_TYPE.ORGANIZATION,
    ENUM_ROLE_TYPE.WEB_MASTER,
    ENUM_ROLE_TYPE.FRONT_DESK_ADMIN,
    ENUM_ROLE_TYPE.TRAINER,
  )
  @AuthJwtAccessProtected()
  async createProduct(@Body() dto: CreateProductDto, @GetUser() user: any) {
    const product = await this.productService.createProduct(dto, user);
    return { message: 'Product Created', data: product };
  }

  @Post('/list')
  @ApiOperation({ summary: 'list of  Product' })
  @PolicyAbilityRoleProtected(
    ENUM_ROLE_TYPE.ORGANIZATION,
    ENUM_ROLE_TYPE.WEB_MASTER,
    ENUM_ROLE_TYPE.FRONT_DESK_ADMIN,
    ENUM_ROLE_TYPE.TRAINER,
  )
  @AuthJwtAccessProtected()
  async ProductListing(@Body() body: any, @GetUser() user: any) {
    const data = await this.productService.getProductsList(body, user);
    return { message: 'Product List', data };
  }

  @Get('/:id')
  @ApiOperation({ summary: 'Get the product Detail' })
  @PolicyAbilityRoleProtected(
    ENUM_ROLE_TYPE.ORGANIZATION,
    ENUM_ROLE_TYPE.WEB_MASTER,
    ENUM_ROLE_TYPE.FRONT_DESK_ADMIN,
    ENUM_ROLE_TYPE.TRAINER,
  )
  @AuthJwtAccessProtected()
  async getProductDetails(@Param('id') id: string) {
    const data = await this.productService.getProductDetails(id);
    return { message: 'Product Details', data };
  }

  @Patch('/:id')
  @ApiOperation({ summary: 'Update product Detail' })
  @PolicyAbilityRoleProtected(
    ENUM_ROLE_TYPE.ORGANIZATION,
    ENUM_ROLE_TYPE.WEB_MASTER,
    ENUM_ROLE_TYPE.FRONT_DESK_ADMIN,
    ENUM_ROLE_TYPE.TRAINER,
  )
  @AuthJwtAccessProtected()
  async updateProduct(
    @Param('id') id: string,
    @Body() dto: CreateProductDto,
    @GetUser() user: any,
  ) {
    const data = await this.productService.updateProduct(id, dto, user);
    return { message: 'Product Updated', data };
  }

  @Patch('/status/:id')
  @ApiOperation({ summary: 'Update product Status ' })
  @PolicyAbilityRoleProtected(
    ENUM_ROLE_TYPE.ORGANIZATION,
    ENUM_ROLE_TYPE.WEB_MASTER,
    ENUM_ROLE_TYPE.FRONT_DESK_ADMIN,
    ENUM_ROLE_TYPE.TRAINER,
  )
  @AuthJwtAccessProtected()
  async updateProductStatus(
    @Param('id') id: string,
    @Body() dto: UpdateProductStatusDto,
  ): Promise<any> {
    return {
      message: 'Product status updated',
      data: await this.productService.updateProductStatus(id, dto),
    };
  }

  @Delete('/:id')
  @ApiOperation({ summary: 'Delete product' })
  @PolicyAbilityRoleProtected(
    ENUM_ROLE_TYPE.ORGANIZATION,
    ENUM_ROLE_TYPE.WEB_MASTER,
    ENUM_ROLE_TYPE.FRONT_DESK_ADMIN,
    ENUM_ROLE_TYPE.TRAINER,
  )
  @AuthJwtAccessProtected()
  async deleteProductDetails(@Param('id') id: string) {
    const data = await this.productService.deleteProduct(id);
    return { message: 'Product Delete Successfully', data };
  }

  // ────────────────────────────────────────────────────────────────
  // BULK UPLOAD (CREATE) — CSV → Direct Processing
  // ────────────────────────────────────────────────────────────────
  @Post('/bulkUpload')
  @UseInterceptors(
    FileInterceptor('productFile', { storage: multer.memoryStorage() }),
  )
  @HttpCode(202)
  @PolicyAbilityRoleProtected(
    ENUM_ROLE_TYPE.ORGANIZATION,
    ENUM_ROLE_TYPE.WEB_MASTER,
    ENUM_ROLE_TYPE.FRONT_DESK_ADMIN,
    ENUM_ROLE_TYPE.TRAINER,
  )
  @AuthJwtAccessProtected()
  async bulkProductUpload(
    @Body() body: any,
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new FileTypeValidator({
            fileType:
              /^(text\/csv|application\/csv|application\/vnd\.ms-excel|text\/plain)$/i,
          }),
          new MaxFileSizeValidator({ maxSize: 100 * 1024 * 1024 }),
        ],
      }),
    )
    file: Express.Multer.File,
    @GetUser() user: any,
    @Res() res: Response,
  ) {
    if (!file?.buffer?.length) throw new BadRequestException('No CSV received');
    const isUpdate = String(body?.mode ?? '').toLowerCase() === 'update';

    // 1) Create batch as queued
    const batch = await this.productService.createUploadBatch({
      user,
      filePath: 'memory',
      isUpdate,
    });

    // 2) Process CSV buffer directly in background
    const buf = file.buffer; // copy to avoid accidental mutation
    void this.productService.processCsvBufferInBackground({
      buffer: buf,
      user,
      isUpdate,
      batchId: String(batch._id),
    });

    // 3) Immediately return 202 with status URL
    return res.status(202).json({
      message: 'Upload accepted and processing in background',
      batchId: batch._id,
      statusUrl: `/product/bulkUpload/status/${batch._id}`,
    });
  }

  // ────────────────────────────────────────────────────────────────
  // BULK UPDATE — CSV → Direct Processing
  // ────────────────────────────────────────────────────────────────
  @Post('/bulkUpdate')
  @UseInterceptors(
    FileInterceptor('productFile', { storage: multer.memoryStorage() }),
  )
  @ApiOperation({
    summary: 'Bulk product UPDATE (CSV via Direct Processing)',
  })
  @PolicyAbilityRoleProtected(
    ENUM_ROLE_TYPE.ORGANIZATION,
    ENUM_ROLE_TYPE.WEB_MASTER,
    ENUM_ROLE_TYPE.FRONT_DESK_ADMIN,
    ENUM_ROLE_TYPE.TRAINER,
  )
  @AuthJwtAccessProtected()
  @HttpCode(202)
  async bulkUpdateProducts(
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new FileTypeValidator({
            fileType:
              /^(text\/csv|application\/csv|application\/vnd\.ms-excel|text\/plain)$/i,
          }),
          new MaxFileSizeValidator({ maxSize: 100 * 1024 * 1024 }),
        ],
      }),
    )
    file: Express.Multer.File,
    @GetUser() user: any,
    @Res() res: Response,
  ) {
    if (!file?.buffer?.length) throw new BadRequestException('No CSV received');

    // 1) Create batch as queued
    const batch = await this.productService.createUploadBatch({
      user,
      filePath: 'memory',
      isUpdate: true,
    });

    // 2) Process CSV buffer directly in background
    const buf = file.buffer; // copy to avoid accidental mutation
    void this.productService.processCsvBufferInBackground({
      buffer: buf,
      user,
      isUpdate: true,
      batchId: String(batch._id),
    });

    // 3) Immediately return 202 with status URL
    return res.status(202).json({
      message: 'Update accepted and processing in background',
      batchId: batch._id,
      statusUrl: `/product/bulkUpload/status/${batch._id}`,
    });
  }

  @Post('/export')
  @HttpCode(200)
  @ApiOperation({ summary: 'Export Products' })
  @PolicyAbilityRoleProtected(
    ENUM_ROLE_TYPE.ORGANIZATION,
    ENUM_ROLE_TYPE.WEB_MASTER,
    ENUM_ROLE_TYPE.FRONT_DESK_ADMIN,
    ENUM_ROLE_TYPE.TRAINER,
  )
  @AuthJwtAccessProtected()
  async exportProductList(
    @Headers('X-Timezone') userTimezone: string,
    @GetUser() user: any,
    @Body() body: ExportProductsDto,
  ): Promise<any> {
    userTimezone =
      userTimezone || Intl.DateTimeFormat().resolvedOptions().timeZone;
    const products = await this.productService.getProductsForExport(
      user,
      body.productType,
      body.startDate,
      body.endDate,
    );
    if (!products.length) return { message: 'No products found to export', data: [] };

    const buffer = await this.exportService.generateExportFile(
      products,
      body.fileType,
    );

    if (body.responseType === 'stream') {
      const contentType = this.exportService.getMimeType(body.fileType);
      const extension = body.fileType;
      const fileName = `products-${Date.now()}.${extension}`;
      return new StreamableFile(new Uint8Array(buffer), {
        type: contentType,
        disposition: `attachment; filename=${fileName}`,
      });
    }
    return { message: 'Products fetched successfully', data: products };
  }

  @Post('/brand-product-list')
  @ApiOperation({ summary: 'List of Product' })
  @PolicyAbilityRoleProtected(
    ENUM_ROLE_TYPE.ORGANIZATION,
    ENUM_ROLE_TYPE.WEB_MASTER,
    ENUM_ROLE_TYPE.FRONT_DESK_ADMIN,
    ENUM_ROLE_TYPE.TRAINER,
  )
  @AuthJwtAccessProtected()
  async BrandProductListing(
    @Body() body: BrandProductListingDto,
    @GetUser() user: any,
  ) {
    const data = await this.productService.bulkProductListing(body, user);
    return { message: 'Product List', data };
  }

  @Get('/bulkUpload/status/:batchId')
  @ApiOperation({ summary: 'Get bulk upload status' })
  @PolicyAbilityRoleProtected(
    ENUM_ROLE_TYPE.ORGANIZATION,
    ENUM_ROLE_TYPE.WEB_MASTER,
    ENUM_ROLE_TYPE.FRONT_DESK_ADMIN,
    ENUM_ROLE_TYPE.TRAINER,
  )
  @AuthJwtAccessProtected()
  async getUploadStatus(@Param('batchId') batchId: string) {
    const doc = await this.productService.getUploadBatch(batchId);
    if (!doc) throw new BadRequestException('Invalid batchId');

    // Use database values directly (no Redis dependency)
    const total = Number(doc.totalRows || 0);
    const processed = Number(doc.processedCount || 0);
    const success = Number(doc.successCount || 0);
    const failed = Number(doc.failedCount || 0);
    const done = processed;

    // Compute progress %
    const pct = total > 0 ? Math.min(100, Math.round((processed / total) * 100)) : 0;

    // Build response from database values only
    const response: any = {
      ...doc,
      computed: {
        total,
        processed,
        success,
        failed,
        pending: Math.max(0, total - processed),
        progressPct: pct
      },
    };

    // Include error summary if upload is completed
    if (doc.status === 'completed' || doc.status === 'failed') {
      const errorCount = (doc.errors && doc.errors.length) || doc.failedCount || 0;
      response.errorSummary = {
        hasErrors: errorCount > 0,
        errorCount: errorCount,
        errorPreviewUrl: errorCount > 0 ? `/product/bulkUpload/errors/${batchId}/preview` : null,
        errorDownloadUrl: errorCount > 0 ? `/product/bulkUpload/errors/${batchId}/download` : null,
      };
    }

    return response;
  }


@Get('/bulkUpload/errors/:batchId/preview')
@ApiOperation({ summary: 'Bulk upload errors (JSON preview from Database)' })
@PolicyAbilityRoleProtected(
  ENUM_ROLE_TYPE.ORGANIZATION,
  ENUM_ROLE_TYPE.WEB_MASTER,
  ENUM_ROLE_TYPE.FRONT_DESK_ADMIN,
  ENUM_ROLE_TYPE.TRAINER,
)
@AuthJwtAccessProtected()
async getUploadErrorsPreview(
  @Param('batchId') batchId: string,
  @Query('offset') offset = '0',
  @Query('limit') limit = '200',
) {
  const off = Math.max(0, Number(offset) || 0);
  const lim = Math.max(1, Math.min(1000, Number(limit) || 200));

  return await this.productService.getBatchErrors(batchId, off, lim);
}



  @Get('/bulkUpload/errors/:batchId/download')
  @ApiOperation({ summary: 'Download bulk upload errors as CSV (from Database)' })
  @PolicyAbilityRoleProtected(
    ENUM_ROLE_TYPE.ORGANIZATION,
    ENUM_ROLE_TYPE.WEB_MASTER,
    ENUM_ROLE_TYPE.FRONT_DESK_ADMIN,
    ENUM_ROLE_TYPE.TRAINER,
  )
  @AuthJwtAccessProtected()
  async downloadUploadErrors(
    @Param('batchId') batchId: string,
    @Res() res: Response,
  ) {
    // Get all errors from database (no pagination for download)
    const errorData = await this.productService.getBatchErrors(batchId, 0, 10000);
    const rows = errorData.data;

    const headers = ['lineNumber', 'message', 'field', 'sku', 'value', 'timestamp'];
    const esc = (v: any) => {
      const s = v == null ? '' : String(v);
      return /[",\n]/.test(s) ? `"${s.replace(/"/g, '""')}"` : s;
    };

    let csv = headers.join(',') + '\n';
    for (const r of rows) {
      csv +=
        [
          esc(r.lineNumber),
          esc(r.message),
          esc(r.field),
          esc(r.sku),
          esc(r.value),
          esc(r.timestamp),
        ].join(',') + '\n';
    }

    const fileName = `bulk-errors-${batchId}.csv`;
    res.setHeader('Content-Type', 'text/csv; charset=utf-8');
    res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
    res.send(csv);
  }

}
