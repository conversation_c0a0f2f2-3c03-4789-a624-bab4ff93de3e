import { Body, Controller, Delete, Get, Param, Patch, Post, Query } from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiParam, ApiProperty, ApiResponse, ApiTags } from "@nestjs/swagger";
import { GetDelegatedUser } from "src/auth/decorators/get-user.decorator";
import { IUserDocument } from "src/users/interfaces/user.interface";
import { PolicyAbilityProtected } from "src/policy/decorators/policy.decorator";
import { ENUM_PERMISSION_TYPE } from "src/policy/enums/policy.permissions.enum";
import { AuthJwtAccessProtected, AuthSessionProtected } from "src/auth/decorators/auth.jwt.decorator";
import { GetOrganizationId } from "../decorators/organization.decorator";
import { IDatabaseObjectId } from "src/common/database/interfaces/database.objectid.interface";
import { VoucherCreateDto } from "../dto/create-voucher.dto";
import { VoucherService } from "../services/voucher.service";
import { Response, ResponsePaging } from "src/common/response/decorators/response.decorator";
import { VoucherStatusUpdateDto, VoucherUpdateDto } from "../dto/update-voucher.dto";
import { PaginationQuery } from "src/common/pagination/decorators/pagination.decorator";
import { ENUM_PAGINATION_ORDER_DIRECTION_TYPE } from "src/utils/decorators/pagination-query.decorator";
import { PaginationListDto } from "src/common/pagination/dtos/pagination.list.dto";
import { VoucherListDto } from "../dto/list-voucher.dto";
import { PaginationService } from "src/common/pagination/services/pagination.service";
import { MongoIdPipeTransform } from "src/common/database/pipes/mongo-id.pipe";
import { ENUM_PRODUCT_ITEM_TYPE } from "src/promotions/enums/item-type.enum";
import { VoucherResponseDto } from "../dto/detail-voucher.response.dto";
import { IResponse, IResponsePaging } from "src/common/response/interfaces/response.interface";
import moment from "moment";
import { VoucherPurchasedResponseDto } from "../dto/purchased-voucher.response.dto";
import { ShareVoucherDto } from "src/users/dto/share-pass.dto";
import { VoucherRedemptionDto, VoucherHistoryResponseDto, VoucherHistoryListDto } from "../dto/voucher-redemption.dto";
import { GetFacilities } from "src/facility/decorators/get.facility.decorators";
import { FacilityProtected } from "src/facility/decorators/user.facility.decorator";
import { BadRequestException } from "@nestjs/common";
import { Types } from "mongoose";

@ApiTags("modules.voucher")
@ApiBearerAuth()
@Controller("voucher")
export class VoucherController {
    constructor(
        private readonly voucherService: VoucherService,
        private readonly paginationService: PaginationService,
    ) { }

    @ApiOperation({ summary: "Create a new Voucher" })
    @ApiResponse({ status: 201, description: "Voucher created successfully", type: VoucherResponseDto })
    @Response("voucher.create")
    @Post('/create')
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.VOUCHER_WRITE)
    @AuthSessionProtected()
    @AuthJwtAccessProtected()
    async createVoucher(
        @GetDelegatedUser() delegateUser: IUserDocument,
        @Body() body: VoucherCreateDto,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ): Promise<IResponse<any>> {
        const data = await this.voucherService.createVoucher(body, delegateUser?._id, organizationId);
        return {
            data
        };
    }

    @ApiOperation({ summary: "Update Voucher" })
    @ApiResponse({ status: 200, description: "Voucher updated successfully", type: VoucherResponseDto })
    @Response("voucher.update")
    @Patch('/update')
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.VOUCHER_UPDATE)
    @AuthSessionProtected()
    @AuthJwtAccessProtected()
    async updateVoucher(
        @GetDelegatedUser() delegateUser: IUserDocument,
        @Body() body: VoucherUpdateDto,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ): Promise<any> {
        const data = await this.voucherService.updateVoucher(body, delegateUser?._id, organizationId);
        return {
            data
        };
    }

    @ApiOperation({ summary: "Voucher List" })
    @ApiResponse({ status: 200, description: "Returns the voucher list", type: [VoucherResponseDto] })
    @ResponsePaging("voucher.list")
    @Get('/list')
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.VOUCHER_READ)
    @AuthJwtAccessProtected()
    async voucherList(
        @PaginationQuery({
            defaultOrderBy: 'updatedAt',
            defaultOrderDirection: ENUM_PAGINATION_ORDER_DIRECTION_TYPE.DESC,
            availableOrderBy: ['updatedAt'],
            availableSearch: ['name']
        }) { _limit, _offset, _order, _search }: PaginationListDto,
        @Query() query: VoucherListDto,
        @Body() body: any,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ): Promise<IResponsePaging<VoucherResponseDto>> {
        const filter: any = {
            organizationId: organizationId,
            itemType: ENUM_PRODUCT_ITEM_TYPE.VOUCHER,
            ..._search
        };
        if (body.isActive !== undefined) filter.isActive = body.isActive;

        const data = await this.voucherService.voucherList(filter, {
            paging: {
                limit: _limit,
                offset: _offset,
            },
            order: _order,
        });
        return {
            _pagination: {
                total: data.count,
                totalPage: this.paginationService.totalPage(data.count, _limit),
                pageSize: query.pageSize,
                page: query.page,
            },
            data: data.list,
        };
    }

    @ApiOperation({ summary: "User's purchase Voucher List" })
    @ApiParam({ name: 'userId', description: 'User ID', type: 'string', required: true })
    @ResponsePaging("voucher.list")
    @Get(['/:userId/list'])
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.VOUCHER_READ)
    @AuthJwtAccessProtected()
    async usersActiveVoucherList(
        @PaginationQuery({
            defaultOrderBy: 'updatedAt',
            defaultOrderDirection: ENUM_PAGINATION_ORDER_DIRECTION_TYPE.DESC,
            availableOrderBy: ['updatedAt'],
            availableSearch: ['name']
        }) { _limit, _offset, _order, _search, page, pageSize }: PaginationListDto,
        // @Query() query: VoucherListDto,
        @Param("userId", MongoIdPipeTransform) userId: IDatabaseObjectId,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ): Promise<IResponsePaging<VoucherPurchasedResponseDto>> {
        const filter: any = {
            organizationId: organizationId,
            userId: userId,
            itemType: ENUM_PRODUCT_ITEM_TYPE.VOUCHER,
            isExpired: false,
            isActive: true,
        };

        if (_search) filter.search = _search;

        const data = await this.voucherService.usersVoucherList(filter, {
            paging: {
                limit: _limit,
                offset: _offset,
            },
            order: _order,
        });

        return {
            _pagination: {
                total: data.count,
                totalPage: this.paginationService.totalPage(data.count, _limit),
                pageSize: pageSize,
                page: page,
            },
            data: data.list,
        };
    }


    @ApiOperation({ summary: "User's purchase Voucher List" })
    @ApiParam({ name: 'userId', description: 'User ID', type: 'string', required: true })
    @ResponsePaging("voucher.list")
    @Get(['/purchased/:userId/list'])
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.VOUCHER_READ)
    @AuthJwtAccessProtected()
    async usersVoucherList(
        @PaginationQuery({
            defaultOrderBy: 'updatedAt',
            defaultOrderDirection: ENUM_PAGINATION_ORDER_DIRECTION_TYPE.DESC,
            availableOrderBy: ['updatedAt'],
            availableSearch: ['name']
        }) { _limit, _offset, _order, _search }: PaginationListDto,
        @Query() query: VoucherListDto,
        @Param("userId", MongoIdPipeTransform) userId: IDatabaseObjectId,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ): Promise<IResponsePaging<VoucherPurchasedResponseDto>> {
        const filter: any = {
            organizationId: organizationId,
            userId: userId,
            itemType: ENUM_PRODUCT_ITEM_TYPE.VOUCHER,
        };
        if (query.isExpired !== undefined) filter.isExpired = query.isExpired;

        if (query.isActive !== undefined) filter.isActive = query.isActive;

        if (query.search) filter.search = _search;

        const data = await this.voucherService.usersVoucherList(filter, {
            paging: {
                limit: _limit,
                offset: _offset,
            },
            order: _order,
        });

        return {
            _pagination: {
                total: data.count,
                totalPage: this.paginationService.totalPage(data.count, _limit),
                pageSize: query.pageSize,
                page: query.page,
            },
            data: data.list,
        };
    }


    @ApiOperation({ summary: "User's purchase Voucher List" })
    @ResponsePaging("voucher.list")
    @Get('/:userId/list')
    async usersVoucherListForUser(
        @PaginationQuery({
            defaultOrderBy: 'updatedAt',
            defaultOrderDirection: ENUM_PAGINATION_ORDER_DIRECTION_TYPE.DESC,
            availableOrderBy: ['updatedAt'],
            availableSearch: ['name']
        }) { _limit, _offset, _order, _search }: PaginationListDto,
        @Query() query: VoucherListDto,
    ): Promise<IResponsePaging<VoucherPurchasedResponseDto>> {

        return {
            _pagination: {
                total: 0,
                totalPage: 0,
                pageSize: query.pageSize,
                page: query.page,
            },
            data: [],
        };
    }

    @Response("voucher.details")
    @ApiOperation({ summary: "Voucher Details" })
    @ApiParam({ name: 'voucherId', description: 'Voucher ID', example: "GRB4G2U4HFB49NR", type: 'string', required: true })
    @ApiResponse({ status: 200, description: "Returns the voucher details", type: VoucherPurchasedResponseDto })
    @Response("voucher.details")
    @Get('/purchased/:voucherId/get')
    @AuthJwtAccessProtected()
    async voucherPurchasedDetails(
        @Param("voucherId") voucherId: string,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ): Promise<IResponse<VoucherPurchasedResponseDto>> {
        const filter = {
            $or: [
                { _id: voucherId },
                { voucherCode: voucherId }
            ],
            organizationId: organizationId,
            itemType: ENUM_PRODUCT_ITEM_TYPE.VOUCHER
        }
        const data = await this.voucherService.voucherPurchasedDetails(filter);
        return {
            data
        };
    }

    @ApiOperation({ summary: "Share Voucher" })
    @Response("Voucher shared successfully")
    @Patch("/purchased/share")
    @AuthJwtAccessProtected()
    async shareVoucher(
        @Body() shareVoucher: ShareVoucherDto,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ): Promise<any> {
        let output = await this.voucherService.shareVoucher(shareVoucher, organizationId);
        return {
            data: output
        }
    }

    @ApiOperation({ summary: "Voucher Details" })
    @ApiParam({ name: 'voucherId', description: 'Voucher ID', example: "659d268dee4b6081dacd41fd", type: 'string', required: true })
    @ApiResponse({ status: 200, description: "Returns the voucher details", type: VoucherResponseDto })
    @Response("voucher.details")
    @Get('/:voucherId/get')
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.VOUCHER_READ)
    @AuthJwtAccessProtected()
    async voucherDetails(
        @Param("voucherId", MongoIdPipeTransform) voucherId: IDatabaseObjectId,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ): Promise<IResponse<VoucherResponseDto>> {
        const data = await this.voucherService.voucherDetails(voucherId, organizationId);
        return {
            data
        };
    }


    @ApiOperation({ summary: "Voucher Status Update" })
    @ApiParam({ name: 'voucherId', description: 'Voucher ID', example: "659d268dee4b6081dacd41fd", type: 'string', required: true })
    @ApiResponse({ status: 200, description: "Returns the voucher details", type: VoucherResponseDto })
    @Response("voucher.status")
    @Patch('/:voucherId/status')
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.VOUCHER_UPDATE)
    @AuthJwtAccessProtected()
    async voucherStatusUpdate(
        @Param("voucherId", MongoIdPipeTransform) voucherId: IDatabaseObjectId,
        @Body() body: VoucherStatusUpdateDto,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ): Promise<IResponse<any>> {
        const data = await this.voucherService.voucherStatusUpdate(voucherId, !!body.isActive, organizationId);
        return {
            data
        };
    }

    @ApiOperation({ summary: "Voucher Delete" })
    @ApiParam({ name: 'voucherId', description: 'Voucher ID', example: "659d268dee4b6081dacd41fd", type: 'string', required: true })
    @ApiResponse({ status: 204, description: "Returns the voucher details", type: Boolean })
    @Response("voucher.delete")
    @Delete('/:voucherId/delete')
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.VOUCHER_DELETE)
    @AuthJwtAccessProtected()
    async voucherDelete(
        @Param("voucherId", MongoIdPipeTransform) voucherId: IDatabaseObjectId,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ): Promise<IResponse<any>> {
        const data = await this.voucherService.voucherDelete(voucherId, organizationId);
        return {
            data
        };
    }

    @ApiOperation({ summary: "Get Voucher Redemption History" })
    @ApiParam({
        name: 'voucherId',
        description: 'Voucher ID',
        example: '659d268dee4b6081dacd41fd',
        type: 'string',
        required: true
    })
    @ApiResponse({ status: 200, description: "Returns the voucher redemption history", type: [VoucherHistoryResponseDto] })
    @ResponsePaging("voucher.history")
    @Get('/:voucherId/history')
    @PolicyAbilityProtected()
    @AuthJwtAccessProtected()
    async getVoucherHistory(
        @PaginationQuery({
            defaultOrderBy: 'createdAt',
            defaultOrderDirection: ENUM_PAGINATION_ORDER_DIRECTION_TYPE.ASC,
            availableOrderBy: ['createdAt'],
        }) { _limit, _offset, _order }: VoucherHistoryListDto,
        @Param("voucherId", MongoIdPipeTransform) voucherIdentifier: IDatabaseObjectId,
        @Query() query: VoucherHistoryListDto,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ): Promise<IResponsePaging<VoucherHistoryResponseDto>> {
        const filter = {
            purchaseId: voucherIdentifier,
            organizationId: organizationId,
        }
        const { list, total } = await this.voucherService.getVoucherHistory(filter, { paging: { limit: _limit, offset: _offset }, order: _order });
        return {
            _pagination: {
                total: total,
                totalPage: this.paginationService.totalPage(total, _limit),
                pageSize: query.pageSize,
                page: query.page,
            },
            data: list,
        };
    }


}
