import { forwardRef, Module } from "@nestjs/common";
import { UploadService } from "./services/upload.service";
import { TransactionService } from "./services/transaction.service";
import { GeneralService } from "./services/general.service";
import { GeneralController } from "./controller/general.controller";
import { HttpModule } from "@nestjs/axios";
import { AuthModule } from "src/auth/auth.module";
import { MongooseModule } from "@nestjs/mongoose";
import { StateSchema } from "./schemas/state.schema";
import { Cities, CitiesSchema } from "./schemas/cities.schema";
import { DATABASE_PRIMARY_CONNECTION_NAME } from "src/common/database/constants/database.constant";
import { CsvParserService } from "./services/csv-parser.service";
import { ExportService } from "./services/export.service";
import { ActiveTimeFrameService } from "./services/active-time-frame.service";
import { InventoryExportService } from "./services/inventory-export.service";
import { InventoryTemplateExportService } from "./services/inventory-template-export.service";
import { GenerateWavierPdfService } from "./services/generateWavierpdf.service";

@Module({
    imports: [
            forwardRef(() => AuthModule),
        HttpModule,
        UtilsModule,
        MongooseModule.forFeature([
            { name: 'State', schema: StateSchema },
            { name: Cities.name, schema: CitiesSchema }],
            DATABASE_PRIMARY_CONNECTION_NAME
        )

    ],

    providers: [UploadService, TransactionService, GeneralService, CsvParserService, ExportService, ActiveTimeFrameService,InventoryExportService,InventoryTemplateExportService,GenerateWavierPdfService],
    exports: [UploadService, TransactionService, GeneralService, CsvParserService, ExportService, ActiveTimeFrameService,InventoryExportService,InventoryTemplateExportService,GenerateWavierPdfService],
    controllers: [GeneralController],

})
export class UtilsModule {}
