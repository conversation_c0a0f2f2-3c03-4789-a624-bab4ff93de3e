// src/utils/services/generateWavierpdf.service.ts

import { Injectable } from '@nestjs/common';
import * as Handlebars from 'handlebars';
import * as puppeteer from 'puppeteer';
import * as fs from 'fs';
import * as path from 'path';
import { UploadService } from './upload.service';

interface BrowserPool {
    browser: puppeteer.Browser;
    inUse: boolean;
    lastUsed: number;
}

@Injectable()
export class GenerateWavierPdfService {
    private compiledTemplate: HandlebarsTemplateDelegate | null = null;
    private templateCompiling: Promise<HandlebarsTemplateDelegate> | null = null;
    
    // Browser pool for handling concurrent requests
    private browserPool: BrowserPool[] = [];
    private readonly maxBrowsers = 3;
    private readonly browserTimeout = 30000; // 30 seconds
    private browserCreationLock: Promise<puppeteer.Browser> | null = null;

    constructor(private readonly uploadService: UploadService) {
        this.registerHandlebarsHelpers();
        
        // Cleanup idle browsers periodically
        setInterval(() => {
            this.cleanupIdleBrowsers();
        }, 60000); // Check every minute
    }

    private registerHandlebarsHelpers(): void {
        Handlebars.registerHelper('ordinalNumber', function(index: number) {
            const ordinals = ['First', 'Second', 'Third', 'Fourth', 'Fifth', 'Sixth', 'Seventh', 'Eighth', 'Ninth', 'Tenth'];
            return ordinals[index] || `${index + 1}th`;
        });

        Handlebars.registerHelper('gt', function(a: number, b: number) {
            return a > b;
        });

        Handlebars.registerHelper('formatDate', function(mongoDate: any) {
            if (!mongoDate) return '';
            
            let date;
            if (mongoDate.$date) {
                date = new Date(mongoDate.$date);
            } else if (typeof mongoDate === 'string') {
                date = new Date(mongoDate);
            } else {
                date = mongoDate;
            }
            
            return date.toLocaleString('en-GB', {
                day: '2-digit',
                month: 'short',
                year: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
            }).replace(',', '');
        });

        Handlebars.registerHelper('ifExists', function(value: any, options: any) {
            if (value && value !== '') {
                return options.fn(this);
            }
            return options.inverse(this);
        });
    }

    // Thread-safe template initialization
    private async initializeTemplate(): Promise<HandlebarsTemplateDelegate> {
        if (this.compiledTemplate) {
            return this.compiledTemplate;
        }

        // If another request is already compiling, wait for it
        if (this.templateCompiling) {
            return this.templateCompiling;
        }

        try {
            this.templateCompiling = this.compileTemplate();
            this.compiledTemplate = await this.templateCompiling;
            return this.compiledTemplate;
        } finally {
            this.templateCompiling = null;
        }
    }

    private async compileTemplate(): Promise<HandlebarsTemplateDelegate> {
        try {
            const templatePath = path.join(process.cwd(), 'templates', 'wavier-form.hbs');
            const templateSource = fs.readFileSync(templatePath, 'utf8');
            return Handlebars.compile(templateSource);
        } catch (error) {
            console.error('Error loading template:', error);
            throw new Error('Failed to load PDF template');
        }
    }

    // Transform Mongoose document to a completely clean object
    private transformLeadDataForPDF(leadData: any): any {
        const cleanData = JSON.parse(JSON.stringify(leadData));
        
        return {
            _id: cleanData._id,
            firstName: cleanData.firstName || '',
            lastName: cleanData.lastName || '',
            email: cleanData.email || '',
            phone: cleanData.phone || '',
            dob: cleanData.dob || '',
            address: {
                street: cleanData.address?.street || '',
                addressLine1: cleanData.address?.addressLine1 || '',
                city: cleanData.address?.city || '',
                state: cleanData.address?.state || '',
                country: cleanData.address?.country || '',
                postalCode: cleanData.address?.postalCode || '',
            },
            minors: cleanData.minors || [],
            rawZohoData: {
                age_consent: cleanData.rawZohoData?.age_consent || 'Agreed',
                Consent: cleanData.rawZohoData?.Consent || 'Agreed',
                electronic_consent: cleanData.rawZohoData?.electronic_consent || 'Agreed',
                ...cleanData.rawZohoData
            },
            signature: cleanData.signature || '',
            createdAt: cleanData.createdAt,
            updatedAt: cleanData.updatedAt,
        };
    }

    private async generateHTML(leadData: any): Promise<string> {
        try {
            const template = await this.initializeTemplate();
            const cleanData = this.transformLeadDataForPDF(leadData);
            
            return template(cleanData);
        } catch (error) {
            console.error('Error generating HTML:', error);
            throw new Error('Failed to generate HTML from template');
        }
    }

    // Thread-safe browser pool management
    private async getBrowser(): Promise<puppeteer.Browser> {
        // Try to find an available browser
        const availableBrowser = this.browserPool.find(pool => !pool.inUse && pool.browser.connected);
        
        if (availableBrowser) {
            availableBrowser.inUse = true;
            availableBrowser.lastUsed = Date.now();
            return availableBrowser.browser;
        }

        // Create new browser if pool isn't full
        if (this.browserPool.length < this.maxBrowsers) {
            return this.createNewBrowser();
        }

        // Wait for an available browser
        return this.waitForAvailableBrowser();
    }

    private async createNewBrowser(): Promise<puppeteer.Browser> {
        try {
            
            const browser = await puppeteer.launch({
                headless: true,
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--disable-gpu',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor',
                    '--disable-background-timer-throttling',
                    '--disable-backgrounding-occluded-windows',
                    '--disable-renderer-backgrounding',
                ],
                timeout: 60000,
            });

            // Add to pool
            this.browserPool.push({
                browser,
                inUse: true,
                lastUsed: Date.now()
            });

            return browser;

        } catch (error) {
            console.error('Failed to create browser:', error);
            throw new Error(`Browser launch failed: ${error.message}`);
        }
    }

    private async waitForAvailableBrowser(): Promise<puppeteer.Browser> {
        const maxWaitTime = 30000; // 30 seconds
        const checkInterval = 500; // 500ms
        const startTime = Date.now();

        while (Date.now() - startTime < maxWaitTime) {
            const availableBrowser = this.browserPool.find(pool => !pool.inUse && pool.browser.connected);
            
            if (availableBrowser) {
                availableBrowser.inUse = true;
                availableBrowser.lastUsed = Date.now();
                return availableBrowser.browser;
            }

            await new Promise(resolve => setTimeout(resolve, checkInterval));
        }

        throw new Error('Timeout waiting for available browser');
    }

    private releaseBrowser(browser: puppeteer.Browser): void {
        const poolItem = this.browserPool.find(pool => pool.browser === browser);
        if (poolItem) {
            poolItem.inUse = false;
            poolItem.lastUsed = Date.now();
        }
    }

    private async cleanupIdleBrowsers(): Promise<void> {
        const now = Date.now();
        const idleThreshold = 5 * 60 * 1000; // 5 minutes

        for (let i = this.browserPool.length - 1; i >= 0; i--) {
            const poolItem = this.browserPool[i];
            
            // Remove idle or disconnected browsers
            if (!poolItem.inUse && (now - poolItem.lastUsed > idleThreshold || !poolItem.browser.connected)) {
                try {
                    if (poolItem.browser.connected) {
                        await poolItem.browser.close();
                    }
                } catch (error) {
                    console.warn('Error closing idle browser:', error.message);
                }
                
                this.browserPool.splice(i, 1);
            }
        }
    }

    private async generatePDFBuffer(html: string): Promise<Buffer> {
        let browser: puppeteer.Browser | null = null;
        let page: puppeteer.Page | null = null;
        
        try {
            browser = await this.getBrowser();
            page = await browser.newPage();
            

            await page.setDefaultTimeout(60000);
            await page.setViewport({ width: 1200, height: 800 });
            
            await page.setContent(html, {
                waitUntil: ['load'],
                timeout: 60000
            });
            

            const pdfBuffer = await page.pdf({
                format: 'A4',
                printBackground: true,
                margin: {
                    top: '20px',
                    right: '20px',
                    bottom: '20px',
                    left: '20px'
                }
            });

            return Buffer.from(pdfBuffer);

        } catch (error) {
            console.error('Error generating PDF:', error);
            throw new Error(`Failed to generate PDF: ${error.message}`);
        } finally {
            // Always close the page
            if (page) {
                try {
                    await page.close();
                } catch (closeError) {
                    console.warn('Error closing page:', closeError.message);
                }
            }
            
            // Release browser back to pool
            if (browser) {
                this.releaseBrowser(browser);
            }
        }
    }

    private async uploadPDFToS3(pdfBuffer: Buffer, leadId: string): Promise<string> {
        try {
            const s3Path = "pdfs/client-wavier/";
            const fileName = `client-wavier-${leadId}.pdf`;
            const mimeType = 'application/pdf';

            const s3UploadResponse = await this.uploadService.uploadPdf(
                pdfBuffer,
                s3Path,
                fileName,
                mimeType
            );

            return s3UploadResponse.Location;
        } catch (error) {
            console.error('Error uploading PDF to S3:', error);
            throw new Error('Failed to upload PDF to S3');
        }
    }

    async generateClientPDF(leadData: any): Promise<string> {
        const startTime = Date.now();
        
        try {
            
            const html = await this.generateHTML(leadData);
            
            const pdfBuffer = await this.generatePDFBuffer(html);
            
            const pdfUrl = await this.uploadPDFToS3(pdfBuffer, leadData._id);
            
            const duration = Date.now() - startTime;
            
            return pdfUrl;
            
        } catch (error) {
            const duration = Date.now() - startTime;
            console.error(`PDF generation failed for lead ${leadData._id} after ${duration}ms:`, error);
            throw error;
        }
    }

    async generateClientPDFBuffer(leadData: any): Promise<Buffer> {
        try {
            
            const html = await this.generateHTML(leadData);
            const pdfBuffer = await this.generatePDFBuffer(html);
            
            return pdfBuffer;
            
        } catch (error) {
            console.error('Error generating PDF buffer:', error);
            throw error;
        }
    }

    clearTemplateCache(): void {
        this.compiledTemplate = null;
    }

    // Graceful shutdown
    async onModuleDestroy() {
        
        for (const poolItem of this.browserPool) {
            try {
                if (poolItem.browser.connected) {
                    await poolItem.browser.close();
                }
            } catch (error) {
                console.warn('Error during shutdown:', error.message);
            }
        }
        
        this.browserPool = [];
    }
}