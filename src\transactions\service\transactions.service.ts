import { BadRequestException, ConsoleLogger, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, PipelineStage, Types } from 'mongoose';
import { UserDocument } from 'src/users/schemas/user.schema';
import { StaffProfileDetails } from 'src/staff/schemas/staff.schema';
import { Facility } from 'src/facility/schemas/facility.schema';
import { CreateReconciliationDto } from '../dto/create-reconciliation.dto';
import { Reconciliation } from '../schema/reconciliation.schema';
import { GetReconciliationDto } from '../dto/get-reconciliation.dto';
import { Purchase, PurchaseDocument } from 'src/users/schemas/purchased-packages.schema';
import { Invoice } from 'src/users/schemas/invoice.schema';
import { PaymentStatus } from 'src/utils/enums/payment.enum';
import { PaymentMethod } from 'src/utils/enums/paymentMethod.enum';
import { InvoiceStatus } from 'src/utils/enums/invoice-status.enum';
import { GetReconciliationResponse } from '../interfaces/get-reconciliation.response';
import { IUserDocument } from 'src/users/interfaces/user.interface';
import { ENUM_ROLE_TYPE } from 'src/role/enums/role.enum';
import { PaymentMethodSchema } from 'src/paymentMethod/schemas/payment-method.schema';
import { IDatabaseObjectId } from 'src/common/database/interfaces/database.objectid.interface';
import { ZoutHistoryDTO } from '../dto/get-zout-history.dto';

@Injectable()
export class TransactionsService {
    constructor(
        @InjectModel(Reconciliation.name) private ReconciliationModel: Model<Reconciliation>,
        @InjectModel(StaffProfileDetails.name) private StaffProfileModel: Model<StaffProfileDetails>,
        @InjectModel(Facility.name) private FacilityModel: Model<Facility>,
        @InjectModel(Invoice.name) private readonly InvoiceModel: Model<Invoice>,
        @InjectModel('PaymentMethod') private readonly paymentMethodModel: Model<typeof PaymentMethodSchema>,
    ) { }

    private async getFacility(facilityId: string) {
        const facility = await this.FacilityModel.findOne({ _id: facilityId }, { organizationId: 1 })
        if (!facility) {
            throw new BadRequestException("This facility does not exist");
        }
        return facility;
    }

    private async getOrganizationId(user: IUserDocument) {
        const { role } = user;
        let organizationId = null;
        switch (role.type) {
            case ENUM_ROLE_TYPE.ORGANIZATION:
                organizationId = user._id;
                break;

            case ENUM_ROLE_TYPE.FRONT_DESK_ADMIN:
            case ENUM_ROLE_TYPE.WEB_MASTER:
            case ENUM_ROLE_TYPE.TRAINER:
                const staffDetails = await this.StaffProfileModel.findOne(
                    { userId: user._id },
                    { organizationId: 1 }
                );
                if (!staffDetails) throw new BadRequestException("Access denied, Staff does not have access");
                organizationId = staffDetails.organizationId
                break;

            default:
                throw new BadRequestException("Access denied");
        }
        return organizationId;
    }


    public async getReconciliation(
        user: IUserDocument,
        organizationId: IDatabaseObjectId,
        body: GetReconciliationDto,
    ): Promise<GetReconciliationResponse | null> {
        const { facilityId } = body;

        // ✅ Validate facility
        const facility = await this.FacilityModel.findOne({
            _id: facilityId,
            organizationId,
        });

        if (!facility) {
            throw new BadRequestException("Facility not found or does not belong to your organization");
        }

        // ✅ Get latest reconciliation
        const latestReconciliation = await this.ReconciliationModel.findOne(
            { facilityId, organizationId },
            {},
            { sort: { updatedAt: -1 } },
        );

        if (!latestReconciliation) {
            return null;
        }

        // ✅ Build lookup from ALL facility payment methods (not just active)
        const allPaymentMethods = facility?.paymentMethods || [];
        const methodLookup = new Map<string, { name: string; shortId: string }>();
        allPaymentMethods.forEach((doc) => {
            const normName = (doc.name || "").trim().toLowerCase();
            const normShortId = (doc.shortId || "").trim().toLowerCase();
            if (normName) methodLookup.set(normName, { name: doc.name.toString(), shortId: doc.shortId });
            if (normShortId) methodLookup.set(normShortId, { name: doc.name.toString(), shortId: doc.shortId });
        });

        const pipeline: PipelineStage[] = [
            {
                $match: {
                    facilityId: new Types.ObjectId(facilityId),
                    organizationId: new Types.ObjectId(organizationId),
                    paymentStatus: PaymentStatus.COMPLETED,
                    invoiceStatus: { $ne: InvoiceStatus.CANCELED },
                    createdAt: { $gte: latestReconciliation.createdAt, $lte: new Date() },
                },
            },
            { $unwind: "$paymentDetails" },
            {
                $group: {
                    _id: "$paymentDetails.paymentMethod",
                    total: { $sum: "$paymentDetails.amount" },
                },
            },
            {
                $project: {
                    _id: 0,
                    method: "$_id",
                    total: 1,
                },
            },
        ];

        const salesByMethod = await this.InvoiceModel.aggregate(pipeline);

        const normalizedSales = salesByMethod.map((sale) => {
            const raw = (sale.method || "").trim().toLowerCase();
            const match = methodLookup.get(raw);
            return {
                method: match?.name || sale.method,
                shortId: match?.shortId || "",
                total: sale.total,
            };
        });

        const cashSales =
            normalizedSales
                .filter((s) => (s.method || "").trim().toLowerCase() === "cash")
                .reduce((sum, s) => sum + (s.total || 0), 0) || 0;

        const otherSales = normalizedSales.filter(
            (s) => (s.method || "").trim().toLowerCase() !== "cash",
        );

        const methodMap = new Map<string, { shortId: string; method: string; total: number }>();

        otherSales.forEach((sale) => {
            const key = (sale.method || "").trim().toLowerCase();
            if (key === "cash" || key ==="split payment") return;
            if (!methodMap.has(key)) {
                methodMap.set(key, {
                    shortId: sale.shortId || "",
                    method: sale.method,
                    total: sale.total,
                });
            } else {
                methodMap.get(key)!.total += sale.total;
            }
        });

        const activePaymentMethods = allPaymentMethods.filter((m: any) => m.isActive === true);
        activePaymentMethods.forEach((method: any) => {
            const key = (method.name || "").trim().toLowerCase();
            if (key === "cash" || key ==="split payment") return;
            if (!methodMap.has(key)) {
                methodMap.set(key, {
                    shortId: method.shortId,
                    method: method.name,
                    total: 0,
                });
            } else {
                const existing = methodMap.get(key)!;
                existing.shortId = method.shortId;
                existing.method = method.name;
                methodMap.set(key, existing);
            }
        });

        const finalOtherSales = Array.from(methodMap.values());
        return {
            _id: latestReconciliation._id,
            organizationId,
            facilityId,
            staffId: user._id,
            leaveAmount: latestReconciliation.leaveAmount,
            startingAmount: latestReconciliation.startingAmount,
            pettyAmount: latestReconciliation.pettyAmount,
            drawerAmount: latestReconciliation.drawerAmount,
            depositAmount: latestReconciliation.depositAmount,
            denominations: latestReconciliation.denominations,
            overUnder: latestReconciliation.overUnder,
            cashSales,
            otherSales: finalOtherSales,
            onlineOverUnder: latestReconciliation.onlineOverUnder,
            otherPayments: latestReconciliation.otherPayments,
            createdAt: latestReconciliation.createdAt,
            updatedAt: latestReconciliation.updatedAt,
        };
    }


    public async createReconciliation(user: IUserDocument, body: CreateReconciliationDto): Promise<any> {
        const organizationId = await this.getOrganizationId(user);
        const { facilityId, leaveAmount, notes } = body;

        const facility = await this.getFacility(facilityId);
        if (facility.organizationId.toString() !== organizationId.toString()) {
            throw new BadRequestException("This facility does not belong to your organization");
        }

        const newReconciliation = new this.ReconciliationModel({
            organizationId,
            facilityId: facility._id,
            staffId: user._id,
            leaveAmount: leaveAmount,
            startingAmount: body.startingAmount,
            pettyAmount: body.pettyAmount,
            drawerAmount: body.drawerAmount,
            depositAmount: body.depositAmount,
            cashSales: body.cashSales,
            denominations: body.denominations,
            overUnder: body.overUnder,
            onlineOverUnder: body.onlineOverUnder,
            otherPayments: body.otherPayments,
            notes: notes
        });
        await newReconciliation.save();
        return newReconciliation;
    }

public async getReconciliationList(
    user: IUserDocument,
    organizationId: IDatabaseObjectId,
    body: ZoutHistoryDTO
): Promise<any> {
    let { facilityId, startDate, endDate, page = 1, pageSize = 10 } = body;

    const facility = await this.FacilityModel.findOne({ _id: facilityId, organizationId });
    if (!facility) {
        throw new BadRequestException("Facility not found or does not belong to your organization");
    }

    if ([ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER, ENUM_ROLE_TYPE.WEB_MASTER].includes(user.role.type)) {
        const checkStaff = await this.StaffProfileModel.findOne({ userId: user._id, facilityId });
        if (!checkStaff) throw new BadRequestException("Staff not found in this facility");
        if (checkStaff.organizationId.toString() !== organizationId.toString()) {
            throw new BadRequestException("This Staff is not in your organization");
        }
    }

    const firstInvoice = await this.InvoiceModel.findOne(
        { facilityId, organizationId },
        { createdAt: 1 }
    ).sort({ createdAt: 1 }).lean();
    const earliestInvoiceDate = firstInvoice?.["createdAt"] || null;



    const matchQuery: any = {
        facilityId: new Types.ObjectId(facilityId),
        organizationId: new Types.ObjectId(organizationId),
    };

    if (startDate && endDate) {
        matchQuery["createdAt"] = {
            $gte: new Date(startDate),
            $lte: new Date(endDate),
        };
    } else if (startDate) {
        matchQuery["createdAt"] = { $gte: new Date(startDate) };
    } else if (endDate) {
        matchQuery["createdAt"] = { $lte: new Date(endDate) };
    }

    page = Math.max(1, page);
    pageSize = Math.max(1, pageSize);

    let skip = (page - 1) * pageSize;
    let limit = pageSize;

    const needLookbehind = skip > 0;
    if (needLookbehind) {
        skip -= 1;
        limit += 1;
    }

    const totalCount = await this.ReconciliationModel.countDocuments(matchQuery);

    const pipeline: PipelineStage[] = [
        { $match: matchQuery },
        { $sort: { createdAt: -1 } },
        { $skip: skip },
        { $limit: limit },
        {
            $lookup: {
                from: "users",
                localField: "staffId",
                foreignField: "_id",
                as: "result"
            }
        },
        { $unwind: "$result" },
        {
            $lookup: {
                from: "roles",
                localField: "result.role",
                foreignField: "_id",
                as: "roleData"
            }
        },
        { $unwind: "$roleData" },
        {
            $project: {
                _id: 1,
                createdAt: 1,
                role: "$roleData.name",
                name: "$result.name",
                overUnder: 1,
                onlineOverUnder:1,
                pettyCash: "$pettyAmount"
            }
        }
    ];

    const reconciliations = await this.ReconciliationModel.aggregate(pipeline);

    if (!reconciliations?.length) {
        return {
            page,
            pageSize,
            totalCount: 0,
            totalPages: 0,
            count: 0,
            data: [],
        };
    }

    reconciliations.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());

    const response = reconciliations.map((rec, idx) => {
        if (needLookbehind && idx === 0) return null;

        const prev = idx > 0
            ? reconciliations[idx - 1].createdAt
            : earliestInvoiceDate;

        return {
            _id: rec._id,
            role: rec.role,
            name: rec.name,
            startDate: prev,
            endDate: rec.createdAt,
            overUnder: rec.overUnder+rec.onlineOverUnder,
            pettyCash: rec.pettyCash
        };
    }).filter(Boolean);

    const reversedResponse = response.reverse();

    return {
        page,
        pageSize,
        totalCount: totalCount,
        totalPages: Math.ceil(totalCount / pageSize),
        count: reversedResponse.length,
        data: reversedResponse
    };
}

}