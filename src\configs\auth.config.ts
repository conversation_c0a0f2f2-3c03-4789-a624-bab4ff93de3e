import { registerAs } from '@nestjs/config';
import ms from 'ms';

export default registerAs(
    'auth',
    (): Record<string, any> => {
        // Validate required environment variables
        const sessionSecret = process.env.SESSION_SECRET;
        if (!sessionSecret) {
            throw new Error('SESSION_SECRET environment variable is required');
        }

        return {
            jwt: {
                accessToken: {
                    secretKey: process.env.JWT_SECRET,
                    expirationTime:
                        ms(
                            process.env
                                .JWT_TOKEN_EXPIRED as ms.StringValue
                        ) / 1000,
                },
                audience: process.env.JWT_AUDIENCE,
                issuer: process.env.JWT_ISSUER,
                header: 'Authorization',
                prefix: 'Bearer',
            },
            session: {
                secret: sessionSecret,
                name: 'session',
                cookie: {
                    secure: process.env.NODE_ENV !== 'local', // Always use secure for cross-site cookies
                    sameSite: process.env.NODE_ENV == 'local' ? 'lax' : 'none', // Required for cross-site cookies
                    httpOnly: process.env.NODE_ENV !== 'local',
                    path: '/',
                    // No maxAge - makes it a session cookie (expires when browser closes)
                },
                // Session expiration time for database records (24 hours as fallback)
                maxAge: ms('60d'),
            },
            cookie: {
                maxAge: ms(
                    process.env
                        .JWT_TOKEN_EXPIRED as ms.StringValue
                ),
                secure: process.env.NODE_ENV !== 'local', // Always use secure for cross-site cookies
                sameSite: process.env.NODE_ENV == 'local' ? 'lax' : 'none', // Use 'none' for cross-site cookies
            }
        };
    }
);
