import { BadRequestException, Injectable, InternalServerErrorException, NotFoundException } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Document } from "mongoose";
import * as crypto from 'crypto';
import * as QRCode from "qrcode";
import { UploadService } from "src/utils/services/upload.service";
import { Pricing, PricingDocument } from "../schemas/pricing.schema";
import { Model, Types } from "mongoose";
import { format } from "date-fns";
import { TransactionService } from "src/utils/services/transaction.service";
import { ActiveTimeFrameService } from "src/utils/services/active-time-frame.service";
import { CreatePricingDto } from "../dto/create-pricing.dto";
import { IDatabaseObjectId } from "src/common/database/interfaces/database.objectid.interface";
import { VoucherCreateDto } from "../dto/create-voucher.dto";
import { VoucherUpdateDto } from "../dto/update-voucher.dto";
import { Order } from "aws-sdk/clients/mediaconvert";
import { IDatabaseCreateOptions, IDatabaseFindAllOptions } from "src/common/database/interfaces/database.interface";
import { PaginationService } from "src/common/pagination/services/pagination.service";
import { ENUM_PRODUCT_ITEM_TYPE } from "src/promotions/enums/item-type.enum";
import { Purchase, PurchaseDocument } from "src/users/schemas/purchased-packages.schema";
import { VoucherResponseDto } from "../dto/detail-voucher.response.dto";
import { plainToInstance } from "class-transformer";
import { transformMongoDocument } from "src/common/transformers/objectid.transformer";
import { PipelineStage } from "mongoose";
import { VoucherPurchasedResponseDto } from "../dto/purchased-voucher.response.dto";
import moment from "moment";
import qrCode from "razorpay/dist/types/qrCode";
import { ShareVoucherDto } from "src/users/dto/share-pass.dto";
import { UserService } from "src/users/services/user.service";
import { Invoice } from "src/users/schemas/invoice.schema";
import { VoucherRedemption, VoucherRedemptionLogDocument } from "src/users/schemas/voucher-redemption.schema";
import { VoucherRedemptionDto, VoucherHistoryResponseDto } from "../dto/voucher-redemption.dto";

@Injectable()
export class VoucherService {
    constructor(
        @InjectModel(Pricing.name) private PricingModel: Model<Pricing>,
        @InjectModel(Purchase.name) private PurchaseModel: Model<Purchase>,
        @InjectModel(Invoice.name) private InvoiceModel: Model<Invoice>,
        @InjectModel(VoucherRedemption.name) private VoucherRedemptionLogModel: Model<VoucherRedemptionLogDocument>,
        private readonly userService: UserService,
        private readonly paginationService: PaginationService,
        private readonly uploadService: UploadService,
        private readonly transactionService: TransactionService,
    ) { }

    private readonly alphanumericCharacters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';

    async createVoucher(body: VoucherCreateDto, userId: IDatabaseObjectId, organizationId: IDatabaseObjectId): Promise<VoucherResponseDto> {
        const isPricingNameExist = await this.PricingModel.findOne({ name: body?.name, organizationId: organizationId })
        if (isPricingNameExist) {
            throw new BadRequestException("voucher.error.alreadyExists");
        }

        let data = {
            organizationId: organizationId,
            createdBy: userId,
            itemType: ENUM_PRODUCT_ITEM_TYPE.VOUCHER,
            name: body.name,
            price: body.price,
            isSellOnline: body.isSellOnline,
            expiredInDays: body.expiredInDays,
            durationUnit: body.durationUnit,
            isActive: true,
            description: body.description
        }
        const addPricing = new this.PricingModel(data);
        await addPricing.save();
        await addPricing.populate("createdBy", "name firstName lastName email");
        return plainToInstance(VoucherResponseDto, addPricing, { excludeExtraneousValues: true });

    }

    async updateVoucher(body: VoucherUpdateDto, userId: IDatabaseObjectId, organizationId: IDatabaseObjectId): Promise<any> {
        const isPricingNameExist = await this.PricingModel.findOne({ name: body?.name, organizationId: organizationId, _id: { $ne: body.voucherId } })
        if (isPricingNameExist) {
            throw new BadRequestException("voucher.error.alreadyExists");
        }

        let data = {
            name: body.name,
            price: body.price,
            isSellOnline: body.isSellOnline,
            expiredInDays: body.expiredInDays,
            durationUnit: body.durationUnit,
            description: body.description
        }
        const addPricing = await this.PricingModel.findOneAndUpdate(
            { _id: body.voucherId },
            { $set: data },
            { new: true },
        ).populate("createdBy", "name firstName lastName email");
        return plainToInstance(VoucherResponseDto, addPricing, { excludeExtraneousValues: true })
    }

    async voucherList(filter: Record<string, any>, order: IDatabaseFindAllOptions): Promise<{ list: VoucherResponseDto[], count: number }> {
        const list = await this.PricingModel.find(filter, "_id createdBy itemType name organizationId price isSellOnline expiredInDays durationUnit isActive description createdAt updatedAt")
            .sort(this.paginationService.orderFormat(order.order))
            .skip(order.paging.offset)
            .limit(order.paging.limit)
            .populate("createdBy", "_id name firstName lastName email")
        const count = await this.PricingModel.countDocuments(filter).lean();
        return {
            list: list.map((item) => {
                // Use utility function to transform MongoDB document
                // Enable debug in development to troubleshoot date issues
                const transformedObject = transformMongoDocument(item, process.env.NODE_ENV === 'development');
                return plainToInstance(VoucherResponseDto, transformedObject, { excludeExtraneousValues: true });
            }),
            count
        };
    }

    async usersVoucherList(filter: Record<string, any>, order: IDatabaseFindAllOptions): Promise<{ list: VoucherPurchasedResponseDto[], count: number }> {
        const { search, ...match } = filter
        // {
        //     organizationId: new Types.ObjectId(filter.organizationId),
        //     userId: new Types.ObjectId(filter.userId),
        //     itemType: filter.itemType,
        //     isExpired: false,
        //     isActive: filter.isActive,
        //     // $expr: {
        //     //     $gt: [
        //     //         {
        //     //             $subtract: [
        //     //                 "$voucherAmount",
        //     //                 "$amountConsumed"
        //     //             ]
        //     //         },
        //     //         0
        //     //     ]
        //     // }
        // }


        const pipeline: PipelineStage[] = [
            { $match: match },
            {
                $lookup: {
                    from: "pricings",
                    localField: "packageId",
                    foreignField: "_id",
                    as: "voucherDetails",
                    pipeline: [
                        {
                            $match: {
                                itemType: filter.itemType,
                                search
                            }
                        }
                    ]
                },
            },
            {
                $unwind: {
                    path: "$voucherDetails",
                    preserveNullAndEmptyArrays: false,
                },
            },
            {
                $lookup: {
                    from: "invoices",
                    localField: "invoiceId",
                    foreignField: "_id",
                    as: "invoiceDetails",
                    pipeline: [
                        {
                            $project: {
                                paymentStatus: 1,
                            }
                        }
                    ]
                },
            },
            {
                $unwind: {
                    path: "$invoiceDetails",
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $facet: {
                    list: [
                        { $sort: this.paginationService.orderFormat(order.order) },
                        { $skip: order.paging.offset },
                        { $limit: order.paging.limit },
                        {
                            $project: {
                                invoiceId: 1,
                                _id: '$_id',
                                organizationId: 1,
                                facilityId: 1,
                                name: "$voucherDetails.name",
                                description: "$voucherDetails.description",
                                userId: 1,
                                voucherId: "$packageId",
                                voucherCode: 1,
                                price: '$voucherAmount',
                                amountConsumed: 1,
                                purchaseDate: 1,
                                isExpired: {
                                    $cond: {
                                        if: {
                                            $or: [
                                                { $eq: ["$isActive", false] },
                                                { $eq: ["$isExpired", true] },
                                                { $gt: [new Date(), "$endDate"] },
                                            ]
                                        },
                                        then: true,
                                        else: false,
                                    },
                                },
                                isActive: 1,
                                startDate: 1,
                                endDate: 1,
                                remainingAmount: {
                                    $subtract: ["$voucherAmount", "$amountConsumed"]
                                },
                                expiredInDays: {
                                    $ceil: {
                                        $divide: [
                                            { $subtract: ["$endDate", new Date()] },
                                            1000 * 60 * 60 * 24
                                        ]
                                    },
                                },
                                qrCodeUrl: 1,
                                paymentStatus: {
                                    $cond: {
                                        if: "$invoiceDetails.paymentStatus",
                                        then: "$invoiceDetails.paymentStatus",
                                        else: "$paymentStatus",
                                    }
                                },
                                createdAt: 1,
                                updatedAt: 1,
                            }
                        }
                    ],
                    total: [
                        { $count: "total" }
                    ]
                }
            }
        ];
        const list = await this.PurchaseModel.aggregate(pipeline);
        const count = list[0]?.total[0]?.total || 0;
        const transformedList = list[0]?.list.map((item) => {
            const transformedObject = transformMongoDocument(item);
            return plainToInstance(VoucherPurchasedResponseDto, transformedObject, { excludeExtraneousValues: true });
        });
        return {
            list: transformedList,
            count
        };
    }

    async voucherDetails(voucherId: IDatabaseObjectId, organizationId: IDatabaseObjectId): Promise<VoucherResponseDto> {
        const result = await this.PricingModel.findOne({
            _id: voucherId,
            organizationId: organizationId,
            itemType: ENUM_PRODUCT_ITEM_TYPE.VOUCHER
        }).populate("createdBy", "name firstName lastName email").lean();
        if (!result) {
            throw new NotFoundException('voucher.error.notFound');
        }
        // Use utility function to transform MongoDB document
        const transformedResult = transformMongoDocument(result);
        return plainToInstance(VoucherResponseDto, transformedResult, { excludeExtraneousValues: true });
    }

    async voucherPurchasedDetails(filter: Record<string, any>): Promise<VoucherPurchasedResponseDto> {

        const result: Omit<PurchaseDocument, 'packageId'> & { packageId: PricingDocument } = await this.PurchaseModel.findOne(filter).populate([
            { path: "packageId", select: "_id name price services expiredInDays durationUnit" },
        ]).lean();
        if (!result) {
            throw new NotFoundException('voucher.error.notFound');
        }

        if (!result.isActive) {
            throw new BadRequestException('voucher.error.inactive');
        }

        // if (result.endDate < new Date() || result.isExpired) {
        if (result.isExpired) {
            throw new BadRequestException({
                statusCode: 400,
                message: 'voucher.error.expired',
                _metadata: {
                    customProperty: {
                        messageProperties: {
                            time: moment(result.endDate).format('Do MMM'),
                        },
                    },
                },
            });
        }

        if (result.amountConsumed >= result.voucherAmount) {
            throw new BadRequestException('voucher.error.consumed');
        }

        const invoice = await this.InvoiceModel.findOne({
            _id: result.invoiceId,
        }, { paymentStatus: 1 }).lean();

        const expiryInDays = moment(result.endDate).diff(new Date(), 'days');

        const data: VoucherPurchasedResponseDto = {
            invoiceId: result.invoiceId,
            _id: result._id,
            facilityId: new Types.ObjectId(result.facilityId),
            voucherId: result.packageId._id,
            userId: result.userId,
            voucherCode: result.voucherCode,
            price: result.voucherAmount,
            amountConsumed: result.amountConsumed,
            purchaseDate: result.purchaseDate,
            startDate: result.startDate,
            endDate: result.endDate,
            remainingAmount: result.voucherAmount - result.amountConsumed,
            qrCodeUrl: result.qrCodeUrl,
            name: result.packageId.name,
            expiredInDays: expiryInDays,
            isActive: result.isActive,
            description: result.packageId.description,
            paymentStatus: invoice?.paymentStatus || result.paymentStatus,
        }
        // Use utility function to transform MongoDB document
        const transformedResult = transformMongoDocument(data);
        return plainToInstance(VoucherPurchasedResponseDto, transformedResult, { excludeExtraneousValues: true });
    }

    async voucherStatusUpdate(voucherId: IDatabaseObjectId, status: boolean, organizationId: IDatabaseObjectId): Promise<any> {
        const result = await this.PricingModel.findOneAndUpdate({
            _id: voucherId,
            organizationId: organizationId,
            itemType: ENUM_PRODUCT_ITEM_TYPE.VOUCHER
        }, { $set: { isActive: status } }, { new: true }).lean();
        if (!result) {
            throw new NotFoundException('voucher.error.notFound');
        }
        return result;
    }

    async voucherDelete(voucherId: IDatabaseObjectId, organizationId: IDatabaseObjectId): Promise<any> {
        const isPricingUsed = await this.PurchaseModel.findOne({ packageId: voucherId });
        if (isPricingUsed) {
            throw new BadRequestException("voucher.error.alreadyUsedCannotDelete");
        }
        const result = await this.PricingModel.findOneAndDelete({
            _id: voucherId,
            organizationId: organizationId,
            itemType: ENUM_PRODUCT_ITEM_TYPE.VOUCHER
        }).lean();
        if (!result) {
            throw new NotFoundException("voucher.error.notFound");
        }
        return result;
    }


    /**
     * Generates a secure code for a voucher.
     * @returns A secure code.
     */
    async generateSecureCode(attempts: number = 0): Promise<string> {
        const length = 12; // Fixed length of 12 characters
        let result = '';
        const bytes = crypto.randomBytes(length);

        for (let i = 0; i < length; i++) {
            const index = bytes[i] % this.shuffleString(this.alphanumericCharacters).length;
            result += this.alphanumericCharacters[index];
        }

        // Add hyphens every 4 characters for readability.
        let formattedResult = result.replace(/(.{4})/g, '$1-').slice(0, -1);
        const count = await this.PurchaseModel.countDocuments({ voucherCode: formattedResult }).lean();
        if (count > 0) {
            if (attempts >= 10) {
                throw new InternalServerErrorException('Failed to generate unique voucher code after 3 attempts.');
            }
            return this.generateSecureCode(attempts + 1);
        }
        return formattedResult;
    }

    private shuffleString(str: string): string {
        let arr = str.split('');
        for (let i = arr.length - 1; i > 0; i--) {
            let j = Math.floor(Math.random() * (i + 1));
            [arr[i], arr[j]] = [arr[j], arr[i]];
        }
        return arr.join('');
    }

    async generateVoucherQRCode(params: {
        organization_id: string | IDatabaseObjectId,
        purchase_id: string | IDatabaseObjectId,
        voucherCode: string,
        package_id: string | IDatabaseObjectId,
        name: string
        startDate: Date,
        endDate: Date
        itemType: ENUM_PRODUCT_ITEM_TYPE,
        price: number
    }): Promise<any> {
        const jsonData = {
            organization_id: params.organization_id,
            purchase_id: params.purchase_id,
            package_id: params.package_id,
            voucherCode: params.voucherCode,
            name: params.name,
            startDate: params.startDate,
            endDate: params.endDate,
            itemType: params.itemType,
            price: params.price,
        };
        const qrData = JSON.stringify(jsonData);
        const qrCodeBuffer = await QRCode.toBuffer(qrData, { type: "png" });
        const s3Result = await this.uploadService.upload(qrCodeBuffer, "voucher-qr/", `voucher-${params.purchase_id}.png`);
        return s3Result?.Location || "";
    }

    async shareVoucher(shareVoucherDto: ShareVoucherDto, organizationId: IDatabaseObjectId): Promise<any> {
        const session = await this.transactionService.startTransaction();
        let newPurchase: PurchaseDocument = null
        try {
            // Validate users
            const [shareFromUser, shareToUser] = await Promise.all([
                this.userService.findOne({ _id: shareVoucherDto.shareFrom, organizationId }),
                this.userService.findOne({ _id: shareVoucherDto.shareTo, organizationId }),
            ]);
            if (!shareFromUser || !shareToUser) throw new BadRequestException("Client not found");

            // Fetch sender's purchase data
            const senderPurchaseData = await this.PurchaseModel.findOne<Omit<PurchaseDocument, 'packageId'> & { packageId: PricingDocument }>({
                _id: shareVoucherDto.purchaseId,
                itemType: ENUM_PRODUCT_ITEM_TYPE.VOUCHER,
                isExpired: false,
                isActive: true,
                $or: [{
                    userId: shareVoucherDto.shareFrom,
                }, {
                    sponsorUser: shareVoucherDto.shareFrom,
                }],
            }).populate([
                { path: "packageId", select: "_id name price services expiredInDays durationUnit" },
            ]).session(session);
            if (!senderPurchaseData) throw new BadRequestException("Voucher data not found");

            const packageStart = senderPurchaseData?.startDate ? new Date(senderPurchaseData.startDate).getTime() : null;
            const packageEnd = senderPurchaseData?.endDate ? new Date(senderPurchaseData.endDate).getTime() : null;
            const currentTime = new Date().getTime();
            if (!packageStart || !packageEnd) throw new BadRequestException("Invalid voucher dates.");

            if (!(packageStart <= currentTime && packageEnd >= currentTime)) {
                throw new BadRequestException(
                    `This voucher is not eligible for booking. Expiry date is ${format(new Date(packageEnd), "dd/MM/yyyy")} at ${format(new Date(packageEnd), "HH:mm")}.`,
                );
            }

            // Decrease session count for sender
            await this.PurchaseModel.updateOne(
                { _id: shareVoucherDto.purchaseId },
                {
                    $set: {
                        amountConsumed: senderPurchaseData.voucherAmount,
                        isExpired: true,
                        isActive: false,
                        endDate: new Date(),
                        voucherCode: undefined,
                    }
                },
                { session },
            );

            // Create new purchase record for the recipient
            const receiverPurchaseData = new this.PurchaseModel({
                userId: shareVoucherDto.shareTo,
                consumers: [shareVoucherDto.shareTo],
                voucherAmount: senderPurchaseData.voucherAmount,
                amountConsumed: 0,
                packageId: senderPurchaseData.packageId,
                itemType: senderPurchaseData.itemType,
                bundledPricingId: senderPurchaseData.bundledPricingId,
                organizationId: senderPurchaseData.organizationId,
                facilityId: senderPurchaseData.facilityId,
                purchasedBy: shareVoucherDto.shareTo,
                purchaseDate: new Date(),
                paymentStatus: senderPurchaseData.paymentStatus,
                isExpired: senderPurchaseData.isExpired,
                sharePass: true,
                sharedBy: shareVoucherDto.shareFrom,
                isActive: senderPurchaseData.isActive,
                startDate: senderPurchaseData.startDate,
                endDate: senderPurchaseData.endDate,
                invoiceId: senderPurchaseData.invoiceId,
                voucherCode: senderPurchaseData.voucherCode,
            });
            await receiverPurchaseData.save({ session });
            const qrCodeUrl = await this.generateVoucherQRCode({
                organization_id: senderPurchaseData.organizationId,
                purchase_id: receiverPurchaseData._id,
                package_id: senderPurchaseData.packageId._id,
                voucherCode: receiverPurchaseData.voucherCode,
                name: senderPurchaseData.packageId.name,
                startDate: receiverPurchaseData.startDate,
                endDate: receiverPurchaseData.endDate,
                itemType: receiverPurchaseData.itemType,
                price: senderPurchaseData.packageId.price,
            });
            receiverPurchaseData.qrCodeUrl = qrCodeUrl;
            newPurchase = await receiverPurchaseData.save({ session });

            await this.transactionService.commitTransaction(session);

            return newPurchase;

        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
            if (newPurchase) {
                // send mail
            }
        }
    }

    /**
     * Create a voucher redemption log entry
     */
    async logRedemption({
        voucher,
        userId,
        organizationId,
        amountRedeemed,
        createdBy,
        invoiceId
    }: {
        voucher: PurchaseDocument,
        userId: IDatabaseObjectId,
        organizationId: IDatabaseObjectId,
        amountRedeemed: number,
        createdBy: IDatabaseObjectId,
        invoiceId: IDatabaseObjectId
    },
        options?: IDatabaseCreateOptions
    ): Promise<VoucherRedemptionLogDocument> {

        if (voucher.itemType !== ENUM_PRODUCT_ITEM_TYPE.VOUCHER) throw new BadRequestException('voucher.error.notFound');

        const previousBalance = voucher.voucherAmount - voucher.amountConsumed;
        const remainingBalance = voucher.voucherAmount - (voucher.amountConsumed + amountRedeemed) || 0;

        const redemptionLog = new this.VoucherRedemptionLogModel({
            purchaseId: voucher._id,
            userId,
            organizationId,
            amountRedeemed,
            remainingBalance: remainingBalance,
            previousBalance: previousBalance,
            invoiceId,
            createdBy,
        });

        return await redemptionLog.save(options);
    }

    /**
     * Get voucher redemption history
     */
    async getVoucherHistory(
        filter: Record<string, any>,
        options: IDatabaseFindAllOptions
    ): Promise<{ list: VoucherHistoryResponseDto[]; total: number; }> {

        const history: any[] = await this.VoucherRedemptionLogModel.find(filter)
            .sort(this.paginationService.orderFormat(options.order))
            .skip(options.paging.offset)
            .limit(options.paging.limit)
            .populate("purchaseId", "voucherAmount amountConsumed voucherCode startDate endDate")
            .populate("userId", "name")
            .populate("createdBy", "name")
            .lean();


        const total = await this.VoucherRedemptionLogModel.countDocuments(filter).lean();

        const data = history.map<VoucherHistoryResponseDto>(log => (
            {
                ...log,
                purchaseId: log.purchaseId?._id || null,
                userId: log.userId?._id || null,
                userName: log.userId?.name || "Unknown User",
                amountRedeemed: log.amountRedeemed,
                remainingBalance: log.remainingBalance,
                previousBalance: log.previousBalance,
                invoiceId: log.invoiceId,
                createdBy: log.createdBy?._id || null,
                createdByName: log.createdBy?.name || "Unknown User",
                createdAt: log.createdAt,
            })
        );

        const list = history.map(log => {
            const transformedLog = transformMongoDocument(log);
            return plainToInstance(VoucherHistoryResponseDto, transformedLog, { excludeExtraneousValues: true });
        });

        return {
            list: list,
            total: total,
        };
    }


}
