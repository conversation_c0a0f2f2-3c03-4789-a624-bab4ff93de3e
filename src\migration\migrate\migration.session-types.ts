import { Model } from "mongoose";
import { InjectModel } from "@nestjs/mongoose";
import { Organizations, OrganizationDocument } from "src/organization/schemas/organization.schema";
import { Injectable, Logger } from "@nestjs/common";
import { Command } from "nestjs-command";

@Injectable()
export class MigrationSessionTypes {
    constructor(@InjectModel(Organizations.name) private orgModel: Model<OrganizationDocument>) {}

    @Command({
        command: "organization:session-type",
        describe: "Add default session types to organizations",
    })
    async seeds(): Promise<void> {
        try {
            const defaultSessionTypes = [
                { name: "Appointments", realName: "Appointments", id: "personalAppointment", orderIndex: 1, isActive: true },
                { name: "Bookings", realName: "Bookings", id: "bookings", orderIndex: 2, isActive: true },
                { name: "Classes", realName: "Classes", id: "classes", orderIndex: 3, isActive: true },
                { name: "Courses", realName: "Courses", id: "courses", orderIndex: 4, isActive: true },
            ];
            const orgs = await this.orgModel.find({
                $or: [{ sessionTypes: { $exists: false } }, { sessionTypes: { $size: 0 } }],
            });

            if (orgs.length === 0) {
                console.log("All organizations already have sessionTypes. Nothing to update.");
                return;
            }

            for (const org of orgs) {
                org.sessionTypes = defaultSessionTypes;
                await org.save();
                console.log(`Inserted sessionTypes for orgId: ${org._id}`);
            }
        } catch (err: any) {
            throw new Error(err);
        }
        return;
    }
}
