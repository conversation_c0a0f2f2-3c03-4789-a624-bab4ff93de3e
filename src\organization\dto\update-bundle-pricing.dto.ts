import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsArray, IsBoolean, IsEnum, IsMongoId, IsNotEmpty, IsNumber, IsOptional, IsString, Max, Min, ValidateNested } from "class-validator";
import { ActiveTimeFrameDto } from "src/utils/dto/active-time-frame.dto";
import { DiscountType } from "src/utils/enums/discount.enum";
import { DurationUnit } from "src/utils/enums/duration-unit.enum";


export class DiscountDto {
    @ApiProperty({
        description: "Type of discount",
        enum: DiscountType,
        example: DiscountType.FLAT,
        required: false,
    })
    @IsOptional()
    @IsEnum(DiscountType, { message: "Invalid discount type" })
    type?: DiscountType;

    @ApiProperty({
        description: "Value of the discount",
        example: 50,
        required: false,
    })
    @IsOptional()
    @IsNumber({}, { message: "Invalid discount value" })
    @Min(0, { message: "Discount value cannot be less than 0" })
    value?: number;
}

export class UpdateBundledPricingDto {
    @ApiProperty({
        description: "The Id of the pricing",
        example: "66cecb432351713ae4447a6b",
        required: true,
    })
    @IsMongoId({ message: "Required valid pricing details" })
    pricingId: string;

    @ApiProperty({
        description: "The name of the Bundled pricing.",
        example: "Animal Flow Select",
        required: true,
    })
    @IsString({ message: "Invalid type of Bundled pricing name" })
    @IsNotEmpty({ message: "Bundled Pricing name cannot be empty" })
    name: string;

    @ApiProperty({
        description: "The description of the Bundled pricing.",
        example: "Animal Flow Select",
    })
    @IsOptional()
    @IsString({ message: "Invalid type of Bundled pricing description" })
    description?: string;

    @ApiProperty({
        description: "The description of the Bundled short pricing.",
        example: "Animal Flow Select",
    })
    @IsOptional()
    @IsString({ message: "Invalid type of Bundled short pricing description" })
    shortDescription?: string;

    @ApiProperty({
        description: "Price excluding GST | Must be a number",
        example: 299,
        required: true,
    })
    @IsNumber({}, { message: "Invalid price type" })
    price: Number;

    @ApiProperty({
        description: "Can be sold online or not | Boolean",
        example: false,
        required: true,
    })
    @IsBoolean({ message: "Invalid flag value for online indicator" })
    isSellOnline: Boolean;

    // @ApiProperty({
    //     description: "Discount details for this pricing",
    //     required: false,
    // })
    // @ValidateNested({ message: "Invalid discount object" })
    // @Type(() => DiscountDto)
    // @IsOptional()
    // discount?: DiscountDto;

    @ApiProperty({
        description: "Tax on Bundled pricing | Must be a number",
        example: 12,
        required: true,
    })
    @IsNumber({}, { message: "Invalid tax type" })
    @Min(0, { message: "Tax percentage cannot be less than 0" })
    @Max(40, { message: "Tax percentage cannot be greater than 40" })
    tax: Number;

    @ApiProperty({
        description: "Expiry in days | Must be a number",
        example: 12,
        required: true,
    })
    @IsNumber({}, { message: "Invalid expiry date" })
    expiredInDays: number;

    @ApiProperty({
        description: "Expiration Unit",
        enum: DurationUnit,
        example: DurationUnit.DAYS,
        required: true,
    })
    @IsEnum(DurationUnit, { message: "Expiration unit is not valid" })
    durationUnit: DurationUnit;

    @ApiProperty({
        description: "Is the pricing inclusive of GST | Boolean",
        example: false,
        required: false,
    })
    @IsBoolean({ message: "Invalid value for GST inclusion" })
    @IsOptional({})
    isInclusiveofGst: Boolean;

    @ApiProperty({
        description: "HSN/SAC code | Must be a string",
        example: "998314",
        required: true,
    })
    @IsString({ message: "Invalid HSN/SAC code" })
    @IsNotEmpty({ message: "HSN/SAC code cannot be empty" })
    hsnOrSacCode: string;

    @ApiProperty({
        description: "Array of Pricing Ids",
        example: ["66cecb432351713ae4447a6b", "66cecb432351713ae4447a6c"],
        required: true,
    })
    @IsNotEmpty({ message: "PricingIds are required" })
    @IsArray({ message: "PricingIds type must be an array" })
    @IsMongoId({ each: true, message: "Each Pricing Id type must be a valid ObjectId" })
    pricingIds: string[];

    @ApiProperty({
        description: "Active time frames define when this pricing is available for purchase in POS",
        type: [ActiveTimeFrameDto],
        required: false,
    })
    @IsOptional()
    @ValidateNested({ each: true })
    @Type(() => ActiveTimeFrameDto)
    activeTimeFrames?: ActiveTimeFrameDto[];

}





