import { Injectable } from "@nestjs/common";
import { ClientsListDto } from "../dto/clients-list.dto";
import { PipelineStage, Types } from "mongoose";
import { ClientsListDtoV1 } from "../dto/clientsv1-list.dto";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { ScheduleStatusType } from "src/scheduling/enums/schedule-status.enum";

@Injectable()
export class UsersPipe {
    getClientsPipe(clientListDto: ClientsListDto, facilityList: Array<string>, search: string) {
        let pageSize = clientListDto.pageSize ?? 10;
        let page = clientListDto.page ?? 1;
        let skip = pageSize * (page - 1);
        if (clientListDto?.isActive == true) {
            (pageSize = 200), (page = 1);
        }
        skip = pageSize * (page - 1);
        let matchObj: any = {};
        let keepOnTop = [];
        if (clientListDto.facilityId) {
            matchObj = {
                $match: {
                    $expr: {
                        $in: ["$facilityId", [Types.ObjectId.createFromHexString(clientListDto.facilityId)]],
                    },
                },
            };
        } else {
            matchObj = {
                $match: {
                    $expr: {
                        $in: ["$facilityId", facilityList],
                    },
                },
            };
        }
        if (clientListDto.notIncludedClientId) matchObj.$match.userId = { $ne: Types.ObjectId.createFromHexString(clientListDto.notIncludedClientId) };
        if (clientListDto.includeUserIds && clientListDto.includeUserIds.length) {
            keepOnTop = [
                {
                    $addFields: {
                        order: {
                            $cond: {
                                if: { $in: ["$userDetails._id", clientListDto.includeUserIds.map((id) => new Types.ObjectId(id))] },
                                then: 0,
                                else: 1,
                            },
                        },
                    },
                },
            ];
        }

        let userMatchConditions: any = {
            $and: [],
        };

        const searchConditions = {
            $or: [
                { name: { $regex: `.*${search}.*`, $options: "i" } },
                { firstName: { $regex: `.*${search}.*`, $options: "i" } },
                { lastName: { $regex: `.*${search}.*`, $options: "i" } },
                { email: { $regex: `.*${search}.*`, $options: "i" } },
                { mobile: { $regex: `.*${search}.*`, $options: "i" } },
            ],
        };

        userMatchConditions.$and.push(searchConditions);

        if (clientListDto.isActive === true) {
            userMatchConditions.$and.push({ isActive: true });
        }

        if (clientListDto.isParent === true) {
            userMatchConditions.$and.push({
                $or: [{ parent: { $exists: false } }, { parent: null }],
            });
        } else if (clientListDto.isParent === false) {
            userMatchConditions.$and.push({
                parent: { $exists: true },
            });
        }

        if (userMatchConditions.$and.length === 1) {
            userMatchConditions = searchConditions;
        }

        let pipeline = [
            matchObj,
            {
                $lookup: {
                    from: "users",
                    localField: "userId",
                    foreignField: "_id",
                    as: "userDetails",
                    pipeline: [
                        {
                            $match: userMatchConditions,
                        },
                    ],
                },
            },
            {
                $unwind: {
                    path: "$userDetails",
                    preserveNullAndEmptyArrays: false,
                },
            },
            ...keepOnTop,
            {
                $facet: {
                    list: [
                        {
                            $sort: {
                                order: 1,
                                "userDetails.isActive": -1,
                                updatedAt: -1,
                            },
                        },
                        {
                            $skip: skip,
                        },
                        {
                            $limit: pageSize,
                        },
                        {
                            $lookup: {
                                from: "facilities",
                                localField: "facilityId",
                                foreignField: "_id",
                                as: "facilityDetails",
                            },
                        },
                        {
                            $unwind: {
                                path: "$facilityDetails",
                                preserveNullAndEmptyArrays: false,
                            },
                        },
                        {
                            $lookup: {
                                from: "cities",
                                localField: "facilityDetails.address.city",
                                foreignField: "_id",
                                as: "cityDetails",
                            },
                        },
                        {
                            $lookup: {
                                from: "users",
                                localField: "createdBy",
                                foreignField: "_id",
                                as: "createdBy",
                            },
                        },
                        {
                            $unwind: {
                                path: "$createdBy",
                                preserveNullAndEmptyArrays: true,
                            },
                        },
                        {
                            $project: {
                                clientId: 1,
                                membershipId: 1,
                                facilityId: 1,
                                facilityName: "$facilityDetails.facilityName",
                                userId: "$userDetails._id",
                                name: "$userDetails.name",
                                firstName: "$userDetails.firstName",
                                lastName: "$userDetails.lastName",
                                isActive: "$userDetails.isActive",
                                mobile: "$userDetails.mobile",
                                countryCode: "$userDetails.countryCode",
                                email: "$userDetails.email",
                                createdAt: "$createdAt",
                                dob: 1,
                                photo: 1,
                                cityName: {
                                    $arrayElemAt: ["$cityDetails.name", 0],
                                },
                                createdBy: {
                                    $cond: {
                                        if: "$createdBy",
                                        then: {
                                            _id: "$createdBy._id",
                                            name: "$createdBy.name",
                                            firstName: "$createdBy.firstName",
                                            lastName: "$createdBy.lastName",
                                        },
                                        else: null,
                                    },
                                },
                                sourceId: 1,
                            },
                        },
                    ],
                    total: [
                        {
                            $count: "total",
                        },
                    ],
                },
            },
        ] as any;
        return pipeline;
    }

    getClientsPipeV1(clientListDto: ClientsListDtoV1, facilityList: Array<string>, search: any) {
        let { page, pageSize, organizationId, clientId } = clientListDto;
        const now = new Date();
        const threeMonthsAgo = new Date(now);
        threeMonthsAgo.setMonth(now.getMonth() - 3);

        if (isNaN(page) || page < 1) {
            page = 1;
        }
        if (isNaN(pageSize) || pageSize < 1) {
            pageSize = 10;
        }
        const skip = (page - 1) * pageSize;

        if (!organizationId) {
            throw new Error("Organization ID is required.");
        }

        if (!Array.isArray(facilityList) || facilityList.length === 0) {
            throw new Error("Facility list must be a non-empty array.");
        }

        const matchObj: { $match: Record<string, any> } = {
            $match: {
                organizationId: new Types.ObjectId(organizationId),
                facilityId: { $in: facilityList.map((id) => new Types.ObjectId(id)) },
            },
        };
        if (clientId) {
            matchObj.$match.userId = new Types.ObjectId(clientId);
        }
        const userMatchConditions: any = { isActive: true };
        if (Array.isArray(search) && search.length > 0) {
            userMatchConditions["$or"] = search;
        }

        return [
            matchObj,
            {
                $project: {
                    dob: 1,
                    gender: 1,
                    photo: 1,
                    userId: 1,
                    facilityId: 1,
                    organizationId: 1,
                    createdAt: 1,
                    updatedAt: 1,
                    policies: 1,
                    activityLevel: 1,
                    userType: 1,
                    subUserType: 1,
                },
            },
            {
                $lookup: {
                    from: "users",
                    localField: "userId",
                    foreignField: "_id",
                    as: "userDetails",
                    pipeline: [
                        {
                            $match: userMatchConditions,
                        },
                        {
                            $lookup: {
                                from: "roles",
                                localField: "role",
                                foreignField: "_id",
                                as: "roleDetails",
                            },
                        },
                        {
                            $match: {
                                "roleDetails.0.type": ENUM_ROLE_TYPE.USER,
                            },
                        },
                    ],
                },
            },
            { $unwind: { path: "$userDetails", preserveNullAndEmptyArrays: false } },
            {
                $lookup: {
                    from: "schedulings",
                    localField: "userId",
                    foreignField: "clientId",
                    as: "schedulingData",
                    pipeline: [
                        {
                            $match: {
                                organizationId: new Types.ObjectId(organizationId),
                                facilityId: { $in: facilityList.map((id) => new Types.ObjectId(id)) },
                                date: { $lte: new Date() },
                            },
                        },
                        { $sort: { date: -1 } },
                        { $limit: 1 },
                    ],
                },
            },
            {
                $addFields: {
                    lastVisited: { $ifNull: [{ $arrayElemAt: ["$schedulingData.date", 0] }, null] },
                },
            },
            // Count visits in last 3 months for this client
            {
                $lookup: {
                    from: "schedulings",
                    localField: "userId",
                    foreignField: "clientId",
                    as: "visits3m",
                    pipeline: [
                        {
                            $match: {
                                organizationId: new Types.ObjectId(organizationId),
                                facilityId: { $in: facilityList.map((id) => new Types.ObjectId(id)) },
                                date: { $gte: threeMonthsAgo, $lte: now },
                                scheduleStatus: "checked-in",
                            },
                        },
                        { $count: "count" },
                    ],
                },
            },
            {
                $addFields: {
                    visitsLast3Months: { $ifNull: [{ $arrayElemAt: ["$visits3m.count", 0] }, 0] },
                },
            },

            {
                $lookup: {
                    from: "organizations",
                    localField: "organizationId",
                    foreignField: "userId",
                    as: "orgDetails",
                },
            },
            { $addFields: { org0: { $arrayElemAt: ["$orgDetails", 0] } } },
            {
                $addFields: {
                    orgPolicies: {
                        $ifNull: ["$org0.clientOnboarding.policies.items", []],
                    },
                },
            },
            {
                $addFields: {
                    clientCheckedPolicyIdStrings: {
                        $map: {
                            input: {
                                $filter: {
                                    input: { $ifNull: ["$policies", []] },
                                    as: "cp",
                                    cond: { $eq: ["$$cp.isEnabled", true] }, // treat isEnabled=true as "checked"
                                },
                            },
                            as: "cp",
                            in: { $toString: "$$cp.policyId" }, // normalize ObjectId → string
                        },
                    },
                },
            },
            {
                $addFields: {
                    missingRequiredPolicies: {
                        $map: {
                            input: {
                                $filter: {
                                    input: { $ifNull: ["$org0.clientOnboarding.policies.items", []] },
                                    as: "p",
                                    cond: {
                                        $and: [
                                            // optional visibility gate:
                                            { $eq: ["$$p.isShown", true] },
                                            { $not: [{ $in: [{ $toString: "$$p._id" }, "$clientCheckedPolicyIdStrings"] }] },
                                        ],
                                    },
                                },
                            },
                            as: "p",
                            in: {
                                id: "$$p._id",
                                name: "$$p.name",
                                required: "$$p.required",
                                isShown: "$$p.isShown",
                                message: {
                                    $concat: ["$$p.name", " ", "is not checked by this client."],
                                },
                            },
                        },
                    },
                },
            },
            {
                $facet: {
                    list: [
                        {
                            $sort: {
                                isActive: -1,
                                updatedAt: -1,
                                "userDetails.firstName": 1,
                            },
                        },
                        { $skip: skip },
                        { $limit: pageSize },
                        {
                            $lookup: {
                                from: "users",
                                localField: "userDetails.createdBy",
                                foreignField: "_id",
                                as: "createdBy",
                            },
                        },
                        { $unwind: { path: "$createdBy", preserveNullAndEmptyArrays: true } },
                        {
                            $project: {
                                createdBy: {
                                    $cond: {
                                        if: "$createdBy",
                                        then: {
                                            _id: "$createdBy._id",
                                            name: "$createdBy.name",
                                            firstName: "$createdBy.firstName",
                                            lastName: "$createdBy.lastName",
                                        },
                                        else: null,
                                    },
                                },
                                facilityId: 1,
                                organizationId: 1,
                                userId: "$userDetails._id",
                                photo: "$photo",
                                name: "$userDetails.name",
                                firstName: "$userDetails.firstName",
                                lastName: "$userDetails.lastName",
                                mobile: "$userDetails.mobile",
                                email: "$userDetails.email",
                                age: "$dob",
                                gender: "$gender",
                                lastVisited: 1,
                                proficiencyLevel: "$activityLevel",
                                userType: "$userType",
                                subUserType: "$subUserType",
                                unpaidSessions: "12",
                                isActive: "$userDetails.isActive",
                                createdAt: "$createdAt",
                                updatedAt: "$updatedAt",
                                missingRequiredPolicies: 1,
                                visitsLast3Months: 1,
                            },
                        },
                    ],
                    total: [{ $count: "total" }],
                },
            },
        ];
    }

    getClientsPipeForTrainer(clientListDto: ClientsListDto, facilityList: Array<string>, search: string, userId: any) {
        const pageSize = clientListDto.pageSize ?? 10;
        const page = clientListDto.page ?? 1;
        const skip = pageSize * (page - 1);
        let facilityMatchObj = {};
        if (clientListDto.facilityId) {
            facilityMatchObj["$expr"] = {
                $in: ["$facilityId", [Types.ObjectId.createFromHexString(clientListDto.facilityId)]],
            };
        } else {
            facilityMatchObj["$expr"] = {
                $in: ["$facilityId", facilityList],
            };
        }
        let userMatchConditions: any = {
            $or: [
                {
                    name: {
                        $regex: `.*${search}.*`,
                        $options: "i",
                    },
                },
                {
                    email: {
                        $regex: `.*${search}.*`,
                        $options: "i",
                    },
                },
                {
                    mobile: {
                        $regex: `.*${search}.*`,
                        $options: "i",
                    },
                },
            ],
        };

        if (clientListDto.isActive === true) {
            userMatchConditions.isActive = true;
        }

        const pipeline: PipelineStage[] = [
            {
                $match: {
                    userId: userId,
                },
            },
            {
                $lookup: {
                    from: "pricings",
                    as: "package",
                    let: {
                        serviceCategory: "$serviceCategory",
                        appointmentType: "$appointmentType",
                    },
                    pipeline: [
                        { $unwind: "$services" },
                        {
                            $unwind: {
                                path: "$services.relationShip",
                                preserveNullAndEmptyArrays: true,
                            },
                        },
                        {
                            $match: {
                                $expr: {
                                    $or: [
                                        {
                                            $in: [
                                                "$$appointmentType",
                                                {
                                                    $cond: {
                                                        if: {
                                                            $isArray: "$services.appointmentType",
                                                        },
                                                        then: "$services.appointmentType",
                                                        else: [],
                                                    },
                                                },
                                            ],
                                        },
                                        {
                                            $in: [
                                                "$$appointmentType",
                                                {
                                                    $cond: {
                                                        if: {
                                                            $isArray: "$services.relationShip.subTypeIds",
                                                        },
                                                        then: "$services.relationShip.subTypeIds",
                                                        else: [],
                                                    },
                                                },
                                            ],
                                        },
                                    ],
                                },
                            },
                        },
                        {
                            $project: {
                                _id: 1,
                            },
                        },
                    ],
                },
            },
            {
                $unwind: "$package",
            },
            {
                $project: {
                    packageId: "$package._id",
                },
            },
            {
                $lookup: {
                    from: "purchases",
                    localField: "packageId",
                    foreignField: "packageId",
                    as: "purchase",
                    pipeline: [
                        {
                            $group: {
                                _id: null,
                                userId: {
                                    $addToSet: "$userId",
                                },
                            },
                        },
                        {
                            $unwind: "$userId",
                        },
                    ],
                },
            },
            {
                $unwind: "$purchase",
            },
            {
                $group: {
                    _id: null,
                    userId: {
                        $addToSet: "$purchase.userId",
                    },
                },
            },
            {
                $unwind: "$userId",
            },
            {
                $lookup: {
                    from: "users",
                    localField: "userId",
                    foreignField: "_id",
                    as: "user",
                    pipeline: [
                        {
                            $match: userMatchConditions,
                        },
                    ],
                },
            },
            {
                $lookup: {
                    from: "clients",
                    localField: "user._id",
                    foreignField: "userId",
                    as: "clientDetails",
                    pipeline: [
                        {
                            $match: {
                                facilityId: { $in: facilityList },
                            },
                        },
                    ],
                },
            },
            {
                $match: {
                    clientDetails: { $ne: [] },
                },
            },
            {
                $replaceRoot: {
                    newRoot: {
                        $arrayElemAt: ["$user", 0],
                    },
                },
            },
            {
                $facet: {
                    total: [
                        {
                            $count: "total",
                        },
                    ],
                    users: [
                        {
                            $sort: {
                                isActive: -1,
                                updatedAt: -1,
                            },
                        },
                        {
                            $skip: skip,
                        },
                        {
                            $limit: pageSize,
                        },
                    ],
                },
            },
            {
                $replaceRoot: {
                    newRoot: {
                        total: {
                            $arrayElemAt: ["$total.total", 0],
                        },
                        users: "$users",
                    },
                },
            },
        ];

        return pipeline;
    }

    clientDetails(clientIds: string[]) {
        const pipeline = [
            {
                $match: {
                    $or: [{ _id: { $in: clientIds.map((id) => new Types.ObjectId(id)) } }, { userId: { $in: clientIds.map((id) => new Types.ObjectId(id)) } }],
                },
            },
            {
                $lookup: {
                    from: "users",
                    localField: "userId",
                    foreignField: "_id",
                    as: "userDetails",
                },
            },
            {
                $match: {
                    userDetails: { $ne: [] },
                },
            },
            {
                $lookup: {
                    from: "clients",
                    let: { parentId: { $arrayElemAt: ["$userDetails.parent", 0] } },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $eq: ["$userId", "$$parentId"],
                                },
                            },
                        },
                        {
                            $project: {
                                sourceId: 1,
                            },
                        },
                    ],
                    as: "parentClient",
                },
            },
            {
                $lookup: {
                    from: "schedulings",
                    localField: "userId",
                    foreignField: "clientId",
                    as: "sessions",
                    pipeline: [
                        {
                            $match: {
                                scheduleStatus: { $in: [ScheduleStatusType.CHECKEDIN, ScheduleStatusType.BOOKED] },
                            },
                        },
                    ],
                },
            },
            {
                $addFields: {
                    totalBookedSessions: { $size: "$sessions" },
                    sourceId: {
                        $cond: {
                            if: { $gt: [{ $size: "$parentClient" }, 0] },
                            then: { $arrayElemAt: ["$parentClient.sourceId", 0] },
                            else: "$sourceId",
                        },
                    },
                },
            },
            {
                $lookup: {
                    from: "schedulings",
                    let: {
                        cId: "$userId",
                        org: "$organizationId",
                        fac: "$facilityId",
                    },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        { $eq: ["$clientId", "$$cId"] },
                                        { $eq: ["$organizationId", "$$org"] }, // ← remove if you want across orgs
                                        { $eq: ["$facilityId", "$$fac"] }, // ← remove if you want across facilities
                                        { $in: ["$scheduleStatus", [ScheduleStatusType.CHECKEDIN, ScheduleStatusType.BOOKED]] },
                                        { $lte: ["$date", "$$NOW"] }, // past only
                                    ],
                                },
                            },
                        },
                        { $sort: { date: -1 } },
                        { $limit: 1 },
                        { $project: { _id: 0, date: 1 } },
                    ],
                    as: "lastSession",
                },
            },
            {
                $addFields: {
                    lastVisited: { $ifNull: [{ $arrayElemAt: ["$lastSession.date", 0] }, null] },
                },
            },

            {
                $lookup: {
                    from: "schedulings",
                    let: {
                        cId: "$userId",
                        org: "$organizationId",
                        fac: "$facilityId",
                        start: { $dateSubtract: { startDate: "$$NOW", unit: "month", amount: 3 } },
                    },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        { $eq: ["$clientId", "$$cId"] },
                                        { $eq: ["$organizationId", "$$org"] }, // ← remove if not needed
                                        { $eq: ["$facilityId", "$$fac"] }, // ← remove if not needed
                                        { $in: ["$scheduleStatus", [ScheduleStatusType.CHECKEDIN]] },
                                        { $gte: ["$date", "$$start"] },
                                        { $lte: ["$date", "$$NOW"] },
                                    ],
                                },
                            },
                        },
                        { $count: "count" },
                    ],
                    as: "visits3m",
                },
            },
            {
                $addFields: {
                    visitsLast3Months: { $ifNull: [{ $arrayElemAt: ["$visits3m.count", 0] }, 0] },
                },
            },

            {
                $project: {
                    firstName: { $arrayElemAt: ["$userDetails.firstName", 0] },
                    lastName: { $arrayElemAt: ["$userDetails.lastName", 0] },
                    mobile: { $arrayElemAt: ["$userDetails.mobile", 0] },
                    email: { $arrayElemAt: ["$userDetails.email", 0] },
                    isActive: { $arrayElemAt: ["$userDetails.isActive", 0] },
                    createdAt: { $arrayElemAt: ["$userDetails.createdAt", 0] },
                    updatedAt: { $arrayElemAt: ["$userDetails.updatedAt", 0] },
                    parent: { $arrayElemAt: ["$userDetails.parent", 0] },
                    countryCode: { $arrayElemAt: ["$userDetails.countryCode", 0] },
                    facilityId: 1,
                    userId: 1,
                    dob: 1,
                    gender: 1,
                    activityLevel: 1,
                    userType: 1,
                    subUserType: 1,
                    address: 1,
                    businessAddress: 1,
                    isBusiness: 1,
                    emergencyContactPerson: 1,
                    emergencyContactPhone: 1,
                    photo: 1,
                    policies: 1,
                    basicAssessments: 1,
                    membershipId: 1,
                    clientId: 1,
                    totalBookedSessions: 1,
                    sourceId: 1,
                    organizationId: 1,
                    lastVisited: 1,
                    visitsLast3Months: 1,
                    notes: 1,
                },
            },
        ];

        return pipeline;
    }

    clientDetailsForApp(clientId: string) {
        let pipeline = [
            {
                $match: {
                    userId: Types.ObjectId.createFromHexString(clientId),
                },
            },
            {
                $lookup: {
                    from: "users",
                    localField: "userId",
                    foreignField: "_id",
                    as: "userDetails",
                    pipeline: [
                        {
                            $match: {
                                isActive: true,
                            },
                        },
                    ],
                },
            },
            {
                $addFields: {
                    isPassword: {
                        $cond: [
                            {
                                $and: [{ $gt: [{ $strLenCP: { $ifNull: [{ $arrayElemAt: ["$userDetails.password", 0] }, ""] } }, 0] }],
                            },
                            true,
                            false,
                        ],
                    },
                    isChild: {
                        $cond: [
                            {
                                $ne: [{ $arrayElemAt: ["$userDetails.parent", 0] }, null],
                            },
                            true,
                            false,
                        ],
                    },
                },
            },
            {
                $match: {
                    userDetails: { $ne: [] },
                },
            },
            {
                $lookup: {
                    from: "states",
                    localField: "address.state",
                    foreignField: "_id",
                    as: "addressStateDetails",
                },
            },
            {
                $unwind: {
                    path: "$addressStateDetails",
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $lookup: {
                    from: "cities",
                    localField: "address.city",
                    foreignField: "_id",
                    as: "addressCityDetails",
                },
            },
            {
                $unwind: {
                    path: "$addressCityDetails",
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $addFields: {
                    "address.stateName": "$addressStateDetails.name",
                    "address.cityName": "$addressCityDetails.name",
                },
            },
            {
                $project: {
                    firstName: { $arrayElemAt: ["$userDetails.firstName", 0] },
                    lastName: { $arrayElemAt: ["$userDetails.lastName", 0] },
                    mobile: { $arrayElemAt: ["$userDetails.mobile", 0] },
                    email: { $arrayElemAt: ["$userDetails.email", 0] },
                    facilityId: 1,
                    dob: 1,
                    gender: 1,
                    activityLevel: 1,
                    address: 1,
                    businessAddress: 1,
                    isBusiness: 1,
                    emergencyContactPerson: 1,
                    emergencyContactPhone: 1,
                    photo: 1,
                    policies: 1,
                    countryCode: { $arrayElemAt: ["$userDetails.countryCode", 0] },
                    isPassword: 1,
                    isChild: 1,
                },
            },
        ] as any;
        return pipeline;
    }
    billingDetails(userId: string, facilityId: string) {
        let pipeline: any = [
            {
                $match: {
                    _id: new Types.ObjectId(userId),
                },
            },
            {
                $lookup: {
                    from: "clients",
                    localField: "_id",
                    foreignField: "userId",
                    as: "clientDetails",
                },
            },
            {
                $unwind: {
                    path: "$clientDetails",
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $lookup: {
                    from: "states",
                    localField: "clientDetails.address.state",
                    foreignField: "_id",
                    as: "clientDetails.address.stateDetails",
                },
            },
            {
                $lookup: {
                    from: "cities",
                    localField: "clientDetails.address.city",
                    foreignField: "_id",
                    as: "clientDetails.address.cityDetails",
                },
            },
            {
                $lookup: {
                    from: "states",
                    localField: "clientDetails.businessAddress.state",
                    foreignField: "_id",
                    as: "clientDetails.businessAddress.stateDetails",
                },
            },
            {
                $lookup: {
                    from: "cities",
                    localField: "clientDetails.businessAddress.city",
                    foreignField: "_id",
                    as: "clientDetails.businessAddress.cityDetails",
                },
            },
            {
                $unwind: {
                    path: "$clientDetails.businessAddress.stateDetails",
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $unwind: {
                    path: "$clientDetails.businessAddress.cityDetails",
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $unwind: {
                    path: "$clientDetails.address.stateDetails",
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $unwind: {
                    path: "$clientDetails.address.cityDetails",
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $lookup: {
                    from: "facilities",
                    let: { facilityId: new Types.ObjectId(facilityId) },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $eq: ["$_id", "$$facilityId"],
                                },
                            },
                        },
                        {
                            $lookup: {
                                from: "states",
                                localField: "billingDetails.state",
                                foreignField: "_id",
                                as: "billingDetails.stateDetails",
                            },
                        },
                        {
                            $lookup: {
                                from: "cities",
                                localField: "billingDetails.city",
                                foreignField: "_id",
                                as: "billingDetails.cityDetails",
                            },
                        },
                        {
                            $unwind: {
                                path: "$billingDetails.stateDetails",
                                preserveNullAndEmptyArrays: true,
                            },
                        },
                        {
                            $unwind: {
                                path: "$billingDetails.cityDetails",
                                preserveNullAndEmptyArrays: true,
                            },
                        },
                    ],
                    as: "facilityDetails",
                },
            },
            {
                $unwind: {
                    path: "$facilityDetails",
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $project: {
                    clientDetails: {
                        _id: "$clientDetails.address._id",
                        userId: "$clientDetails._id",
                        customerId: "$clientDetails.clientId",
                        name: { $concat: ["$firstName", " ", "$lastName"] },
                        email: "$email",
                        phone: "$mobile",
                        addressLine1: "$clientDetails.address.addressLine1",
                        addressLine2: "$clientDetails.address.addressLine2",
                        postalCode: "$clientDetails.address.postalCode",
                        cityId: "$clientDetails.address.city",
                        cityName: "$clientDetails.address.cityDetails.name",
                        stateId: "$clientDetails.address.state",
                        stateName: "$clientDetails.address.stateDetails.name",
                        utCode: "$clientDetails.address.stateDetails.gstCode",
                    },
                    clientBusinessDetails: {
                        _id: "$clientDetails.businessAddress._id",
                        userId: "$clientDetails._id",
                        customerId: "$clientDetails.clientId",
                        name: "$clientDetails.businessAddress.businessName",
                        email: "$email",
                        phone: "$mobile",
                        addressLine1: "$clientDetails.businessAddress.addressLine1",
                        addressLine2: "$clientDetails.businessAddress.addressLine2",
                        postalCode: "$clientDetails.businessAddress.postalCode",
                        cityId: "$clientDetails.businessAddress.city",
                        cityName: "$clientDetails.businessAddress.cityDetails.name",
                        stateId: "$clientDetails.businessAddress.state",
                        stateName: "$clientDetails.businessAddress.stateDetails.name",
                        gstNumber: "$clientDetails.businessAddress.gstNumber",
                        utCode: "$clientDetails.businessAddress.stateDetails.gstCode",
                    },
                    billingDetails: {
                        _id: "$facilityDetails.billingDetails._id",
                        facilityName: "$facilityDetails.facilityName",
                        billingName: "$facilityDetails.billingDetails.billingName",
                        gstNumber: "$facilityDetails.billingDetails.gstNumber",
                        email: "$facilityDetails.email",
                        phone: "$facilityDetails.mobile",
                        addressLine1: "$facilityDetails.billingDetails.addressLine1",
                        addressLine2: "$facilityDetails.billingDetails.addressLine2",
                        postalCode: "$facilityDetails.billingDetails.postalCode",
                        cityId: "$facilityDetails.billingDetails.city",
                        cityName: "$facilityDetails.billingDetails.cityDetails.name",
                        stateId: "$facilityDetails.billingDetails.state",
                        stateName: "$facilityDetails.billingDetails.stateDetails.name",
                        utCode: "$facilityDetails.billingDetails.stateDetails.gstCode",
                    },
                },
            },
        ];
        return pipeline;
    }
    invoiceAllData(invoiceId: string) {
        let pipeline: any = [
            { $match: { _id: new Types.ObjectId(invoiceId) } },
            {
                $lookup: {
                    from: "users",
                    localField: "organizationId",
                    foreignField: "_id",
                    as: "organizationDetails",
                },
            },
            {
                $unwind: {
                    path: "$organizationDetails",
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $unwind: {
                    path: "$purchaseItems",
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $lookup: {
                    from: "pricings",
                    localField: "purchaseItems.packageId",
                    foreignField: "_id",
                    as: "purchaseItems.packageDetails",
                },
            },
            {
                $group: {
                    _id: "$_id",
                    invoiceDate: { $first: "$invoiceDate" },
                    invoiceNumber: { $first: "$invoiceNumber" },
                    organizationDetails: {
                        $first: "$organizationDetails",
                    },
                    clientDetails: { $first: "$clientDetails" },
                    billingDetails: {
                        $first: "$billingDetails",
                    },
                    discount: { $first: "$discount" },
                    subTotal: { $first: "$subTotal" },
                    total: { $first: "$total" },
                    paymentStatus: { $first: "$paymentStatus" },
                    paymentDetails: {
                        $first: "$paymentDetails",
                    },
                    platform: { $first: "$platform" },
                    refundStatus: { $first: "$refundStatus" },
                    refundAmount: { $first: "$refundAmount" },
                    createdAt: { $first: "$createdAt" },
                    updatedAt: { $first: "$updatedAt" },
                    purchaseItems: {
                        $push: {
                            packageId: "$purchaseItems.packageId",
                            quantity: "$purchaseItems.quantity",
                            packageDetails: {
                                $arrayElemAt: ["$purchaseItems.packageDetails", 0],
                            },
                        },
                    },
                },
            },
            {
                $project: {
                    _id: 1,
                    invoiceDate: 1,
                    invoiceNumber: 1,
                    organizationDetails: 1,
                    clientDetails: 1,
                    billingDetails: 1,
                    purchaseItems: 1,
                    discount: 1,
                    subTotal: 1,
                    total: 1,
                    paymentStatus: 1,
                    paymentDetails: 1,
                    platform: 1,
                    refundStatus: 1,
                    refundAmount: 1,
                    createdAt: 1,
                    updatedAt: 1,
                },
            },
        ];
        return pipeline;
    }
}
