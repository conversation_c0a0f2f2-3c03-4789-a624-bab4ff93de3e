<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Client Registration Form</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.4;
            color: #333;
        }
        
        .form-container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .form-row {
            display: flex;
            margin-bottom: 15px;
            border-bottom: 1px solid #e0e0e0;
            padding-bottom: 10px;
        }
        
        .form-label {
            width: 300px;
            font-weight: normal;
            color: #666;
            padding-right: 20px;
            flex-shrink: 0;
        }
        
        .form-value {
            flex: 1;
            color: #333;
        }
        
        .form-value.email {
            color: #4a9eff;
        }
        
        .form-value.phone {
            color: #4a9eff;
        }
        
        .consent-text {
            background-color: #f8f9fa;
            padding: 15px;
            margin-top: 10px;
            border-left: 4px solid #4a9eff;
            font-size: 12px;
            line-height: 1.5;
            border-radius: 4px;
        }
        
     .consent-status {
            font-weight: bold;
            color: #28a745;
            margin-bottom: 10px;
        }
        
        .consent-status-with-checkbox {
            font-weight: bold;
            color: #28a745;
            margin-top: 10px;
            display: flex;
            align-items: center;
        }
         .checkbox-tick {
            width: 16px;
            height: 16px;
            border: 2px solid #131313;
            background-color: #101010;
            border-radius: 3px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 8px;
            color: white;
            font-size: 12px;
            font-weight: bold;
        }
        .section-header {
            font-size: 18px;
            font-weight: bold;
            margin: 30px 0 20px 0;
            color: #333;
            border-bottom: 2px solid #333;
            padding-bottom: 5px;
        }
        
        .signature-box {
            border: 1px solid #ddd;
            height: 60px;
            width: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #fafafa;
            font-style: italic;
        }
        
        .signature-image {
            max-width: 200px;
            max-height: 60px;
            border: 1px solid #ddd;
        }
        
        .minor-section {
            background-color: #f9f9f9;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #4a9eff;
        }
        
        .minor-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        
        .header-title {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            border-bottom: 3px solid #4a9eff;
            padding-bottom: 10px;
        }
        
        @media print {
            body { margin: 0; }
            .form-container { max-width: none; }
            .consent-text { font-size: 10px; }
        }
    </style>
</head>
<body>
    <div class="form-container">
        <h1 class="header-title">Client Waiver Form</h1>
        
        <!-- Basic Information -->
   
        
        <!-- Adult Information -->
        <div class="section-header">Adult Information</div>
        
        <div class="form-row">
            <div class="form-label">Adult's Name</div>
            <div class="form-value">{{firstName}} {{lastName}}</div>
        </div>
        
        <div class="form-row">
            <div class="form-label">Date of Birth</div>
            <div class="form-value">{{dob}}</div>
        </div>
        
        <div class="form-row">
            <div class="form-label">Phone Number</div>
            <div class="form-value phone">{{phone}}</div>
        </div>
        
        <div class="form-row">
            <div class="form-label">Email Address</div>
            <div class="form-value email">{{email}}</div>
        </div>
        
        <div class="form-row">
            <div class="form-label">Address</div>
            <div class="form-value">
                {{#if address}}
                    {{address.street}}
                    {{#if address.state}}, {{address.state}}{{/if}}
                    {{#if address.country}}, {{address.country}}{{/if}}
                    {{#if address.postalCode}}, {{address.postalCode}}{{/if}}
                {{/if}}
            </div>
        </div>
        
        <!-- Minors Information -->
        {{#if minors}}
        {{#if (gt minors.length 0)}}
        <div class="section-header">Sub-Clients Information</div>
        
        {{#each minors}}
        <div class="minor-section">
            <div class="minor-title">{{ordinalNumber @index}} Sub-CLient</div>
            
            <div class="form-row">
                <div class="form-label"> Sub-CLient's Name</div>
                <div class="form-value">{{this.firstName}} {{this.lastName}}</div>
            </div>
            
            <div class="form-row">
                <div class="form-label"> Sub-CLient's Date of Birth</div>
                <div class="form-value">{{this.dob}}</div>
            </div>
            
            <div class="form-row">
                <div class="form-label">Sub-CLient's Gender</div>
                <div class="form-value">{{this.gender}}</div>
            </div>
        </div>
        {{/each}}
        {{/if}}
        {{/if}}
        
        <!-- Agreement Section -->
        <div class="section-header">Agreements & Consent</div>
        
        <div class="form-row">
            <div class="form-label">Age verification</div>
            <div class="form-value">
                
                {{#if rawZohoData.age_consent_text}}
                    <div class="consent-text">
                        {{rawZohoData.age_consent_text}}
                    </div>
                {{/if}}
                   <div class="consent-status-with-checkbox">
                    <span class="checkbox-tick">✓</span>
                    {{#if rawZohoData.age_consent}}
                        {{rawZohoData.age_consent}}
                    {{else}}
                        Agreed
                    {{/if}}
                </div>
            </div>
        </div>
        
        <div class="form-row">
            <div class="form-label">Terms and Conditions</div>
            <div class="form-value">
               
                {{#if rawZohoData.Term_conditon_text}}
                    <div class="consent-text">
                        {{rawZohoData.Term_conditon_text}}
                    </div>
                {{/if}}
                   <div class="consent-status-with-checkbox">
                    <span class="checkbox-tick">✓</span>
                    {{#if rawZohoData.Consent}}
                        {{rawZohoData.Consent}}
                    {{else}}
                        Agreed
                    {{/if}}
                </div>
            </div>
        </div>
        
        <div class="form-row">
            <div class="form-label">Electronic Consent Signature</div>
            <div class="form-value">
               
                {{#if rawZohoData.electronci_consent_text}}
                    <div class="consent-text">
                        {{rawZohoData.electronci_consent_text}}
                    </div>
                {{/if}}
                   <div class="consent-status-with-checkbox">
                    <span class="checkbox-tick">✓</span>
                    {{#if rawZohoData.electronic_consent}}
                        {{rawZohoData.electronic_consent}}
                    {{else}}
                        Agreed
                    {{/if}}
                </div>
            </div>
        </div>
        
        <div class="form-row">
            <div class="form-label">Signature</div>
            <div class="form-value">
                {{#if signature}}
                    <img src="{{signature}}" alt="Digital Signature" class="signature-image" />
                {{else}}
                    <div class="signature-box">[Digital Signature]</div>
                {{/if}}
            </div>
        </div>
        
        <!-- Administrative Information -->
        <div class="section-header">Administrative Information</div>
        
        <div class="form-row">
            <div class="form-label">Submission Time</div>
            <div class="form-value">{{formatDate createdAt}}</div>
        </div>
       
    </div>
</body>
</html>