import { ApiProperty, OmitType } from '@nestjs/swagger';
import { IsMongoId, IsN<PERSON>ber, IsOptional, IsString, Min } from 'class-validator';
import { Expose, Transform } from 'class-transformer';
import { TransformObjectId } from 'src/common/transformers/objectid.transformer';
import { IDatabaseObjectId } from 'src/common/database/interfaces/database.objectid.interface';
import { PaginationListDto } from 'src/common/pagination/dtos/pagination.list.dto';

export class VoucherRedemptionDto {
    @ApiProperty({
        description: 'The voucher ID or voucher code to redeem',
        example: 'GRB4G2U4HFB49NR',
        required: true,
    })
    @IsString()
    voucherIdentifier: string;

    @ApiProperty({
        description: 'Amount to redeem from the voucher',
        example: 50.00,
        required: true,
    })
    @IsNumber()
    @Min(0.01, { message: 'Amount must be greater than 0' })
    amountToRedeem: number;

    @ApiProperty({
        description: 'Optional notes for the redemption',
        example: 'Redeemed for spa service',
        required: false,
    })
    @IsOptional()
    @IsString()
    notes?: string;
}

export class VoucherHistoryListDto extends OmitType(PaginationListDto, ['search'] as const) { }

export class VoucherHistoryResponseDto {
    @ApiProperty({
        description: 'The redemption log ID',
        example: '659d268dee4b6081dacd41fd',
        required: true,
    })
    @Expose()
    @TransformObjectId()
    _id: string;

    @ApiProperty({
        description: 'The purchase ID',
        example: '659d268dee4b6081dacd41fd',
        required: true,
    })
    @Expose()
    @TransformObjectId()
    purchaseId: string;

    @ApiProperty({
        description: 'The customer ID',
        example: '659d268dee4b6081dacd41fd',
        required: true,
    })
    @Expose()
    @TransformObjectId()
    userId: string;

    @ApiProperty({
        description: 'The customer name',
        example: 'John Doe',
        required: true,
    })
    @Expose()
    userName: string;

    @ApiProperty({
        description: 'Amount redeemed',
        example: 50.00,
        required: true,
    })
    @Expose()
    amountRedeemed: number;

    @ApiProperty({
        description: 'Remaining balance after redemption',
        example: 150.00,
        required: true,
    })
    @Expose()
    remainingBalance: number;

    @ApiProperty({
        description: 'Previous balance before redemption',
        example: 200.00,
        required: true,
    })
    @Expose()
    previousBalance: number;

    @ApiProperty({
        description: 'Invoice ID if redemption was part of a transaction',
        example: '659d268dee4b6081dacd41fd',
        required: false,
    })
    @Expose()
    @TransformObjectId()
    invoiceId?: string;

    @ApiProperty({
        description: 'Created by',
        example: '659d268dee4b6081dacd41fd',
        required: true,
    })
    @Expose()
    createdBy: IDatabaseObjectId;

    @ApiProperty({
        description: 'Created by name',
        example: 'John Doe',
        required: true,
    })
    @Expose()
    createdByName: string;

    @ApiProperty({
        description: 'Pricing ID of the voucher',
        example: '659d268dee4b6081dacd41fd',
        required: true,
    })
    @Expose()
    pricingId: IDatabaseObjectId;

    @ApiProperty({
        description: 'Voucher code',
        example: 'GRB4G2U4HFB49NR',
        required: true,
    })
    @Expose()
    voucherCode: string;

    @ApiProperty({
        description: 'Voucher name',
        example: 'Spa Package Voucher',
        required: true,
    })
    @Expose()
    voucherName: string;

    @ApiProperty({
        description: 'Creation timestamp',
        example: '2024-01-15T10:30:00.000Z',
        required: true,
    })
    @Expose()
    createdAt: Date;
}
