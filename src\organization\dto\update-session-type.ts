import { ApiProperty } from "@nestjs/swagger";
import { <PERSON><PERSON><PERSON>y, IsBoolean, IsMongoId, IsNotEmpty, IsNumber, IsString, ValidateNested } from "class-validator";
import { Type } from "class-transformer";

class SessionTypeDto {
    @ApiProperty({
        description: "Unique identifier for the session type",
        example: "personalAppointment",
        required: true,
    })
    @IsString({ message: "id must be a string" })
    @IsNotEmpty({ message: "id is required" })
    id: string;

    @ApiProperty({
        description: "Display name of the session type",
        example: "Appointments",
        required: true,
    })
    @IsString({ message: "name must be a string" })
    @IsNotEmpty({ message: "name is required" })
    name: string;

    @ApiProperty({
        description: "Ordering index of the session type",
        example: 1,
        required: true,
    })
    @IsNumber({}, { message: "orderIndex must be a number" })
    orderIndex: number;
}

export class UpdateSessionTypesDto {
    @ApiProperty({
        description: "Organization Id",
        example: "68d26ea96232d34b6939990",
        required: true,
    })
    @IsMongoId({ message: "Invalid organization id" })
    @IsNotEmpty({ message: "organizationId is required" })
    organizationId: string;

    @ApiProperty({
        description: "Array of session types to update",
        type: [SessionTypeDto],
        example: [
            {
                name: "Appointments",
                id: "personalAppointment",
                orderIndex: 1,
            },
        ],
        required: true,
    })
    @IsArray({ message: "sessionTypes must be an array" })
    @ValidateNested({ each: true })
    @Type(() => SessionTypeDto)
    sessionTypes: SessionTypeDto[];
}
